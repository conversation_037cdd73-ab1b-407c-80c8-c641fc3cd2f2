<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Lee™ - Working Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #0f172a;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Agent <PERSON> Card */
        .agent-card {
            width: 350px;
            height: 600px;
            background: linear-gradient(145deg, #1e293b 0%, #0f172a 100%);
            border-radius: 20px;
            box-shadow: 0 25px 80px rgba(0,0,0,0.8), 0 0 0 2px rgba(59, 130, 246, 0.3);
            border: 1px solid rgba(59, 130, 246, 0.2);
            cursor: move;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }

        /* Minimized State */
        .agent-card.minimized {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            cursor: grab;
        }

        .agent-card.minimized .card-content {
            display: none;
        }

        .agent-card.minimized .minimized-bubble {
            display: flex;
        }

        /* Minimized Bubble */
        .minimized-bubble {
            display: none;
            width: 100%;
            height: 100%;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            border-radius: 50%;
            position: relative;
        }

        .bubble-face {
            font-size: 32px;
            animation: blink 3s infinite;
        }

        @keyframes blink {
            0%, 90%, 100% { transform: scale(1); }
            95% { transform: scale(0.9); }
        }

        /* Card Header */
        .card-header {
            background: linear-gradient(135deg, #1e40af 0%, #3730a3 50%, #374151 100%);
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: grab;
            border-bottom: 1px solid rgba(59, 130, 246, 0.3);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .agent-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 6px 20px rgba(0,0,0,0.4), 0 0 0 2px rgba(59, 130, 246, 0.3);
            border: 2px solid rgba(255,255,255,0.2);
        }

        .agent-face {
            font-size: 20px;
            animation: blink 3s infinite;
        }

        .agent-info {
            color: white;
        }

        .agent-name {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 2px;
        }

        .agent-status {
            font-size: 10px;
            color: rgba(255,255,255,0.8);
        }

        /* Header Controls */
        .header-controls {
            display: flex;
            gap: 6px;
        }

        .control-btn {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            font-size: 10px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .minimize-btn { background: #fbbf24; }
        .close-btn { background: #ef4444; }

        .control-btn:hover {
            transform: scale(1.3);
        }

        /* Card Content */
        .card-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 15px;
            gap: 12px;
            overflow-y: auto;
        }

        /* Chat Section */
        .chat-section {
            flex: 1;
            background: rgba(30, 41, 59, 0.6);
            border-radius: 10px;
            padding: 12px;
            border: 1px solid rgba(59, 130, 246, 0.2);
            display: flex;
            flex-direction: column;
            min-height: 200px;
        }

        .chat-header {
            color: white;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
        }

        .chat-area {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 10px;
            max-height: 150px;
        }

        .message {
            margin-bottom: 8px;
            padding: 6px 8px;
            border-radius: 6px;
            font-size: 11px;
            line-height: 1.3;
        }

        .agent-message {
            background: rgba(59, 130, 246, 0.2);
            color: #e2e8f0;
            border-left: 2px solid #3b82f6;
        }

        .user-message {
            background: rgba(16, 185, 129, 0.2);
            color: #e2e8f0;
            border-left: 2px solid #10b981;
            text-align: right;
        }

        .input-area {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 6px;
            padding: 8px;
            color: white;
            font-size: 11px;
            outline: none;
        }

        .message-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .input-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            font-size: 14px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .voice-btn {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
        }

        .send-btn {
            background: linear-gradient(135deg, #3b82f6, #1e40af);
        }

        .input-btn:hover {
            transform: scale(1.1);
        }

        .input-btn.active {
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* Quick Actions */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
        }

        .action-btn {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 8px;
            padding: 10px 6px;
            color: white;
            font-size: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            text-align: center;
        }

        .action-btn:hover {
            background: rgba(59, 130, 246, 0.3);
            transform: translateY(-1px);
        }

        .action-icon {
            font-size: 16px;
        }

        /* Status Bar */
        .status-bar {
            background: rgba(15, 23, 42, 0.8);
            padding: 6px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 9px;
            color: rgba(255, 255, 255, 0.7);
            border-top: 1px solid rgba(59, 130, 246, 0.2);
        }

        .listening-indicator {
            display: none;
            color: #ef4444;
            animation: pulse 1s infinite;
        }

        .listening-indicator.active {
            display: inline;
        }
    </style>
</head>
<body>
    <!-- Agent Lee Card -->
    <div class="agent-card" id="agentCard">
        <!-- Minimized Bubble -->
        <div class="minimized-bubble" id="minimizedBubble">
            <div class="bubble-face">🤖</div>
        </div>

        <!-- Full Card Content -->
        <div class="card-content" id="cardContent">
            <!-- Header -->
            <div class="card-header" id="cardHeader">
                <div class="header-left">
                    <div class="agent-avatar">
                        <div class="agent-face" id="agentFace">🤖</div>
                    </div>
                    <div class="agent-info">
                        <div class="agent-name">Agent Lee™</div>
                        <div class="agent-status" id="agentStatus">Ready to Chat</div>
                    </div>
                </div>
                
                <div class="header-controls">
                    <button class="control-btn minimize-btn" id="minimizeBtn" title="Minimize">−</button>
                    <button class="control-btn close-btn" id="closeBtn" title="Close">×</button>
                </div>
            </div>

            <!-- Chat Section -->
            <div class="chat-section">
                <div class="chat-header">💬 Chat with Agent Lee</div>
                
                <div class="chat-area" id="chatArea">
                    <div class="message agent-message">
                        Hello! I'm Agent Lee, your AI assistant. I can help you with applications, conversations, and tasks. What would you like me to do?
                    </div>
                </div>
                
                <div class="input-area">
                    <input type="text" class="message-input" id="messageInput" placeholder="Type your message to Agent Lee...">
                    <button class="input-btn voice-btn" id="voiceBtn" title="Voice Input">🎤</button>
                    <button class="input-btn send-btn" id="sendBtn" title="Send Message">➤</button>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <button class="action-btn" id="appsBtn">
                    <span class="action-icon">📱</span>
                    <span>Open Apps</span>
                </button>
                <button class="action-btn" id="webBtn">
                    <span class="action-icon">🌐</span>
                    <span>Web Search</span>
                </button>
                <button class="action-btn" id="helpBtn">
                    <span class="action-icon">❓</span>
                    <span>Help</span>
                </button>
            </div>

            <!-- Status Bar -->
            <div class="status-bar">
                <span id="connectionStatus">🟢 Connected</span>
                <span class="listening-indicator" id="listeningIndicator">🎤 Listening</span>
                <span id="timeStatus"></span>
            </div>
        </div>
    </div>

    <script>
        // Agent Lee Working Implementation
        class AgentLeeWorking {
            constructor() {
                this.isMinimized = false;
                this.isListening = false;
                this.conversationHistory = [];
                this.apiUrl = 'http://localhost:8000';
                
                this.initialize();
            }

            initialize() {
                this.initializeElements();
                this.initializeEventListeners();
                this.initializeDragFunctionality();
                this.startStatusUpdates();
                this.greetUser();
            }

            initializeElements() {
                this.elements = {
                    card: document.getElementById('agentCard'),
                    minimizedBubble: document.getElementById('minimizedBubble'),
                    cardContent: document.getElementById('cardContent'),
                    header: document.getElementById('cardHeader'),
                    face: document.getElementById('agentFace'),
                    status: document.getElementById('agentStatus'),
                    chat: document.getElementById('chatArea'),
                    input: document.getElementById('messageInput'),
                    sendBtn: document.getElementById('sendBtn'),
                    voiceBtn: document.getElementById('voiceBtn'),
                    minimizeBtn: document.getElementById('minimizeBtn'),
                    closeBtn: document.getElementById('closeBtn'),
                    connectionStatus: document.getElementById('connectionStatus'),
                    timeStatus: document.getElementById('timeStatus'),
                    listeningIndicator: document.getElementById('listeningIndicator')
                };
            }

            initializeEventListeners() {
                // Card controls
                this.elements.minimizeBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.toggleMinimize();
                });
                
                this.elements.closeBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.closeWidget();
                });

                // Chat controls
                this.elements.sendBtn.addEventListener('click', () => this.sendMessage());
                this.elements.input.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') this.sendMessage();
                });
                this.elements.voiceBtn.addEventListener('click', () => this.toggleVoice());

                // Quick actions
                document.getElementById('appsBtn').addEventListener('click', () => this.handleApps());
                document.getElementById('webBtn').addEventListener('click', () => this.handleWeb());
                document.getElementById('helpBtn').addEventListener('click', () => this.handleHelp());

                // Restore from minimized
                this.elements.minimizedBubble.addEventListener('dblclick', () => this.toggleMinimize());
            }

            initializeDragFunctionality() {
                let dragTarget = null;
                let startX = 0;
                let startY = 0;
                let startLeft = 0;
                let startTop = 0;

                const startDrag = (element, handle, e) => {
                    dragTarget = element;
                    startX = e.clientX;
                    startY = e.clientY;
                    const rect = element.getBoundingClientRect();
                    startLeft = rect.left;
                    startTop = rect.top;
                    element.style.cursor = 'grabbing';
                    e.preventDefault();
                };

                // Main card dragging
                this.elements.header.addEventListener('mousedown', (e) => {
                    startDrag(this.elements.card, this.elements.header, e);
                });

                // Minimized bubble dragging
                this.elements.minimizedBubble.addEventListener('mousedown', (e) => {
                    startDrag(this.elements.card, this.elements.minimizedBubble, e);
                });

                document.addEventListener('mousemove', (e) => {
                    if (dragTarget) {
                        const deltaX = e.clientX - startX;
                        const deltaY = e.clientY - startY;
                        const newLeft = startLeft + deltaX;
                        const newTop = startTop + deltaY;

                        // Keep within screen bounds
                        const maxLeft = window.innerWidth - dragTarget.offsetWidth;
                        const maxTop = window.innerHeight - dragTarget.offsetHeight;

                        dragTarget.style.left = Math.max(0, Math.min(newLeft, maxLeft)) + 'px';
                        dragTarget.style.top = Math.max(0, Math.min(newTop, maxTop)) + 'px';
                        dragTarget.style.position = 'fixed';
                    }
                });

                document.addEventListener('mouseup', () => {
                    if (dragTarget) {
                        dragTarget.style.cursor = this.isMinimized ? 'grab' : 'move';
                        dragTarget = null;
                    }
                });
            }

            toggleMinimize() {
                this.isMinimized = !this.isMinimized;
                this.elements.card.classList.toggle('minimized', this.isMinimized);
                
                if (this.isMinimized) {
                    this.updateStatus('Minimized');
                    this.elements.card.style.cursor = 'grab';
                } else {
                    this.updateStatus('Ready to Chat');
                    this.elements.card.style.cursor = 'move';
                }
            }

            async sendMessage() {
                const message = this.elements.input.value.trim();
                if (!message) return;

                this.addMessage(message, 'user');
                this.elements.input.value = '';

                try {
                    const response = await this.sendToBackend(message);
                    this.addMessage(response.message, 'agent');
                    
                    if (response.speak) {
                        this.speak(response.message);
                    }

                    if (response.action) {
                        await this.handleAction(response.action);
                    }
                } catch (error) {
                    this.addMessage("I'm having trouble connecting. Please try again.", 'agent');
                    console.error('Error:', error);
                }
            }

            async sendToBackend(message) {
                try {
                    const response = await fetch(`${this.apiUrl}/api/chat`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ message, history: this.conversationHistory })
                    });

                    if (response.ok) {
                        return await response.json();
                    }
                } catch (error) {
                    console.error('Backend error:', error);
                }

                // Fallback response
                return {
                    message: this.generateResponse(message),
                    speak: true,
                    action: this.detectAction(message)
                };
            }

            generateResponse(message) {
                const lower = message.toLowerCase();
                
                if (lower.includes('hello') || lower.includes('hi')) {
                    return "Hello! I'm Agent Lee, your AI assistant. I can help you with applications, conversations, and tasks!";
                } else if (lower.includes('open') || lower.includes('launch')) {
                    return "I can help you open applications! Tell me which app you'd like to open, like Chrome, Notepad, or Calculator.";
                } else if (lower.includes('help')) {
                    return "I can help you with: opening applications, web searches, conversations, and various tasks. What would you like me to help you with?";
                } else if (lower.includes('weather')) {
                    return "I can help you check the weather! What location would you like weather information for?";
                } else {
                    return "I understand! I'm Agent Lee, your AI assistant. I can help with applications, conversations, and tasks. What would you like me to do?";
                }
            }

            detectAction(message) {
                const lower = message.toLowerCase();
                
                if (lower.includes('open chrome')) {
                    return { type: 'open_app', app: 'chrome' };
                } else if (lower.includes('open notepad')) {
                    return { type: 'open_app', app: 'notepad' };
                } else if (lower.includes('open calculator')) {
                    return { type: 'open_app', app: 'calculator' };
                }
                
                return null;
            }

            async handleAction(action) {
                switch (action.type) {
                    case 'open_app':
                        await this.openApplication(action.app);
                        break;
                }
            }

            async openApplication(appName) {
                try {
                    const response = await fetch(`${this.apiUrl}/api/open_app`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ app: appName })
                    });

                    const result = await response.json();
                    
                    if (result.status === 'success') {
                        this.addMessage(`✅ ${appName} opened successfully!`, 'agent');
                        this.speak(`${appName} is now open`);
                    } else {
                        this.addMessage(`❌ Could not open ${appName}: ${result.message}`, 'agent');
                    }
                } catch (error) {
                    this.addMessage(`❌ Error opening ${appName}: ${error.message}`, 'agent');
                }
            }

            toggleVoice() {
                if (this.isListening) {
                    this.stopListening();
                } else {
                    this.startListening();
                }
            }

            startListening() {
                this.isListening = true;
                this.elements.voiceBtn.classList.add('active');
                this.elements.listeningIndicator.classList.add('active');
                this.updateStatus('Listening...');

                if ('webkitSpeechRecognition' in window) {
                    const recognition = new webkitSpeechRecognition();
                    recognition.continuous = false;
                    recognition.interimResults = false;
                    recognition.lang = 'en-US';

                    recognition.onresult = (event) => {
                        const transcript = event.results[0][0].transcript;
                        this.elements.input.value = transcript;
                        this.sendMessage();
                    };

                    recognition.onerror = () => {
                        this.stopListening();
                        this.addMessage("Sorry, I couldn't hear you clearly.", 'agent');
                    };

                    recognition.onend = () => {
                        this.stopListening();
                    };

                    recognition.start();
                } else {
                    this.stopListening();
                    this.addMessage("Voice recognition not supported in this browser.", 'agent');
                }
            }

            stopListening() {
                this.isListening = false;
                this.elements.voiceBtn.classList.remove('active');
                this.elements.listeningIndicator.classList.remove('active');
                this.updateStatus('Ready to Chat');
            }

            async speak(text) {
                try {
                    await fetch(`${this.apiUrl}/api/speak`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text })
                    });
                } catch (error) {
                    // Fallback to browser speech
                    if ('speechSynthesis' in window) {
                        const utterance = new SpeechSynthesisUtterance(text);
                        utterance.rate = 0.9;
                        speechSynthesis.speak(utterance);
                    }
                }
            }

            addMessage(text, sender) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}-message`;
                messageDiv.textContent = text;
                
                this.elements.chat.appendChild(messageDiv);
                this.elements.chat.scrollTop = this.elements.chat.scrollHeight;

                this.conversationHistory.push({ sender, text, timestamp: Date.now() });
                
                if (this.conversationHistory.length > 20) {
                    this.conversationHistory = this.conversationHistory.slice(-20);
                }
            }

            startStatusUpdates() {
                // Update time every second
                setInterval(() => {
                    this.elements.timeStatus.textContent = new Date().toLocaleTimeString();
                }, 1000);
            }

            async greetUser() {
                await new Promise(resolve => setTimeout(resolve, 1000));
                this.speak("Hello! I'm Agent Lee, your AI assistant. I'm ready to help you with applications, conversations, and tasks!");
                this.updateStatus("Ready to Chat");
            }

            updateStatus(status) {
                this.elements.status.textContent = status;
            }

            closeWidget() {
                if (confirm('Close Agent Lee?')) {
                    this.elements.card.style.display = 'none';
                }
            }

            // Quick action handlers
            handleApps() {
                this.elements.input.value = "Show me available applications";
                this.sendMessage();
            }

            handleWeb() {
                this.elements.input.value = "Help me search the web";
                this.sendMessage();
            }

            handleHelp() {
                this.elements.input.value = "What can you help me with?";
                this.sendMessage();
            }
        }

        // Initialize Agent Lee when page loads
        document.addEventListener('DOMContentLoaded', () => {
            window.agentLee = new AgentLeeWorking();
            console.log('🤖 Agent Lee Working Interface initialized!');
        });
    </script>
</body>
</html>
