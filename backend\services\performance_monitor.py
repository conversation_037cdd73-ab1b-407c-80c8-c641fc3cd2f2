"""
Agent Lee™ Performance Monitor
Real-time system performance monitoring and optimization
"""

import asyncio
import time
import psutil
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import deque
import json
import threading

logger = logging.getLogger("agentlee.performance")

@dataclass
class PerformanceMetric:
    """Individual performance metric"""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_sent_mb: float
    network_recv_mb: float
    active_connections: int
    response_time_ms: float
    request_count: int
    error_count: int
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class SystemAlert:
    """System performance alert"""
    alert_id: str
    alert_type: str
    severity: str  # low, medium, high, critical
    message: str
    timestamp: float
    metric_value: float
    threshold: float
    resolved: bool = False
    resolved_at: Optional[float] = None

class PerformanceMonitor:
    """Comprehensive performance monitoring system"""
    
    def __init__(self):
        self.is_monitoring = False
        self.metrics_history: deque = deque(maxlen=1000)  # Keep last 1000 metrics
        self.alerts: List[SystemAlert] = []
        self.alert_thresholds = {
            "cpu_high": 80.0,
            "cpu_critical": 95.0,
            "memory_high": 80.0,
            "memory_critical": 95.0,
            "disk_high": 85.0,
            "disk_critical": 95.0,
            "response_time_high": 1000.0,  # ms
            "response_time_critical": 5000.0,  # ms
            "error_rate_high": 5.0,  # %
            "error_rate_critical": 10.0  # %
        }
        
        # Performance counters
        self.request_counter = 0
        self.error_counter = 0
        self.response_times: deque = deque(maxlen=100)
        
        # System baseline
        self.baseline_metrics: Optional[PerformanceMetric] = None
        self.monitoring_interval = 5.0  # seconds
        
        # Network and disk I/O tracking
        self.last_disk_io = None
        self.last_network_io = None
        
        logger.info("Performance Monitor initialized")
    
    async def start_monitoring(self):
        """Start performance monitoring"""
        if self.is_monitoring:
            logger.warning("Performance monitoring already running")
            return
        
        self.is_monitoring = True
        logger.info("🔍 Starting performance monitoring...")
        
        # Initialize baseline
        await self._establish_baseline()
        
        # Start monitoring loop
        asyncio.create_task(self._monitoring_loop())
        
        # Start alert processing
        asyncio.create_task(self._alert_processing_loop())
        
        logger.info("✅ Performance monitoring started")
    
    async def stop_monitoring(self):
        """Stop performance monitoring"""
        self.is_monitoring = False
        logger.info("🛑 Performance monitoring stopped")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                # Collect metrics
                metric = await self._collect_metrics()
                
                # Store metric
                self.metrics_history.append(metric)
                
                # Check for alerts
                await self._check_alerts(metric)
                
                # Sleep until next collection
                await asyncio.sleep(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(self.monitoring_interval)
    
    async def _collect_metrics(self) -> PerformanceMetric:
        """Collect current system metrics"""
        current_time = time.time()
        
        # CPU metrics
        cpu_percent = psutil.cpu_percent(interval=0.1)
        
        # Memory metrics
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_used_mb = memory.used / (1024 * 1024)
        
        # Disk I/O metrics
        disk_io = psutil.disk_io_counters()
        if self.last_disk_io:
            disk_read_mb = (disk_io.read_bytes - self.last_disk_io.read_bytes) / (1024 * 1024)
            disk_write_mb = (disk_io.write_bytes - self.last_disk_io.write_bytes) / (1024 * 1024)
        else:
            disk_read_mb = 0
            disk_write_mb = 0
        self.last_disk_io = disk_io
        
        # Network I/O metrics
        network_io = psutil.net_io_counters()
        if self.last_network_io:
            network_sent_mb = (network_io.bytes_sent - self.last_network_io.bytes_sent) / (1024 * 1024)
            network_recv_mb = (network_io.bytes_recv - self.last_network_io.bytes_recv) / (1024 * 1024)
        else:
            network_sent_mb = 0
            network_recv_mb = 0
        self.last_network_io = network_io
        
        # Network connections
        try:
            connections = len(psutil.net_connections())
        except (psutil.AccessDenied, psutil.NoSuchProcess):
            connections = 0
        
        # Application metrics
        avg_response_time = sum(self.response_times) / len(self.response_times) if self.response_times else 0
        
        metric = PerformanceMetric(
            timestamp=current_time,
            cpu_percent=cpu_percent,
            memory_percent=memory_percent,
            memory_used_mb=memory_used_mb,
            disk_io_read_mb=disk_read_mb,
            disk_io_write_mb=disk_write_mb,
            network_sent_mb=network_sent_mb,
            network_recv_mb=network_recv_mb,
            active_connections=connections,
            response_time_ms=avg_response_time,
            request_count=self.request_counter,
            error_count=self.error_counter
        )
        
        return metric
    
    async def _establish_baseline(self):
        """Establish performance baseline"""
        logger.info("📊 Establishing performance baseline...")
        
        # Collect several samples
        samples = []
        for _ in range(5):
            metric = await self._collect_metrics()
            samples.append(metric)
            await asyncio.sleep(1)
        
        # Calculate baseline averages
        if samples:
            self.baseline_metrics = PerformanceMetric(
                timestamp=time.time(),
                cpu_percent=sum(s.cpu_percent for s in samples) / len(samples),
                memory_percent=sum(s.memory_percent for s in samples) / len(samples),
                memory_used_mb=sum(s.memory_used_mb for s in samples) / len(samples),
                disk_io_read_mb=0,  # Baseline for I/O is 0
                disk_io_write_mb=0,
                network_sent_mb=0,
                network_recv_mb=0,
                active_connections=int(sum(s.active_connections for s in samples) / len(samples)),
                response_time_ms=0,
                request_count=0,
                error_count=0
            )
            
            logger.info(f"📊 Baseline established: CPU {self.baseline_metrics.cpu_percent:.1f}%, "
                       f"Memory {self.baseline_metrics.memory_percent:.1f}%")
    
    async def _check_alerts(self, metric: PerformanceMetric):
        """Check for performance alerts"""
        alerts_triggered = []
        
        # CPU alerts
        if metric.cpu_percent >= self.alert_thresholds["cpu_critical"]:
            alerts_triggered.append(("cpu_critical", metric.cpu_percent, "CPU usage critical"))
        elif metric.cpu_percent >= self.alert_thresholds["cpu_high"]:
            alerts_triggered.append(("cpu_high", metric.cpu_percent, "CPU usage high"))
        
        # Memory alerts
        if metric.memory_percent >= self.alert_thresholds["memory_critical"]:
            alerts_triggered.append(("memory_critical", metric.memory_percent, "Memory usage critical"))
        elif metric.memory_percent >= self.alert_thresholds["memory_high"]:
            alerts_triggered.append(("memory_high", metric.memory_percent, "Memory usage high"))
        
        # Response time alerts
        if metric.response_time_ms >= self.alert_thresholds["response_time_critical"]:
            alerts_triggered.append(("response_time_critical", metric.response_time_ms, "Response time critical"))
        elif metric.response_time_ms >= self.alert_thresholds["response_time_high"]:
            alerts_triggered.append(("response_time_high", metric.response_time_ms, "Response time high"))
        
        # Error rate alerts
        if self.request_counter > 0:
            error_rate = (self.error_counter / self.request_counter) * 100
            if error_rate >= self.alert_thresholds["error_rate_critical"]:
                alerts_triggered.append(("error_rate_critical", error_rate, "Error rate critical"))
            elif error_rate >= self.alert_thresholds["error_rate_high"]:
                alerts_triggered.append(("error_rate_high", error_rate, "Error rate high"))
        
        # Create alerts
        for alert_type, value, message in alerts_triggered:
            await self._create_alert(alert_type, value, message)
    
    async def _create_alert(self, alert_type: str, value: float, message: str):
        """Create a new alert"""
        # Check if similar alert already exists and is not resolved
        existing_alert = None
        for alert in self.alerts:
            if alert.alert_type == alert_type and not alert.resolved:
                existing_alert = alert
                break
        
        if existing_alert:
            # Update existing alert
            existing_alert.timestamp = time.time()
            existing_alert.metric_value = value
        else:
            # Create new alert
            severity = "critical" if "critical" in alert_type else "high" if "high" in alert_type else "medium"
            
            alert = SystemAlert(
                alert_id=f"{alert_type}_{int(time.time())}",
                alert_type=alert_type,
                severity=severity,
                message=f"{message}: {value:.2f}",
                timestamp=time.time(),
                metric_value=value,
                threshold=self.alert_thresholds.get(alert_type, 0)
            )
            
            self.alerts.append(alert)
            logger.warning(f"🚨 Performance Alert: {alert.message}")
    
    async def _alert_processing_loop(self):
        """Process and resolve alerts"""
        while self.is_monitoring:
            try:
                current_time = time.time()
                
                # Check for alerts to resolve
                for alert in self.alerts:
                    if not alert.resolved and self._should_resolve_alert(alert):
                        alert.resolved = True
                        alert.resolved_at = current_time
                        logger.info(f"✅ Alert resolved: {alert.alert_type}")
                
                # Clean up old resolved alerts (keep last 100)
                resolved_alerts = [a for a in self.alerts if a.resolved]
                if len(resolved_alerts) > 100:
                    # Remove oldest resolved alerts
                    resolved_alerts.sort(key=lambda x: x.resolved_at or 0)
                    alerts_to_remove = resolved_alerts[:-100]
                    for alert in alerts_to_remove:
                        self.alerts.remove(alert)
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in alert processing loop: {e}")
                await asyncio.sleep(30)
    
    def _should_resolve_alert(self, alert: SystemAlert) -> bool:
        """Check if an alert should be resolved"""
        if not self.metrics_history:
            return False
        
        # Get recent metrics (last 3 samples)
        recent_metrics = list(self.metrics_history)[-3:]
        
        if alert.alert_type.startswith("cpu"):
            return all(m.cpu_percent < alert.threshold * 0.8 for m in recent_metrics)
        elif alert.alert_type.startswith("memory"):
            return all(m.memory_percent < alert.threshold * 0.8 for m in recent_metrics)
        elif alert.alert_type.startswith("response_time"):
            return all(m.response_time_ms < alert.threshold * 0.8 for m in recent_metrics)
        elif alert.alert_type.startswith("error_rate"):
            if self.request_counter > 0:
                current_error_rate = (self.error_counter / self.request_counter) * 100
                return current_error_rate < alert.threshold * 0.8
        
        return False
    
    def record_request(self, response_time_ms: float, is_error: bool = False):
        """Record a request for performance tracking"""
        self.request_counter += 1
        self.response_times.append(response_time_ms)
        
        if is_error:
            self.error_counter += 1
    
    def get_current_metrics(self) -> Optional[Dict[str, Any]]:
        """Get current performance metrics"""
        if not self.metrics_history:
            return None
        
        latest_metric = self.metrics_history[-1]
        return latest_metric.to_dict()
    
    def get_metrics_history(self, minutes: int = 60) -> List[Dict[str, Any]]:
        """Get metrics history for specified time period"""
        cutoff_time = time.time() - (minutes * 60)
        
        filtered_metrics = [
            metric.to_dict() for metric in self.metrics_history
            if metric.timestamp >= cutoff_time
        ]
        
        return filtered_metrics
    
    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """Get active (unresolved) alerts"""
        active_alerts = [
            {
                "alert_id": alert.alert_id,
                "alert_type": alert.alert_type,
                "severity": alert.severity,
                "message": alert.message,
                "timestamp": alert.timestamp,
                "metric_value": alert.metric_value,
                "threshold": alert.threshold
            }
            for alert in self.alerts
            if not alert.resolved
        ]
        
        return active_alerts
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary"""
        if not self.metrics_history:
            return {"status": "no_data"}
        
        recent_metrics = list(self.metrics_history)[-10:]  # Last 10 samples
        
        avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
        avg_memory = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics)
        avg_response_time = sum(self.response_times) / len(self.response_times) if self.response_times else 0
        
        error_rate = (self.error_counter / self.request_counter * 100) if self.request_counter > 0 else 0
        
        # Determine overall health
        health_score = 100
        if avg_cpu > 80:
            health_score -= 20
        elif avg_cpu > 60:
            health_score -= 10
        
        if avg_memory > 80:
            health_score -= 20
        elif avg_memory > 60:
            health_score -= 10
        
        if avg_response_time > 1000:
            health_score -= 15
        elif avg_response_time > 500:
            health_score -= 5
        
        if error_rate > 5:
            health_score -= 15
        elif error_rate > 2:
            health_score -= 5
        
        health_score = max(0, health_score)
        
        return {
            "health_score": health_score,
            "avg_cpu_percent": round(avg_cpu, 2),
            "avg_memory_percent": round(avg_memory, 2),
            "avg_response_time_ms": round(avg_response_time, 2),
            "error_rate_percent": round(error_rate, 2),
            "total_requests": self.request_counter,
            "total_errors": self.error_counter,
            "active_alerts": len([a for a in self.alerts if not a.resolved]),
            "monitoring_duration_hours": (time.time() - self.metrics_history[0].timestamp) / 3600 if self.metrics_history else 0
        }
    
    def update_thresholds(self, new_thresholds: Dict[str, float]):
        """Update alert thresholds"""
        self.alert_thresholds.update(new_thresholds)
        logger.info(f"Alert thresholds updated: {new_thresholds}")

# Global performance monitor instance
performance_monitor = PerformanceMonitor()
