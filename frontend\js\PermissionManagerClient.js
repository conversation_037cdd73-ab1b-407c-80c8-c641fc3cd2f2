/**
 * Agent Lee™ Permission Manager Client
 * Handles the UI for requesting user permission to launch and mirror specific applications
 */

class PermissionManagerClient {
    constructor() {
        this.permissions = new Map();
        this.permissionSettings = {
            defaultMode: 'ask_every_time', // 'allow_all', 'deny_all', 'ask_every_time'
            rememberChoices: true,
            autoCloseDelay: 30000 // 30 seconds
        };
        this.initialized = false;
    }

    async initialize() {
        console.log('🔐 Initializing Permission Manager Client...');
        
        try {
            // Load saved permissions
            await this.loadSavedPermissions();
            
            // Set up permission UI
            this.setupPermissionUI();
            
            this.initialized = true;
            console.log('✅ Permission Manager Client initialized successfully');
            
        } catch (error) {
            console.error('❌ Permission Manager Client initialization failed:', error);
            throw error;
        }
    }

    async loadSavedPermissions() {
        try {
            const savedPermissions = localStorage.getItem('agentlee_echo_permissions');
            if (savedPermissions) {
                const permissionsData = JSON.parse(savedPermissions);
                this.permissions = new Map(permissionsData.permissions || []);
                this.permissionSettings = { ...this.permissionSettings, ...permissionsData.settings };
            }
            
            console.log(`🔐 Loaded ${this.permissions.size} saved permissions`);
            
        } catch (error) {
            console.error('❌ Failed to load saved permissions:', error);
        }
    }

    async savePermissions() {
        try {
            const permissionsData = {
                permissions: Array.from(this.permissions.entries()),
                settings: this.permissionSettings,
                lastUpdated: Date.now()
            };
            
            localStorage.setItem('agentlee_echo_permissions', JSON.stringify(permissionsData));
            console.log('💾 Permissions saved successfully');
            
        } catch (error) {
            console.error('❌ Failed to save permissions:', error);
        }
    }

    setupPermissionUI() {
        // Add permission dialog styles if not already added
        if (!document.getElementById('permission-dialog-styles')) {
            this.addPermissionStyles();
        }
    }

    addPermissionStyles() {
        const styleSheet = document.createElement('style');
        styleSheet.id = 'permission-dialog-styles';
        styleSheet.textContent = `
            .permission-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.7);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
                animation: fadeIn 0.3s ease;
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            .permission-dialog {
                background: linear-gradient(145deg, #1e293b 0%, #0f172a 100%);
                border-radius: 20px;
                box-shadow: 0 25px 80px rgba(0,0,0,0.8), 0 0 0 2px rgba(59, 130, 246, 0.3);
                border: 1px solid rgba(59, 130, 246, 0.2);
                max-width: 450px;
                width: 90%;
                color: white;
                animation: slideUp 0.3s ease;
            }

            @keyframes slideUp {
                from { transform: translateY(50px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }

            .permission-header {
                background: linear-gradient(135deg, #1e40af 0%, #3730a3 50%, #374151 100%);
                padding: 20px;
                border-radius: 20px 20px 0 0;
                text-align: center;
                border-bottom: 1px solid rgba(59, 130, 246, 0.3);
            }

            .permission-icon {
                font-size: 40px;
                margin-bottom: 10px;
            }

            .permission-title {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 5px;
            }

            .permission-subtitle {
                font-size: 12px;
                opacity: 0.8;
            }

            .permission-content {
                padding: 25px;
            }

            .permission-app-info {
                display: flex;
                align-items: center;
                gap: 15px;
                margin-bottom: 20px;
                padding: 15px;
                background: rgba(30, 41, 59, 0.6);
                border-radius: 12px;
                border: 1px solid rgba(59, 130, 246, 0.2);
            }

            .permission-app-icon {
                font-size: 32px;
                width: 50px;
                height: 50px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: rgba(59, 130, 246, 0.2);
                border-radius: 12px;
            }

            .permission-app-details {
                flex: 1;
            }

            .permission-app-name {
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 4px;
            }

            .permission-app-device {
                font-size: 12px;
                opacity: 0.7;
            }

            .permission-message {
                margin-bottom: 20px;
                line-height: 1.5;
                font-size: 14px;
            }

            .permission-warning {
                background: rgba(245, 158, 11, 0.1);
                border: 1px solid rgba(245, 158, 11, 0.3);
                border-radius: 8px;
                padding: 12px;
                margin-bottom: 20px;
                font-size: 12px;
                color: #fbbf24;
            }

            .permission-options {
                margin-bottom: 20px;
            }

            .permission-option {
                display: flex;
                align-items: center;
                gap: 10px;
                margin-bottom: 10px;
                padding: 8px;
                border-radius: 8px;
                cursor: pointer;
                transition: background 0.2s ease;
            }

            .permission-option:hover {
                background: rgba(59, 130, 246, 0.1);
            }

            .permission-option input[type="radio"] {
                margin: 0;
            }

            .permission-option label {
                cursor: pointer;
                font-size: 14px;
            }

            .permission-buttons {
                display: flex;
                gap: 12px;
                justify-content: flex-end;
            }

            .permission-btn {
                padding: 12px 24px;
                border-radius: 8px;
                border: none;
                cursor: pointer;
                font-size: 14px;
                font-weight: 600;
                transition: all 0.3s ease;
                min-width: 80px;
            }

            .permission-btn-deny {
                background: rgba(239, 68, 68, 0.2);
                color: #fca5a5;
                border: 1px solid rgba(239, 68, 68, 0.3);
            }

            .permission-btn-deny:hover {
                background: rgba(239, 68, 68, 0.3);
            }

            .permission-btn-allow {
                background: linear-gradient(135deg, #3b82f6, #1e40af);
                color: white;
            }

            .permission-btn-allow:hover {
                background: linear-gradient(135deg, #2563eb, #1d4ed8);
                transform: translateY(-1px);
            }

            .permission-countdown {
                text-align: center;
                margin-top: 15px;
                font-size: 12px;
                opacity: 0.7;
            }
        `;

        document.head.appendChild(styleSheet);
    }

    /**
     * Request permission to create an Echo for a specific application
     * @param {Object} app - Application details
     * @param {string} deviceType - Device type (computer, phone, tablet, laptop)
     * @returns {Promise<boolean>} - Whether permission was granted
     */
    async requestEchoPermission(app, deviceType) {
        const permissionKey = `${deviceType}_${app.id}`;
        
        console.log(`🔐 Requesting Echo permission for ${app.name} on ${deviceType}`);

        // Check if permission already exists
        if (this.permissions.has(permissionKey)) {
            const existingPermission = this.permissions.get(permissionKey);
            
            if (existingPermission.choice === 'allow') {
                console.log('✅ Permission already granted');
                return true;
            } else if (existingPermission.choice === 'deny') {
                console.log('❌ Permission already denied');
                return false;
            }
        }

        // Check global settings
        if (this.permissionSettings.defaultMode === 'allow_all') {
            console.log('✅ Permission granted by global setting');
            return true;
        } else if (this.permissionSettings.defaultMode === 'deny_all') {
            console.log('❌ Permission denied by global setting');
            return false;
        }

        // Show permission dialog
        return await this.showPermissionDialog(app, deviceType, permissionKey);
    }

    async showPermissionDialog(app, deviceType, permissionKey) {
        return new Promise((resolve) => {
            const overlay = this.createPermissionDialog(app, deviceType, permissionKey, resolve);
            document.body.appendChild(overlay);

            // Auto-close after timeout
            const timeout = setTimeout(() => {
                if (overlay.parentElement) {
                    overlay.remove();
                    resolve(false); // Default to deny
                }
            }, this.permissionSettings.autoCloseDelay);

            // Clear timeout if dialog is manually closed
            overlay.addEventListener('remove', () => {
                clearTimeout(timeout);
            });
        });
    }

    createPermissionDialog(app, deviceType, permissionKey, resolve) {
        const overlay = document.createElement('div');
        overlay.className = 'permission-overlay';
        
        const deviceIcons = {
            computer: '🖥️',
            phone: '📱',
            tablet: '📱',
            laptop: '💻'
        };

        const deviceNames = {
            computer: 'Computer',
            phone: 'Phone',
            tablet: 'Tablet',
            laptop: 'Laptop'
        };

        overlay.innerHTML = `
            <div class="permission-dialog">
                <div class="permission-header">
                    <div class="permission-icon">🔄</div>
                    <div class="permission-title">Echo Permission Request</div>
                    <div class="permission-subtitle">Agent Lee™ Echo Engine</div>
                </div>
                
                <div class="permission-content">
                    <div class="permission-app-info">
                        <div class="permission-app-icon">${app.icon}</div>
                        <div class="permission-app-details">
                            <div class="permission-app-name">${app.name}</div>
                            <div class="permission-app-device">${deviceIcons[deviceType]} ${deviceNames[deviceType]}</div>
                        </div>
                    </div>
                    
                    <div class="permission-message">
                        Agent Lee would like to create an <strong>Echo</strong> of <strong>${app.name}</strong> from your ${deviceNames[deviceType].toLowerCase()}.
                        <br><br>
                        This will:
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li>Launch ${app.name} on your ${deviceNames[deviceType].toLowerCase()}</li>
                            <li>Mirror its interface in a floating window</li>
                            <li>Allow you to interact with it through Agent Lee</li>
                        </ul>
                    </div>
                    
                    <div class="permission-warning">
                        ⚠️ Agent Lee will be able to see and interact with this application while the Echo is active.
                    </div>
                    
                    <div class="permission-options">
                        <div class="permission-option">
                            <input type="radio" id="once_${permissionKey}" name="permission_${permissionKey}" value="once" checked>
                            <label for="once_${permissionKey}">Allow this time only</label>
                        </div>
                        <div class="permission-option">
                            <input type="radio" id="always_${permissionKey}" name="permission_${permissionKey}" value="always">
                            <label for="always_${permissionKey}">Always allow for ${app.name}</label>
                        </div>
                        <div class="permission-option">
                            <input type="radio" id="never_${permissionKey}" name="permission_${permissionKey}" value="never">
                            <label for="never_${permissionKey}">Never allow for ${app.name}</label>
                        </div>
                    </div>
                    
                    <div class="permission-buttons">
                        <button class="permission-btn permission-btn-deny" data-action="deny">Deny</button>
                        <button class="permission-btn permission-btn-allow" data-action="allow">Allow Echo</button>
                    </div>
                    
                    <div class="permission-countdown">
                        Auto-deny in <span id="countdown">${Math.floor(this.permissionSettings.autoCloseDelay / 1000)}</span> seconds
                    </div>
                </div>
            </div>
        `;

        // Set up event handlers
        this.setupPermissionDialogHandlers(overlay, permissionKey, resolve);

        // Start countdown
        this.startPermissionCountdown(overlay);

        return overlay;
    }

    setupPermissionDialogHandlers(overlay, permissionKey, resolve) {
        overlay.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            
            if (action === 'allow' || action === 'deny') {
                const selectedOption = overlay.querySelector(`input[name="permission_${permissionKey}"]:checked`);
                const choice = selectedOption ? selectedOption.value : 'once';
                
                this.handlePermissionChoice(permissionKey, action, choice);
                
                overlay.remove();
                resolve(action === 'allow');
            }
        });

        // Close on overlay click (outside dialog)
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                overlay.remove();
                resolve(false);
            }
        });

        // Prevent dialog click from closing
        const dialog = overlay.querySelector('.permission-dialog');
        dialog.addEventListener('click', (e) => {
            e.stopPropagation();
        });
    }

    startPermissionCountdown(overlay) {
        const countdownElement = overlay.querySelector('#countdown');
        let timeLeft = Math.floor(this.permissionSettings.autoCloseDelay / 1000);

        const countdownInterval = setInterval(() => {
            timeLeft--;
            if (countdownElement) {
                countdownElement.textContent = timeLeft;
            }

            if (timeLeft <= 0 || !overlay.parentElement) {
                clearInterval(countdownInterval);
            }
        }, 1000);
    }

    handlePermissionChoice(permissionKey, action, choice) {
        console.log(`🔐 Permission choice: ${action} (${choice}) for ${permissionKey}`);

        if (choice === 'always' || choice === 'never') {
            // Save permanent permission
            this.permissions.set(permissionKey, {
                choice: choice === 'always' ? 'allow' : 'deny',
                timestamp: Date.now(),
                permanent: true
            });

            if (this.permissionSettings.rememberChoices) {
                this.savePermissions();
            }
        }
    }

    /**
     * Clear all saved permissions
     */
    clearAllPermissions() {
        this.permissions.clear();
        localStorage.removeItem('agentlee_echo_permissions');
        console.log('🔐 All permissions cleared');
    }

    /**
     * Clear permission for a specific app
     */
    clearPermission(deviceType, appId) {
        const permissionKey = `${deviceType}_${appId}`;
        this.permissions.delete(permissionKey);
        this.savePermissions();
        console.log(`🔐 Permission cleared for ${permissionKey}`);
    }

    /**
     * Get all saved permissions
     */
    getAllPermissions() {
        return Array.from(this.permissions.entries()).map(([key, value]) => ({
            key,
            ...value
        }));
    }

    /**
     * Update permission settings
     */
    updateSettings(newSettings) {
        this.permissionSettings = { ...this.permissionSettings, ...newSettings };
        this.savePermissions();
        console.log('🔐 Permission settings updated');
    }

    /**
     * Get current permission settings
     */
    getSettings() {
        return { ...this.permissionSettings };
    }
}

// Create global instance
window.PermissionManagerClient = new PermissionManagerClient();
