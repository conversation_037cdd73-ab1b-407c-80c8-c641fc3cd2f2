<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Lee™ - Floating Widget</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: transparent;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
            height: 100vh;
            width: 100vw;
        }

        /* Agent Lee Floating Card Widget */
        .agent-lee-widget {
            position: fixed;
            top: 50px;
            right: 50px;
            width: 350px;
            height: 600px;
            background: linear-gradient(145deg, #1e293b 0%, #0f172a 100%);
            border-radius: 20px;
            box-shadow: 0 25px 80px rgba(0,0,0,0.8), 0 0 0 2px rgba(59, 130, 246, 0.3);
            border: 1px solid rgba(59, 130, 246, 0.2);
            z-index: 10000;
            cursor: move;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .agent-lee-widget.minimized {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            cursor: grab;
        }

        .agent-lee-widget.minimized .widget-content {
            display: none;
        }

        .agent-lee-widget.minimized .minimized-view {
            display: flex;
        }

        /* Minimized Circular View */
        .minimized-view {
            display: none;
            width: 100%;
            height: 100%;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            border-radius: 50%;
            position: relative;
        }

        .minimized-avatar {
            font-size: 32px;
            animation: blink 3s infinite;
        }

        .minimized-status {
            position: absolute;
            bottom: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            background: #10b981;
            border-radius: 50%;
            border: 2px solid #0f172a;
        }

        /* Widget Header */
        .widget-header {
            background: linear-gradient(135deg, #1e40af 0%, #3730a3 50%, #374151 100%);
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: grab;
            border-bottom: 1px solid rgba(59, 130, 246, 0.3);
            min-height: 70px;
        }

        .widget-header:active {
            cursor: grabbing;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .agent-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 6px 20px rgba(0,0,0,0.4), 0 0 0 2px rgba(59, 130, 246, 0.3);
            border: 2px solid rgba(255,255,255,0.2);
        }

        .agent-face {
            font-size: 20px;
            animation: blink 3s infinite;
        }

        @keyframes blink {
            0%, 90%, 100% { transform: scale(1); }
            95% { transform: scale(0.9); }
        }

        .agent-info {
            color: white;
        }

        .agent-name {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 2px;
        }

        .agent-status {
            font-size: 10px;
            color: rgba(255,255,255,0.8);
        }

        /* Widget Controls */
        .widget-controls {
            display: flex;
            gap: 6px;
        }

        .control-btn {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            font-size: 10px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .minimize-btn { background: #fbbf24; }
        .close-btn { background: #ef4444; }
        .spawn-btn { background: #10b981; }

        .control-btn:hover {
            transform: scale(1.2);
        }

        /* Widget Content */
        .widget-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 15px;
            gap: 12px;
            overflow-y: auto;
        }

        /* Quick Info */
        .quick-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .info-card {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 8px;
            padding: 10px;
            color: white;
            text-align: center;
            font-size: 11px;
        }

        .info-icon {
            font-size: 16px;
            margin-bottom: 4px;
        }

        .info-value {
            font-size: 12px;
            color: #3b82f6;
            font-weight: bold;
        }

        /* Conversation Area */
        .conversation-area {
            flex: 1;
            background: rgba(30, 41, 59, 0.6);
            border-radius: 10px;
            padding: 10px;
            overflow-y: auto;
            border: 1px solid rgba(59, 130, 246, 0.2);
            min-height: 150px;
            max-height: 200px;
        }

        .message {
            margin-bottom: 8px;
            padding: 6px 8px;
            border-radius: 6px;
            font-size: 11px;
            line-height: 1.3;
        }

        .agent-message {
            background: rgba(59, 130, 246, 0.2);
            color: #e2e8f0;
            border-left: 2px solid #3b82f6;
        }

        .user-message {
            background: rgba(16, 185, 129, 0.2);
            color: #e2e8f0;
            border-left: 2px solid #10b981;
            text-align: right;
        }

        /* Input Controls */
        .input-controls {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 6px;
            padding: 8px;
            color: white;
            font-size: 11px;
            outline: none;
        }

        .message-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            font-size: 14px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .voice-btn {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
        }

        .send-btn {
            background: linear-gradient(135deg, #3b82f6, #1e40af);
        }

        .action-btn:hover {
            transform: scale(1.1);
        }

        .action-btn.active {
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* Feature Grid */
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
        }

        .feature-btn {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 8px;
            padding: 8px 4px;
            color: white;
            font-size: 9px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            text-align: center;
        }

        .feature-btn:hover {
            background: rgba(59, 130, 246, 0.3);
            transform: translateY(-1px);
        }

        .feature-icon {
            font-size: 14px;
        }

        /* Echo Engine */
        .echo-engine {
            background: rgba(30, 41, 59, 0.6);
            border-radius: 8px;
            padding: 10px;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .echo-header {
            color: white;
            font-size: 11px;
            font-weight: bold;
            margin-bottom: 8px;
            text-align: center;
        }

        .device-tabs {
            display: flex;
            gap: 4px;
            margin-bottom: 8px;
            justify-content: center;
        }

        .device-tab {
            background: rgba(59, 130, 246, 0.2);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 4px;
            padding: 4px 8px;
            color: white;
            font-size: 9px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .device-tab.active {
            background: rgba(59, 130, 246, 0.5);
        }

        .app-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 6px;
        }

        .app-tile {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 6px;
            padding: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 3px;
            text-align: center;
        }

        .app-tile:hover {
            background: rgba(59, 130, 246, 0.3);
            transform: translateY(-1px);
        }

        .app-icon {
            font-size: 12px;
        }

        .app-name {
            font-size: 8px;
            color: white;
            font-weight: 500;
        }

        /* Status Bar */
        .status-bar {
            background: rgba(15, 23, 42, 0.8);
            padding: 6px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 9px;
            color: rgba(255, 255, 255, 0.7);
            border-top: 1px solid rgba(59, 130, 246, 0.2);
        }

        .listening-indicator {
            display: none;
            color: #ef4444;
            animation: pulse 1s infinite;
        }

        .listening-indicator.active {
            display: inline;
        }

        /* Spawned Cards */
        .spawned-card {
            position: fixed;
            width: 400px;
            height: 300px;
            background: linear-gradient(145deg, #1e293b 0%, #0f172a 100%);
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.8), 0 0 0 2px rgba(59, 130, 246, 0.3);
            border: 1px solid rgba(59, 130, 246, 0.2);
            z-index: 9999;
            cursor: move;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .spawned-header {
            background: linear-gradient(135deg, #1e40af 0%, #3730a3 50%, #374151 100%);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: grab;
            border-bottom: 1px solid rgba(59, 130, 246, 0.3);
        }

        .spawned-title {
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .spawned-content {
            flex: 1;
            padding: 15px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Agent Lee Floating Widget -->
    <div class="agent-lee-widget" id="agentWidget">
        <!-- Minimized View -->
        <div class="minimized-view" id="minimizedView">
            <div class="minimized-avatar">🤖</div>
            <div class="minimized-status"></div>
        </div>

        <!-- Full Widget Content -->
        <div class="widget-content" id="widgetContent">
            <!-- Widget Header -->
            <div class="widget-header" id="widgetHeader">
                <div class="header-left">
                    <div class="agent-avatar">
                        <div class="agent-face" id="agentFace">🤖</div>
                    </div>
                    <div class="agent-info">
                        <div class="agent-name">Agent Lee™</div>
                        <div class="agent-status" id="agentStatus">Ready</div>
                    </div>
                </div>
                
                <div class="widget-controls">
                    <button class="control-btn spawn-btn" id="spawnBtn" title="Spawn New Card">+</button>
                    <button class="control-btn minimize-btn" id="minimizeBtn" title="Minimize">−</button>
                    <button class="control-btn close-btn" id="closeBtn" title="Close">×</button>
                </div>
            </div>

            <!-- Quick Info -->
            <div class="quick-info">
                <div class="info-card">
                    <div class="info-icon">🌤️</div>
                    <div class="info-value" id="weatherInfo">72°F</div>
                </div>
                <div class="info-card">
                    <div class="info-icon">🚗</div>
                    <div class="info-value" id="trafficInfo">Light</div>
                </div>
            </div>

            <!-- Conversation Area -->
            <div class="conversation-area" id="conversationArea">
                <div class="message agent-message">
                    Hello! I'm Agent Lee. I can open apps, create Echoes, and help you with tasks. What would you like me to do?
                </div>
            </div>

            <!-- Input Controls -->
            <div class="input-controls">
                <input type="text" class="message-input" id="messageInput" placeholder="Type or speak to Agent Lee...">
                <button class="action-btn voice-btn" id="voiceBtn" title="Voice">🎤</button>
                <button class="action-btn send-btn" id="sendBtn" title="Send">➤</button>
            </div>

            <!-- Feature Buttons -->
            <div class="feature-grid">
                <button class="feature-btn" id="appsBtn">
                    <span class="feature-icon">📱</span>
                    <span>Apps</span>
                </button>
                <button class="feature-btn" id="webBtn">
                    <span class="feature-icon">🌐</span>
                    <span>Web</span>
                </button>
                <button class="feature-btn" id="emailBtn">
                    <span class="feature-icon">📧</span>
                    <span>Email</span>
                </button>
                <button class="feature-btn" id="newsBtn">
                    <span class="feature-icon">📰</span>
                    <span>News</span>
                </button>
            </div>

            <!-- Echo Engine -->
            <div class="echo-engine">
                <div class="echo-header">🔄 Echo Engine</div>
                
                <div class="device-tabs">
                    <div class="device-tab active" data-device="computer">💻</div>
                    <div class="device-tab" data-device="phone">📱</div>
                    <div class="device-tab" data-device="tablet">📱</div>
                    <div class="device-tab" data-device="laptop">💻</div>
                </div>
                
                <div class="app-grid" id="appGrid">
                    <!-- Apps will be populated by JavaScript -->
                </div>
            </div>

            <!-- Status Bar -->
            <div class="status-bar">
                <span id="connectionStatus">🟢 Ready</span>
                <span class="listening-indicator" id="listeningIndicator">🎤</span>
                <span id="timeStatus"></span>
            </div>
        </div>
    </div>

    <script src="agent_lee_widget.js"></script>
</body>
</html>
