#!/usr/bin/env python3
"""
Agent Lee™ Simple Production Starter
Simplified, reliable startup script for production deployment
"""

import os
import sys
import time
import subprocess
import webbrowser
from pathlib import Path

def main():
    """Simple main function to start Agent <PERSON>"""
    print("🧠 AGENT LEE™ SIMPLE PRODUCTION STARTER")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print(f"❌ Python 3.8+ required, found {sys.version_info[0]}.{sys.version_info[1]}")
        return False
    
    print(f"✅ Python {sys.version_info[0]}.{sys.version_info[1]}.{sys.version_info[2]}")
    
    # Check if backend exists
    backend_file = Path("backend/agentlee_controller_production.py")
    if not backend_file.exists():
        print("❌ Production controller not found")
        print("💡 Make sure you're in the Agent Lee directory")
        return False
    
    print("✅ Production controller found")
    
    # Try to start the backend
    print("🚀 Starting Agent Lee backend...")
    
    try:
        # Change to backend directory and start uvicorn
        cmd = [
            sys.executable, "-m", "uvicorn",
            "agentlee_controller_production:app",
            "--host", "localhost",
            "--port", "8000",
            "--log-level", "info"
        ]
        
        print(f"🔧 Command: {' '.join(cmd)}")
        print("🔧 Working directory: backend/")
        
        # Start the process
        process = subprocess.Popen(
            cmd,
            cwd="backend",
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        print("✅ Backend process started")
        print("⏳ Waiting for system to be ready...")
        
        # Wait a moment for startup
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is not None:
            print("❌ Backend process terminated")
            output = process.stdout.read()
            print(f"Error output: {output}")
            return False
        
        print("✅ Backend appears to be running")
        
        # Display success info
        print("\n" + "=" * 50)
        print("🧠 AGENT LEE™ - READY")
        print("=" * 50)
        print("🌐 Frontend: http://localhost:8000/app")
        print("🔧 API: http://localhost:8000")
        print("🏥 Health: http://localhost:8000/api/health")
        print("=" * 50)
        print("💡 Press Ctrl+C to shutdown")
        print("=" * 50)
        
        # Try to open browser
        try:
            webbrowser.open("http://localhost:8000/app")
            print("🌐 Browser opened")
        except:
            print("⚠️ Could not open browser automatically")
        
        # Monitor the process
        try:
            while True:
                # Check if process is still running
                if process.poll() is not None:
                    print("❌ Backend process terminated")
                    break
                
                # Read and display any output
                try:
                    line = process.stdout.readline()
                    if line:
                        # Only show important messages
                        if any(keyword in line.lower() for keyword in ['error', 'warning', 'started', 'application startup complete']):
                            print(f"Backend: {line.strip()}")
                except:
                    pass
                
                time.sleep(0.1)
                
        except KeyboardInterrupt:
            print("\n🛑 Shutdown signal received")
            
        # Cleanup
        print("🛑 Shutting down...")
        process.terminate()
        
        try:
            process.wait(timeout=5)
            print("✅ Shutdown complete")
        except subprocess.TimeoutExpired:
            print("⚠️ Force killing process...")
            process.kill()
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to start: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Interrupted")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
