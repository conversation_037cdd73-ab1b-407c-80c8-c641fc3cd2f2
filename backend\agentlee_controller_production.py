#!/usr/bin/env python3
"""
Agent Lee™ Production Controller
Simplified, production-ready FastAPI backend with essential features only
"""

import os
import sys
import time
import platform
import logging
import asyncio
from typing import Optional, Dict, Any
from datetime import datetime
from contextlib import asynccontextmanager
from pathlib import Path

# FastAPI and CORS
from fastapi import Fast<PERSON>I, HTTPException, BackgroundTasks, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, HTMLResponse
from pydantic import BaseModel

# System monitoring
import psutil

# Text to speech
try:
    import pyttsx3
    TTS_AVAILABLE = True
except ImportError:
    TTS_AVAILABLE = False

# Environment variables
from dotenv import load_dotenv
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("agentlee.log")
    ]
)

logger = logging.getLogger("agentlee.production")

# Global state
start_time = time.time()
SYSTEM_STATUS = {
    "llm_ready": True,
    "system_health": 100.0,
    "last_update": datetime.now(),
    "cognitive_active": False
}

# API Models
class SystemStatusResponse(BaseModel):
    status: str
    llm_ready: bool
    system_health: float
    last_update: str
    cognitive_active: bool
    uptime: float

class TextToSpeechRequest(BaseModel):
    text: str
    voice: Optional[str] = "default"
    rate: Optional[float] = 1.0

# Helper Functions
def get_system_health() -> float:
    """Calculate system health score"""
    try:
        cpu = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory().percent
        
        # Simple health calculation
        health = 100 - max(0, (cpu - 50) * 2) - max(0, (memory - 70) * 3)
        return max(0, min(100, health))
    except:
        return 100.0

def text_to_speech(text: str, voice: str = "default", rate: float = 1.0) -> bool:
    """Convert text to speech"""
    if not TTS_AVAILABLE:
        logger.warning("TTS not available")
        return False
    
    try:
        engine = pyttsx3.init()
        engine.setProperty('rate', int(engine.getProperty('rate') * rate))
        engine.say(text)
        engine.runAndWait()
        return True
    except Exception as e:
        logger.error(f"TTS error: {e}")
        return False

# Background tasks
async def update_system_status():
    """Update system status periodically"""
    while True:
        try:
            SYSTEM_STATUS["system_health"] = get_system_health()
            SYSTEM_STATUS["last_update"] = datetime.now()
            await asyncio.sleep(30)
        except Exception as e:
            logger.error(f"Status update error: {e}")
            await asyncio.sleep(30)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    # Startup
    logger.info("Agent Lee Production Controller starting...")

    # Start background tasks
    status_task = asyncio.create_task(update_system_status())

    # Welcome message
    if TTS_AVAILABLE:
        asyncio.create_task(asyncio.to_thread(text_to_speech, "Agent Lee Production System Ready"))

    logger.info("Agent Lee Production Controller ready")
    
    yield
    
    # Shutdown
    logger.info("Agent Lee Production Controller shutting down...")
    status_task.cancel()
    try:
        await status_task
    except asyncio.CancelledError:
        pass

# Create FastAPI app
app = FastAPI(
    title="Agent Lee™ Production Controller",
    description="Production-ready AI assistant backend",
    version="3.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Routes
@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "Agent Lee™ Production Controller",
        "version": "3.0.0",
        "status": "operational",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/app")
async def serve_app():
    """Serve the production frontend"""
    try:
        # Look for production HTML file
        html_files = ["index_production.html", "index.html"]
        
        for html_file in html_files:
            html_path = Path(__file__).parent.parent / html_file
            if html_path.exists():
                return FileResponse(str(html_path))
        
        # Fallback: return simple HTML
        return HTMLResponse(content="""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Agent Lee™</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body { 
                    font-family: Arial, sans-serif; 
                    background: linear-gradient(135deg, #1e293b, #0f172a);
                    color: white; 
                    display: flex; 
                    align-items: center; 
                    justify-content: center; 
                    height: 100vh; 
                    margin: 0;
                }
                .container { 
                    text-align: center; 
                    padding: 40px;
                    background: rgba(30, 41, 59, 0.8);
                    border-radius: 20px;
                    border: 1px solid rgba(59, 130, 246, 0.3);
                }
                h1 { color: #3b82f6; margin-bottom: 20px; }
                .status { color: #10b981; margin: 10px 0; }
                button {
                    background: #3b82f6;
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 8px;
                    cursor: pointer;
                    margin: 10px;
                    font-size: 16px;
                }
                button:hover { background: #2563eb; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🧠 Agent Lee™</h1>
                <div class="status">Production System Ready</div>
                <p>Cognitive AI Assistant</p>
                <button onclick="testSpeech()">Test Speech</button>
                <button onclick="checkStatus()">Check Status</button>
                
                <script>
                    async function testSpeech() {
                        try {
                            const response = await fetch('/api/speak', {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({ text: 'Hello, I am Agent Lee, your cognitive AI assistant.' })
                            });
                            const result = await response.json();
                            alert('Speech test: ' + result.status);
                        } catch (error) {
                            alert('Speech test failed: ' + error.message);
                        }
                    }
                    
                    async function checkStatus() {
                        try {
                            const response = await fetch('/api/system_status');
                            const status = await response.json();
                            alert('System Status: ' + JSON.stringify(status, null, 2));
                        } catch (error) {
                            alert('Status check failed: ' + error.message);
                        }
                    }
                </script>
            </div>
        </body>
        </html>
        """)
        
    except Exception as e:
        logger.error(f"Error serving app: {e}")
        raise HTTPException(status_code=500, detail="Frontend not available")

@app.get("/api/system_status", response_model=SystemStatusResponse)
async def get_system_status():
    """Get system status"""
    return SystemStatusResponse(
        status="operational",
        llm_ready=SYSTEM_STATUS["llm_ready"],
        system_health=SYSTEM_STATUS["system_health"],
        last_update=SYSTEM_STATUS["last_update"].isoformat(),
        cognitive_active=SYSTEM_STATUS["cognitive_active"],
        uptime=time.time() - start_time
    )

@app.post("/api/speak")
async def speak(request: TextToSpeechRequest, background_tasks: BackgroundTasks):
    """Text to speech endpoint"""
    try:
        if not TTS_AVAILABLE:
            return {
                "status": "error",
                "message": "Text-to-speech not available",
                "details": "pyttsx3 not installed"
            }
        
        # Queue speech in background
        background_tasks.add_task(text_to_speech, request.text, request.voice, request.rate)
        
        return {
            "status": "success",
            "message": "Speech queued",
            "text": request.text
        }
        
    except Exception as e:
        logger.error(f"Speech error: {e}")
        return {
            "status": "error",
            "message": "Speech failed",
            "details": str(e)
        }

@app.get("/api/health")
async def health_check():
    """Detailed health check"""
    try:
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "uptime": time.time() - start_time,
            "system": {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "memory_available_gb": memory.available / (1024**3),
                "platform": platform.system(),
                "python_version": platform.python_version()
            },
            "services": {
                "tts_available": TTS_AVAILABLE,
                "llm_ready": SYSTEM_STATUS["llm_ready"],
                "cognitive_active": SYSTEM_STATUS["cognitive_active"]
            }
        }
        
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return {
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.get("/api/version")
async def get_version():
    """Get version information"""
    return {
        "name": "Agent Lee™",
        "version": "3.0.0",
        "build": "Production Release",
        "description": "Cognitive AI Assistant",
        "platform": platform.system(),
        "python_version": platform.python_version(),
        "uptime": time.time() - start_time,
        "features": {
            "tts": TTS_AVAILABLE,
            "cognitive_architecture": False,  # Will be enabled when fully implemented
            "voice_recognition": False,
            "browser_automation": False
        }
    }

@app.get("/favicon.ico")
async def favicon():
    """Favicon endpoint"""
    return {"message": "Agent Lee favicon"}

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request: Request, exc: HTTPException):
    return {
        "error": "Not Found",
        "message": f"The requested path {request.url.path} was not found",
        "status_code": 404
    }

@app.exception_handler(500)
async def internal_error_handler(request: Request, exc: HTTPException):
    logger.error(f"Internal server error: {exc}")
    return {
        "error": "Internal Server Error",
        "message": "An internal server error occurred",
        "status_code": 500
    }

if __name__ == "__main__":
    import uvicorn
    
    logger.info("🚀 Starting Agent Lee™ Production Controller...")
    
    uvicorn.run(
        "agentlee_controller_production:app",
        host="localhost",
        port=8000,
        log_level="info",
        reload=False  # Disable reload in production
    )
