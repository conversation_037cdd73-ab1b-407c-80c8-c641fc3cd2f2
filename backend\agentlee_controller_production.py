#!/usr/bin/env python3
"""
Agent Lee™ Production Controller
Simplified, production-ready FastAPI backend with essential features only
"""

import os
import sys
import time
import platform
import logging
import asyncio
from typing import Optional, Dict, Any
from datetime import datetime
from contextlib import asynccontextmanager
from pathlib import Path

# FastAPI and CORS
from fastapi import Fast<PERSON><PERSON>, HTTPException, BackgroundTasks, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel

# System monitoring
import psutil

# Text to speech
try:
    import pyttsx3
    TTS_AVAILABLE = True
except ImportError:
    TTS_AVAILABLE = False

# Environment variables
from dotenv import load_dotenv
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("agentlee.log")
    ]
)

logger = logging.getLogger("agentlee.production")

# Global state
start_time = time.time()
SYSTEM_STATUS = {
    "llm_ready": True,
    "system_health": 100.0,
    "last_update": datetime.now(),
    "cognitive_active": False
}

# API Models
class SystemStatusResponse(BaseModel):
    status: str
    llm_ready: bool
    system_health: float
    last_update: str
    cognitive_active: bool
    uptime: float

class TextToSpeechRequest(BaseModel):
    text: str
    voice: Optional[str] = "default"
    rate: Optional[float] = 1.0

class ChatRequest(BaseModel):
    message: str
    history: Optional[list] = []

class ChatResponse(BaseModel):
    message: str
    speak: bool = True
    action: Optional[Dict[str, Any]] = None

class OpenAppRequest(BaseModel):
    app: str

# Helper Functions
def get_system_health() -> float:
    """Calculate system health score"""
    try:
        cpu = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory().percent
        
        # Simple health calculation
        health = 100 - max(0, (cpu - 50) * 2) - max(0, (memory - 70) * 3)
        return max(0, min(100, health))
    except:
        return 100.0

def text_to_speech(text: str, voice: str = "default", rate: float = 1.0) -> bool:
    """Convert text to speech"""
    if not TTS_AVAILABLE:
        logger.warning("TTS not available")
        return False
    
    try:
        engine = pyttsx3.init()
        engine.setProperty('rate', int(engine.getProperty('rate') * rate))
        engine.say(text)
        engine.runAndWait()
        return True
    except Exception as e:
        logger.error(f"TTS error: {e}")
        return False

# Background tasks
async def update_system_status():
    """Update system status periodically"""
    while True:
        try:
            SYSTEM_STATUS["system_health"] = get_system_health()
            SYSTEM_STATUS["last_update"] = datetime.now()
            await asyncio.sleep(30)
        except Exception as e:
            logger.error(f"Status update error: {e}")
            await asyncio.sleep(30)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    # Startup
    logger.info("Agent Lee Production Controller starting...")

    # Start background tasks
    status_task = asyncio.create_task(update_system_status())

    # Welcome message
    if TTS_AVAILABLE:
        asyncio.create_task(asyncio.to_thread(text_to_speech, "Agent Lee Production System Ready"))

    logger.info("Agent Lee Production Controller ready")
    
    yield
    
    # Shutdown
    logger.info("Agent Lee Production Controller shutting down...")
    status_task.cancel()
    try:
        await status_task
    except asyncio.CancelledError:
        pass

# Create FastAPI app
app = FastAPI(
    title="Agent Lee™ Production Controller",
    description="Production-ready AI assistant backend",
    version="3.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Mount static files for frontend assets (if they exist)
try:
    frontend_dir = Path(__file__).parent.parent / "frontend"
    if frontend_dir.exists():
        app.mount("/frontend", StaticFiles(directory=str(frontend_dir)), name="frontend")
        logger.info(f"Mounted frontend directory: {frontend_dir}")
    else:
        logger.warning("Frontend directory not found - static files not mounted")
except Exception as e:
    logger.warning(f"Could not mount static files: {e}")

# Handle missing static files gracefully
@app.get("/frontend/{file_path:path}")
async def serve_frontend_fallback(file_path: str):
    """Fallback for missing frontend files"""
    logger.warning(f"Frontend file not found: {file_path}")
    return JSONResponse(
        status_code=404,
        content={"error": "File not found", "message": f"Frontend file {file_path} not available"}
    )

# Routes
@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "Agent Lee™ Production Controller",
        "version": "3.0.0",
        "status": "operational",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/app")
async def serve_app():
    """Serve the production frontend"""
    try:
        # Look for REAL Agent Lee Widget first (actually working floating widget), then fallbacks
        html_files = ["agent_lee_real_widget.html", "agent_lee_widget.html", "agent_lee_complete.html", "echo_engine.html", "agent_lee_real.html", "index_production.html", "index.html"]

        for html_file in html_files:
            html_path = Path(__file__).parent.parent / html_file
            if html_path.exists():
                return FileResponse(str(html_path))
        
        # Fallback: return simple HTML
        return HTMLResponse(content="""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Agent Lee™</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body { 
                    font-family: Arial, sans-serif; 
                    background: linear-gradient(135deg, #1e293b, #0f172a);
                    color: white; 
                    display: flex; 
                    align-items: center; 
                    justify-content: center; 
                    height: 100vh; 
                    margin: 0;
                }
                .container { 
                    text-align: center; 
                    padding: 40px;
                    background: rgba(30, 41, 59, 0.8);
                    border-radius: 20px;
                    border: 1px solid rgba(59, 130, 246, 0.3);
                }
                h1 { color: #3b82f6; margin-bottom: 20px; }
                .status { color: #10b981; margin: 10px 0; }
                button {
                    background: #3b82f6;
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 8px;
                    cursor: pointer;
                    margin: 10px;
                    font-size: 16px;
                }
                button:hover { background: #2563eb; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🧠 Agent Lee™</h1>
                <div class="status">Production System Ready</div>
                <p>Cognitive AI Assistant</p>
                <button onclick="testSpeech()">Test Speech</button>
                <button onclick="checkStatus()">Check Status</button>
                
                <script>
                    async function testSpeech() {
                        try {
                            const response = await fetch('/api/speak', {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({ text: 'Hello, I am Agent Lee, your cognitive AI assistant.' })
                            });
                            const result = await response.json();
                            alert('Speech test: ' + result.status);
                        } catch (error) {
                            alert('Speech test failed: ' + error.message);
                        }
                    }
                    
                    async function checkStatus() {
                        try {
                            const response = await fetch('/api/system_status');
                            const status = await response.json();
                            alert('System Status: ' + JSON.stringify(status, null, 2));
                        } catch (error) {
                            alert('Status check failed: ' + error.message);
                        }
                    }
                </script>
            </div>
        </body>
        </html>
        """)
        
    except Exception as e:
        logger.error(f"Error serving app: {e}")
        raise HTTPException(status_code=500, detail="Frontend not available")

@app.get("/api/system_status", response_model=SystemStatusResponse)
async def get_system_status():
    """Get system status"""
    return SystemStatusResponse(
        status="operational",
        llm_ready=SYSTEM_STATUS["llm_ready"],
        system_health=SYSTEM_STATUS["system_health"],
        last_update=SYSTEM_STATUS["last_update"].isoformat(),
        cognitive_active=SYSTEM_STATUS["cognitive_active"],
        uptime=time.time() - start_time
    )

@app.post("/api/speak")
async def speak(request: TextToSpeechRequest, background_tasks: BackgroundTasks):
    """Text to speech endpoint"""
    try:
        if not TTS_AVAILABLE:
            return {
                "status": "error",
                "message": "Text-to-speech not available",
                "details": "pyttsx3 not installed"
            }

        # Queue speech in background
        background_tasks.add_task(text_to_speech, request.text, request.voice, request.rate)

        return {
            "status": "success",
            "message": "Speech queued",
            "text": request.text
        }

    except Exception as e:
        logger.error(f"Speech error: {e}")
        return {
            "status": "error",
            "message": "Speech failed",
            "details": str(e)
        }

@app.post("/api/chat", response_model=ChatResponse)
async def chat_with_agent(request: ChatRequest):
    """Chat with Agent Lee"""
    try:
        message = request.message.lower().strip()

        # Simple AI responses based on keywords
        if any(word in message for word in ["hello", "hi", "hey"]):
            response = "Hello! I'm Agent Lee, your AI assistant. How can I help you today?"

        elif any(word in message for word in ["app", "application", "program", "software"]):
            if "open" in message or "start" in message or "launch" in message:
                # Extract app name
                app_name = extract_app_name(message)
                if app_name:
                    return ChatResponse(
                        message=f"I'll open {app_name} for you right away!",
                        speak=True,
                        action={"type": "open_app", "app": app_name}
                    )
                else:
                    response = "Which application would you like me to open? I can help you launch programs like Chrome, Notepad, Calculator, or any other installed application."
            else:
                response = "I can help you open applications! Just tell me which program you'd like to launch, like 'open Chrome' or 'start Calculator'."

        elif any(word in message for word in ["search", "google", "web", "internet"]):
            if "for" in message:
                query = message.split("for", 1)[1].strip()
                return ChatResponse(
                    message=f"I'll search the web for '{query}' for you!",
                    speak=True,
                    action={"type": "web_search", "query": query}
                )
            else:
                response = "What would you like me to search for on the web?"

        elif any(word in message for word in ["weather", "temperature", "forecast"]):
            response = "I'd be happy to check the weather for you! However, I need access to a weather service to provide current conditions. Would you like me to open a weather website?"

        elif any(word in message for word in ["email", "mail", "message"]):
            response = "I can help you with email! Would you like me to open your email application?"

        elif any(word in message for word in ["news", "headlines", "current events"]):
            response = "I can help you stay updated with the latest news! Would you like me to open a news website or search for specific news topics?"

        elif any(word in message for word in ["help", "what can you do", "capabilities"]):
            response = """I'm Agent Lee, your AI assistant! Here's what I can help you with:

🖥️ **System Control**: Open applications, manage programs
🌐 **Web Search**: Search the internet for information
📧 **Email**: Help with email applications
🌤️ **Weather**: Weather information and forecasts
📰 **News**: Latest news and current events
⚙️ **Settings**: System settings and configuration

Just tell me what you need, like "open Chrome" or "search for Python tutorials"!"""

        elif any(word in message for word in ["thank", "thanks"]):
            response = "You're very welcome! I'm always here to help. Is there anything else you'd like me to do?"

        elif any(word in message for word in ["bye", "goodbye", "see you"]):
            response = "Goodbye! It was great helping you today. Feel free to come back anytime you need assistance!"

        else:
            # Default intelligent response
            response = f"I understand you're asking about '{message}'. While I'm still learning, I can help you with opening applications, web searches, and system tasks. What specific task would you like me to help you with?"

        return ChatResponse(
            message=response,
            speak=True,
            action=None
        )

    except Exception as e:
        logger.error(f"Chat error: {e}")
        return ChatResponse(
            message="I'm sorry, I'm having trouble processing that right now. Please try again.",
            speak=True,
            action=None
        )

def extract_app_name(message: str) -> Optional[str]:
    """Extract application name from user message"""
    # Common application mappings
    app_mappings = {
        "chrome": "chrome",
        "browser": "chrome",
        "firefox": "firefox",
        "edge": "msedge",
        "notepad": "notepad",
        "calculator": "calc",
        "paint": "mspaint",
        "word": "winword",
        "excel": "excel",
        "powerpoint": "powerpnt",
        "outlook": "outlook",
        "teams": "teams",
        "discord": "discord",
        "spotify": "spotify",
        "steam": "steam",
        "code": "code",
        "vscode": "code",
        "visual studio": "code"
    }

    for app_keyword, app_executable in app_mappings.items():
        if app_keyword in message.lower():
            return app_executable

    # Try to extract app name after "open" or "start"
    words = message.split()
    for i, word in enumerate(words):
        if word.lower() in ["open", "start", "launch"] and i + 1 < len(words):
            return words[i + 1]

    return None

@app.post("/api/open_app")
async def open_application(request: OpenAppRequest):
    """Open an application"""
    try:
        app_name = request.app.lower()

        # Application executable mappings
        app_executables = {
            "chrome": "chrome.exe",
            "firefox": "firefox.exe",
            "msedge": "msedge.exe",
            "edge": "msedge.exe",
            "notepad": "notepad.exe",
            "calc": "calc.exe",
            "calculator": "calc.exe",
            "mspaint": "mspaint.exe",
            "paint": "mspaint.exe",
            "winword": "winword.exe",
            "word": "winword.exe",
            "excel": "excel.exe",
            "powerpnt": "powerpnt.exe",
            "powerpoint": "powerpnt.exe",
            "outlook": "outlook.exe",
            "teams": "teams.exe",
            "discord": "discord.exe",
            "spotify": "spotify.exe",
            "steam": "steam.exe",
            "code": "code.exe",
            "vscode": "code.exe"
        }

        executable = app_executables.get(app_name, f"{app_name}.exe")

        # Try to open the application
        import subprocess

        try:
            # Try direct execution first
            subprocess.Popen([executable], shell=True)
            logger.info(f"Successfully opened {executable}")

            return {
                "status": "success",
                "message": f"Successfully opened {app_name}",
                "app": app_name
            }

        except Exception as e:
            # Try with start command (Windows)
            try:
                subprocess.Popen(f"start {executable}", shell=True)
                logger.info(f"Successfully opened {executable} with start command")

                return {
                    "status": "success",
                    "message": f"Successfully opened {app_name}",
                    "app": app_name
                }

            except Exception as e2:
                logger.error(f"Failed to open {executable}: {e2}")

                return {
                    "status": "error",
                    "message": f"Could not open {app_name}. Make sure it's installed.",
                    "app": app_name,
                    "error": str(e2)
                }

    except Exception as e:
        logger.error(f"Application opening error: {e}")
        return {
            "status": "error",
            "message": f"Failed to open application: {str(e)}",
            "app": request.app
        }

@app.get("/api/scan_apps")
async def scan_installed_applications():
    """Scan for installed applications on the system"""
    try:
        import subprocess
        import winreg

        apps = []

        # Common applications with their typical paths and icons
        common_apps = [
            {"id": "chrome", "name": "Google Chrome", "icon": "🌐", "path": "chrome.exe", "category": "Browser"},
            {"id": "firefox", "name": "Firefox", "icon": "🦊", "path": "firefox.exe", "category": "Browser"},
            {"id": "edge", "name": "Microsoft Edge", "icon": "🌐", "path": "msedge.exe", "category": "Browser"},
            {"id": "notepad", "name": "Notepad", "icon": "📝", "path": "notepad.exe", "category": "Editor"},
            {"id": "calculator", "name": "Calculator", "icon": "🧮", "path": "calc.exe", "category": "Utility"},
            {"id": "paint", "name": "Paint", "icon": "🎨", "path": "mspaint.exe", "category": "Graphics"},
            {"id": "word", "name": "Microsoft Word", "icon": "📄", "path": "winword.exe", "category": "Office"},
            {"id": "excel", "name": "Microsoft Excel", "icon": "📊", "path": "excel.exe", "category": "Office"},
            {"id": "powerpoint", "name": "PowerPoint", "icon": "📊", "path": "powerpnt.exe", "category": "Office"},
            {"id": "outlook", "name": "Outlook", "icon": "📧", "path": "outlook.exe", "category": "Email"},
            {"id": "teams", "name": "Microsoft Teams", "icon": "👥", "path": "teams.exe", "category": "Communication"},
            {"id": "discord", "name": "Discord", "icon": "💬", "path": "discord.exe", "category": "Communication"},
            {"id": "spotify", "name": "Spotify", "icon": "🎵", "path": "spotify.exe", "category": "Media"},
            {"id": "steam", "name": "Steam", "icon": "🎮", "path": "steam.exe", "category": "Gaming"},
            {"id": "vscode", "name": "Visual Studio Code", "icon": "💻", "path": "code.exe", "category": "Development"},
            {"id": "photoshop", "name": "Adobe Photoshop", "icon": "🎨", "path": "photoshop.exe", "category": "Graphics"},
            {"id": "vlc", "name": "VLC Media Player", "icon": "🎬", "path": "vlc.exe", "category": "Media"},
            {"id": "zoom", "name": "Zoom", "icon": "📹", "path": "zoom.exe", "category": "Communication"}
        ]

        # Check which applications are actually available
        for app in common_apps:
            try:
                # Try to find the application
                result = subprocess.run(f"where {app['path']}", shell=True, capture_output=True, text=True)
                if result.returncode == 0:
                    apps.append(app)
            except:
                # If 'where' command fails, assume the app might still be available
                apps.append(app)

        logger.info(f"Found {len(apps)} applications")

        return {
            "status": "success",
            "apps": apps,
            "count": len(apps)
        }

    except Exception as e:
        logger.error(f"Application scanning error: {e}")
        return {
            "status": "error",
            "message": f"Failed to scan applications: {str(e)}",
            "apps": []
        }

@app.get("/api/health")
async def health_check():
    """Detailed health check"""
    try:
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "uptime": time.time() - start_time,
            "system": {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "memory_available_gb": memory.available / (1024**3),
                "platform": platform.system(),
                "python_version": platform.python_version()
            },
            "services": {
                "tts_available": TTS_AVAILABLE,
                "llm_ready": SYSTEM_STATUS["llm_ready"],
                "cognitive_active": SYSTEM_STATUS["cognitive_active"]
            }
        }
        
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return {
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.get("/api/version")
async def get_version():
    """Get version information"""
    return {
        "name": "Agent Lee™",
        "version": "3.0.0",
        "build": "Production Release",
        "description": "Cognitive AI Assistant",
        "platform": platform.system(),
        "python_version": platform.python_version(),
        "uptime": time.time() - start_time,
        "features": {
            "tts": TTS_AVAILABLE,
            "cognitive_architecture": False,  # Will be enabled when fully implemented
            "voice_recognition": False,
            "browser_automation": False
        }
    }

@app.get("/favicon.ico")
async def favicon():
    """Favicon endpoint"""
    return {"message": "Agent Lee favicon"}

@app.get("/manifest.webmanifest")
async def manifest():
    """PWA manifest fallback"""
    try:
        manifest_path = Path(__file__).parent.parent / "manifest.webmanifest"
        if manifest_path.exists():
            return FileResponse(str(manifest_path))
        else:
            # Return basic manifest
            return JSONResponse(
                content={
                    "name": "Agent Lee™ Cognitive AI Assistant",
                    "short_name": "AgentLee",
                    "description": "Advanced Cognitive AI Assistant",
                    "start_url": "/",
                    "display": "standalone",
                    "background_color": "#000000",
                    "theme_color": "#3b82f6"
                },
                headers={"Content-Type": "application/manifest+json"}
            )
    except Exception as e:
        logger.error(f"Manifest error: {e}")
        return JSONResponse(
            status_code=404,
            content={"error": "Manifest not available"}
        )

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request: Request, exc: HTTPException):
    from fastapi.responses import JSONResponse
    return JSONResponse(
        status_code=404,
        content={
            "error": "Not Found",
            "message": f"The requested path {request.url.path} was not found",
            "status_code": 404
        }
    )

@app.exception_handler(500)
async def internal_error_handler(request: Request, exc: HTTPException):
    from fastapi.responses import JSONResponse
    logger.error(f"Internal server error: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal Server Error",
            "message": "An internal server error occurred",
            "status_code": 500
        }
    )

if __name__ == "__main__":
    import uvicorn
    
    logger.info("🚀 Starting Agent Lee™ Production Controller...")
    
    uvicorn.run(
        "agentlee_controller_production:app",
        host="localhost",
        port=8000,
        log_level="info",
        reload=False  # Disable reload in production
    )
