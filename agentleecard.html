<html lang="en">
<head>
<!-- 🔴 REGION: SEO --><meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Agent Lee™</title>
<link rel="manifest" href="data:application/json,{%22name%22:%22Agent%20Lee%22,%22short_name%22:%22AgentLee%22,%22display%22:%22standalone%22,%22background_color%22:%22transparent%22}">
<style>
    /* 🟢 REGION: CORE - Clean Floating Interface */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    html, body {
      background: transparent !important;
      overflow: hidden;
      width: 100% !important;
      height: 100% !important;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    /* 🔵 REGION: UI - Professional Phone Design */
    .phone-container {
      width: 320px;
      height: 640px;
      background: linear-gradient(145deg, #1e293b 0%, #0f172a 100%);
      border-radius: 25px;
      box-shadow: 0 20px 60px rgba(0,0,0,0.8), 0 0 0 2px rgba(59, 130, 246, 0.2);
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      overflow: hidden;
      cursor: move;
      z-index: 10000;
      transition: all 0.3s ease;
      resize: both;
      min-width: 300px;
      min-height: 580px;
      max-width: 450px;
      max-height: 800px;
      border: 1px solid rgba(59, 130, 246, 0.3);
    }

    .phone-container:hover {
      box-shadow: 0 25px 80px rgba(0,0,0,0.9), 0 0 0 2px rgba(59, 130, 246, 0.4);
    }

    /* Animated Circuit Board Background */
    .circuit-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: 
        radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(30, 41, 59, 0.8) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
      opacity: 0.3;
      z-index: 1;
    }

    .circuit-lines {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: 
        linear-gradient(90deg, transparent 49%, rgba(59, 130, 246, 0.1) 50%, transparent 51%),
        linear-gradient(0deg, transparent 49%, rgba(59, 130, 246, 0.1) 50%, transparent 51%);
      background-size: 30px 30px;
      animation: circuitFlow 20s linear infinite;
      z-index: 1;
    }

    .circuit-nodes {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
    }

    .circuit-node {
      position: absolute;
      width: 4px;
      height: 4px;
      background: rgba(59, 130, 246, 0.6);
      border-radius: 50%;
      animation: nodeGlow 3s ease-in-out infinite;
    }

    .circuit-node:nth-child(1) { top: 15%; left: 20%; animation-delay: 0s; }
    .circuit-node:nth-child(2) { top: 35%; left: 70%; animation-delay: 0.5s; }
    .circuit-node:nth-child(3) { top: 55%; left: 25%; animation-delay: 1s; }
    .circuit-node:nth-child(4) { top: 75%; left: 80%; animation-delay: 1.5s; }
    .circuit-node:nth-child(5) { top: 25%; left: 50%; animation-delay: 2s; }
    .circuit-node:nth-child(6) { top: 65%; left: 60%; animation-delay: 2.5s; }

    @keyframes circuitFlow {
      0% { background-position: 0px 0px; }
      100% { background-position: 30px 30px; }
    }

    @keyframes nodeGlow {
      0%, 100% { 
        opacity: 0.3; 
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
      }
      50% { 
        opacity: 1; 
        transform: scale(1.5);
        box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
      }
    }

    /* 🧠 REGION: AI - Agent Lee Header */
    .agent-header {
      position: relative;
      height: 100px;
      background: linear-gradient(135deg, #1e40af 0%, #3730a3 50%, #374151 100%);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 15px;
      cursor: grab;
      z-index: 10;
      border-bottom: 1px solid rgba(59, 130, 246, 0.2);
    }

    .agent-header:active {
      cursor: grabbing;
    }

    .agent-avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: linear-gradient(135deg, #3b82f6, #1e40af);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      color: white;
      margin-bottom: 6px;
      box-shadow: 0 8px 25px rgba(0,0,0,0.3), 0 0 0 2px rgba(59, 130, 246, 0.3);
      border: 2px solid rgba(255,255,255,0.2);
      font-weight: 500;
    }

    .agent-name {
      font-size: 16px;
      font-weight: bold;
      color: white;
      text-shadow: 0 2px 10px rgba(0,0,0,0.5);
      margin-bottom: 2px;
    }

    .agent-status {
      font-size: 10px;
      color: rgba(255,255,255,0.8);
      text-align: center;
    }

    /* Window Controls */
    .window-controls {
      position: absolute;
      top: 8px;
      right: 10px;
      display: flex;
      gap: 5px;
      z-index: 1000;
    }

    .window-control {
      width: 18px;
      height: 18px;
      border-radius: 50%;
      border: none;
      cursor: pointer;
      font-size: 10px;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }

    .window-control:hover {
      transform: scale(1.2);
    }

    .minimize-btn { background: #fbbf24; }
    .expand-btn { background: #10b981; }
    .close-btn { background: #ef4444; }

    /* Settings Wheel */
    .settings-wheel {
      background: rgba(59, 130, 246, 0.2);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(59, 130, 246, 0.3);
    }

    .settings-wheel:hover {
      background: rgba(59, 130, 246, 0.3);
      animation: rotate 2s linear infinite;
    }

    @keyframes rotate {
      from { transform: rotate(0deg) scale(1.2); }
      to { transform: rotate(360deg) scale(1.2); }
    }

    /* 🟠 REGION: UTIL - Integrated Feature Strip */
    .feature-strip {
      position: absolute;
      bottom: 8px;
      left: 10px;
      right: 10px;
      display: flex;
      justify-content: space-between;
      gap: 4px;
      z-index: 1000;
      padding: 0 5px;
    }<!DOCTYPE html>
<html lang="en">
<head>
  <!-- 🔴 REGION: SEO -->
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Agent Lee™</title>
  <link rel="manifest" href="data:application/json,{%22name%22:%22Agent%20Lee%22,%22short_name%22:%22AgentLee%22,%22display%22:%22standalone%22,%22background_color%22:%22transparent%22}">
  
  <style>
    /* 🟢 REGION: CORE - Clean Floating Interface */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    html, body {
      background: transparent !important;
      overflow: hidden;
      width: 100% !important;
      height: 100% !important;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    /* 🔵 REGION: UI - Professional Phone Design */
    .phone-container {
      width: 320px;
      height: 640px;
      background: linear-gradient(145deg, #1e293b 0%, #0f172a 100%);
      border-radius: 25px;
      box-shadow: 0 20px 60px rgba(0,0,0,0.8), 0 0 0 2px rgba(59, 130, 246, 0.2);
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      overflow: hidden;
      cursor: move;
      z-index: 10000;
      transition: all 0.3s ease;
      resize: both;
      min-width: 300px;
      min-height: 580px;
      max-width: 450px;
      max-height: 800px;
      border: 1px solid rgba(59, 130, 246, 0.3);
    }

    .phone-container:hover {
      box-shadow: 0 25px 80px rgba(0,0,0,0.9), 0 0 0 2px rgba(59, 130, 246, 0.4);
    }

    /* Animated Circuit Board Background */
    .circuit-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: 
        radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(30, 41, 59, 0.8) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
      opacity: 0.3;
      z-index: 1;
    }

    .circuit-lines {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: 
        linear-gradient(90deg, transparent 49%, rgba(59, 130, 246, 0.1) 50%, transparent 51%),
        linear-gradient(0deg, transparent 49%, rgba(59, 130, 246, 0.1) 50%, transparent 51%);
      background-size: 30px 30px;
      animation: circuitFlow 20s linear infinite;
      z-index: 1;
    }

    .circuit-nodes {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
    }

    .circuit-node {
      position: absolute;
      width: 4px;
      height: 4px;
      background: rgba(59, 130, 246, 0.6);
      border-radius: 50%;
      animation: nodeGlow 3s ease-in-out infinite;
    }

    .circuit-node:nth-child(1) { top: 15%; left: 20%; animation-delay: 0s; }
    .circuit-node:nth-child(2) { top: 35%; left: 70%; animation-delay: 0.5s; }
    .circuit-node:nth-child(3) { top: 55%; left: 25%; animation-delay: 1s; }
    .circuit-node:nth-child(4) { top: 75%; left: 80%; animation-delay: 1.5s; }
    .circuit-node:nth-child(5) { top: 25%; left: 50%; animation-delay: 2s; }
    .circuit-node:nth-child(6) { top: 65%; left: 60%; animation-delay: 2.5s; }

    @keyframes circuitFlow {
      0% { background-position: 0px 0px; }
      100% { background-position: 30px 30px; }
    }

    @keyframes nodeGlow {
      0%, 100% { 
        opacity: 0.3; 
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
      }
      50% { 
        opacity: 1; 
        transform: scale(1.5);
        box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
      }
    }

    /* 🧠 REGION: AI - Agent Lee Header */
    .agent-header {
      position: relative;
      height: 100px;
      background: linear-gradient(135deg, #1e40af 0%, #3730a3 50%, #374151 100%);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 15px;
      cursor: grab;
      z-index: 10;
      border-bottom: 1px solid rgba(59, 130, 246, 0.2);
    }

    .agent-header:active {
      cursor: grabbing;
    }

    .agent-avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: linear-gradient(135deg, #3b82f6, #1e40af);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      color: white;
      margin-bottom: 6px;
      box-shadow: 0 8px 25px rgba(0,0,0,0.3), 0 0 0 2px rgba(59, 130, 246, 0.3);
      border: 2px solid rgba(255,255,255,0.2);
      font-weight: 500;
    }

    .agent-name {
      font-size: 16px;
      font-weight: bold;
      color: white;
      text-shadow: 0 2px 10px rgba(0,0,0,0.5);
      margin-bottom: 2px;
    }

    .agent-status {
      font-size: 10px;
      color: rgba(255,255,255,0.8);
      text-align: center;
    }

    /* Window Controls */
    .window-controls {
      position: absolute;
      top: 8px;
      right: 10px;
      display: flex;
      gap: 5px;
      z-index: 1000;
    }

    .window-control {
      width: 18px;
      height: 18px;
      border-radius: 50%;
      border: none;
      cursor: pointer;
      font-size: 10px;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }

    .window-control:hover {
      transform: scale(1.2);
    }

    .minimize-btn { background: #fbbf24; }
    .expand-btn { background: #10b981; }
    .close-btn { background: #ef4444; }

    /* Settings Wheel */
    .settings-wheel {
      background: rgba(59, 130, 246, 0.2);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(59, 130, 246, 0.3);
    }

    .settings-wheel:hover {
      background: rgba(59, 130, 246, 0.3);
      animation: rotate 2s linear infinite;
    }

    @keyframes rotate {
      from { transform: rotate(0deg) scale(1.2); }
      to { transform: rotate(360deg) scale(1.2); }
    }

    /* 🟠 REGION: UTIL - Integrated Feature Strip */
    .feature-strip {
      position: absolute;
      bottom: 8px;
      left: 10px;
      right: 10px;
      display: flex;
      justify-content: space-between;
      gap: 4px;
      z-index: 1000;
      padding: 0 5px;
    }

    .feature-tag {
      background: rgba(30, 41, 59, 0.8);
      backdrop-filter: blur(15px);
      border: 1px solid rgba(59, 130, 246, 0.2);
      border-radius: 8px;
      padding: 4px 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 3px;
      font-size: 9px;
      color: rgba(255, 255, 255, 0.9);
      font-weight: 500;
      text-shadow: 0 1px 2px rgba(0,0,0,0.8);
      flex: 1;
      justify-content: center;
      box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

    .feature-tag:hover {
      background: rgba(59, 130, 246, 0.3);
      border-color: rgba(59, 130, 246, 0.5);
      transform: translateY(-1px);
      box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        0 4px 12px rgba(59, 130, 246, 0.2);
    }

    .feature-tag:active {
      transform: translateY(0);
    }

    .feature-icon {
      font-size: 11px;
      filter: drop-shadow(0 1px 2px rgba(0,0,0,0.5));
    }

    /* 🌍 REGION: LANG - User Language Indicator */
    .user-language-indicator {
      position: absolute;
      top: 8px;
      left: 10px;
      display: flex;
      align-items: center;
      gap: 4px;
      background: rgba(30, 41, 59, 0.8);
      backdrop-filter: blur(15px);
      padding: 4px 8px;
      border-radius: 8px;
      z-index: 1000;
      border: 1px solid rgba(59, 130, 246, 0.2);
      box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

    .user-language-flag {
      font-size: 12px;
      line-height: 1;
    }

    .user-language-code {
      font-size: 9px;
      font-weight: bold;
      color: white;
      text-shadow: 0 1px 2px rgba(0,0,0,0.8);
      letter-spacing: 0.5px;
    }    /* 🔵 REGION: UI - Echo Launcher Content Area */
    .phone-body {
      flex: 1;
      background: rgba(0, 0, 0, 0.4);
      backdrop-filter: blur(20px);
      color: white;
      display: flex;
      flex-direction: column;
      height: calc(100% - 100px);
      padding: 15px;
      position: relative;
      z-index: 10;
      border-top: 1px solid rgba(59, 130, 246, 0.1);
    }

    .echo-launcher {
      flex: 1;
      position: relative;
      overflow: hidden;
      border-radius: 12px;
      margin-bottom: 15px;
      background: rgba(15, 23, 42, 0.6);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(59, 130, 246, 0.1);
    }

    .launcher-carousel {
      display: flex;
      transition: transform 0.3s ease;
      height: 100%;
    }

    .launcher-page {
      min-width: 100%;
      padding: 15px;
      display: flex;
      flex-direction: column;
    }

    .page-title {
      font-size: 16px;
      font-weight: bold;
      color: #e2e8f0;
      margin-bottom: 15px;
      text-align: center;
      padding-bottom: 8px;
      border-bottom: 1px solid rgba(59, 130, 246, 0.2);
    }

    .device-label {
      font-size: 12px;
      color: #94a3b8;
      text-align: center;
      margin-bottom: 15px;
    }

    .app-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 12px;
      flex: 1;
    }

    .app-tile {
      background: rgba(30, 41, 59, 0.6);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(59, 130, 246, 0.2);
      border-radius: 12px;
      padding: 12px 8px;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 6px;
      min-height: 80px;
      justify-content: center;
      box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05);
    }

    .app-tile:hover {
      background: rgba(59, 130, 246, 0.2);
      border-color: rgba(59, 130, 246, 0.4);
      transform: translateY(-2px);
      box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 4px 15px rgba(59, 130, 246, 0.2);
    }

    .app-tile.detected {
      border-color: #10b981;
      box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.05),
        0 0 10px rgba(16, 185, 129, 0.3);
    }

    .app-icon {
      font-size: 24px;
      margin-bottom: 4px;
      filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
    }

    .app-name {
      font-size: 10px;
      color: #e2e8f0;
      text-align: center;
      line-height: 1.2;
      font-weight: 500;
      text-shadow: 0 1px 2px rgba(0,0,0,0.8);
    }

    .device-tag {
      font-size: 8px;
      color: #94a3b8;
      background: rgba(30, 41, 59, 0.8);
      padding: 2px 6px;
      border-radius: 4px;
      margin-top: 2px;
      border: 1px solid rgba(59, 130, 246, 0.1);
    }

    /* Navigation & Controls */
    .nav-dots {
      display: flex;
      justify-content: center;
      gap: 8px;
      margin-bottom: 15px;
    }

    .nav-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: rgba(74, 85, 104, 0.8);
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid rgba(59, 130, 246, 0.2);
    }

    .nav-dot.active {
      background: #3b82f6;
      transform: scale(1.2);
      box-shadow: 0 0 8px rgba(59, 130, 246, 0.6);
    }

    /* Professional Voice Control Section */
    .voice-control-section {
      background: rgba(30, 41, 59, 0.8);
      backdrop-filter: blur(15px);
      padding: 15px;
      border-radius: 12px;
      border: 1px solid rgba(59, 130, 246, 0.2);
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 4px 15px rgba(0,0,0,0.2);
      margin-bottom: 35px;
    }

    .voice-label {
      font-size: 14px;
      font-weight: 500;
      color: #e2e8f0;
      text-shadow: 0 1px 2px rgba(0,0,0,0.8);
    }

    .voice-main-btn {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      border: none;
      color: white;
      padding: 10px 16px;
      border-radius: 10px;
      cursor: pointer;
      font-size: 16px;
      transition: all 0.2s ease;
      min-width: 50px;
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      box-shadow: 
        0 2px 10px rgba(16, 185, 129, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(16, 185, 129, 0.4);
    }    .voice-main-btn:hover {
      transform: scale(1.05);
      box-shadow: 
        0 4px 20px rgba(16, 185, 129, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }

    .voice-main-btn.listening {
      background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
      animation: pulse 1s infinite;
      border-color: rgba(220, 38, 38, 0.4);
      box-shadow: 
        0 2px 10px rgba(220, 38, 38, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    @keyframes pulse {
      0%, 100% { 
        opacity: 1; 
        box-shadow: 
          0 0 0 0 rgba(220, 38, 38, 0.7),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
      }
      50% { 
        opacity: 0.8;
        box-shadow: 
          0 0 0 10px rgba(220, 38, 38, 0),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
      }
    }

    /* Language Selection */
    .language-selector {
      background: rgba(30, 41, 59, 0.8);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(59, 130, 246, 0.3);
      color: white;
      padding: 6px 10px;
      border-radius: 8px;
      font-size: 11px;
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

    .language-selector:hover {
      background: rgba(59, 130, 246, 0.2);
      border-color: rgba(59, 130, 246, 0.5);
    }

    .language-selector:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 0 0 2px rgba(59, 130, 246, 0.2);
    }

    /* Collapsed State */
    .phone-container.collapsed {
      height: 60px;
      width: 200px;
    }

    .phone-container.collapsed .phone-body {
      display: none;
    }

    .phone-container.collapsed .agent-header {
      height: 60px;
      flex-direction: row;
      padding: 10px;
    }

    .phone-container.collapsed .agent-avatar {
      width: 40px;
      height: 40px;
      font-size: 12px;
      margin-bottom: 0;
      margin-right: 10px;
    }

    /* Resize Handle */
    .resize-handle {
      position: absolute;
      bottom: 5px;
      right: 5px;
      width: 15px;
      height: 15px;
      background: linear-gradient(135deg, #374151, #1f2937);
      border-radius: 0 0 20px 0;
      cursor: se-resize;
      opacity: 0.5;
      z-index: 1000;
    }

    .resize-handle:hover {
      opacity: 1;
    }

    /* 🟪 REGION: FLOATING WINDOWS - Spawned GUI Cards */
    .gui-card {
      position: fixed;
      background: linear-gradient(145deg, #1e293b 0%, #0f172a 100%);
      border-radius: 15px;
      box-shadow: 0 20px 60px rgba(0,0,0,0.4), 0 0 0 1px rgba(59, 130, 246, 0.2);
      z-index: 10001;
      overflow: hidden;
      min-width: 280px;
      min-height: 200px;
      color: white;
      resize: both;
      border: 1px solid rgba(59, 130, 246, 0.3);
    }

    .gui-card-header {
      padding: 15px 20px;
      background: linear-gradient(90deg, #1e40af, #3730a3);
      cursor: move;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: white;
      font-size: 16px;
      font-weight: 600;
      border-bottom: 1px solid rgba(59, 130, 246, 0.2);
    }

    .gui-card-controls {
      display: flex;
      gap: 8px;
    }

    .gui-card-control {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      border: none;
      cursor: pointer;
      font-size: 12px;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
    }    .gui-card-minimize {
      background: #fbbf24;
    }

    .gui-card-close {
      background: #ef4444;
    }

    .gui-card-content {
      padding: 20px;
      height: calc(100% - 65px);
      overflow: auto;
      color: #e2e8f0;
      background: rgba(15, 23, 42, 0.4);
    }

    /* Voice Status Indicator */
    .voice-status {
      position: absolute;
      bottom: 50px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(30, 41, 59, 0.9);
      backdrop-filter: blur(15px);
      color: white;
      padding: 8px 15px;
      border-radius: 20px;
      font-size: 12px;
      opacity: 0;
      transition: opacity 0.3s ease;
      border: 1px solid rgba(59, 130, 246, 0.3);
      box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    }

    .voice-status.show {
      opacity: 1;
    }

    /* Professional Button Styling */
    .professional-btn {
      background: rgba(30, 41, 59, 0.8);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(59, 130, 246, 0.3);
      color: #e2e8f0;
      padding: 12px 20px;
      border-radius: 10px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

    .professional-btn:hover {
      background: rgba(59, 130, 246, 0.2);
      border-color: rgba(59, 130, 246, 0.5);
      transform: translateY(-1px);
      box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        0 4px 15px rgba(59, 130, 246, 0.2);
    }

    .professional-btn:active {
      transform: translateY(0);
    }

    .professional-btn.primary {
      background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
      border-color: rgba(29, 78, 216, 0.5);
    }

    .professional-btn.primary:hover {
      background: linear-gradient(135deg, #1d4ed8 0%, #1e3a8a 100%);
    }
  </style>
</head>
<body>
  <!-- 🟢 REGION: CORE - Main Agent Lee Phone Interface -->
  <div class="phone-container" id="phone-interface">
    
    <!-- Animated Circuit Board Background -->
    <div class="circuit-background"></div>
    <div class="circuit-lines"></div>
    <div class="circuit-nodes">
      <div class="circuit-node"></div>
      <div class="circuit-node"></div>
      <div class="circuit-node"></div>
      <div class="circuit-node"></div>
      <div class="circuit-node"></div>
      <div class="circuit-node"></div>
    </div>

    <!-- Window Controls with Settings Wheel -->
    <div class="window-controls">
      <button class="window-control minimize-btn" id="minimize-btn" title="Minimize">−</button>
      <button class="window-control expand-btn" id="expand-btn" title="Expand">□</button>
      <button class="window-control settings-wheel" id="settings-btn" title="Settings" onclick="window.open('settings.html', '_blank')"><button class="window-control settings-wheel" id="settings-btn" title="Settings">⚙️</button></button>
      <button class="window-control close-btn" id="close-btn" title="Close">×</button>
    </div>

    <!-- 🌍 REGION: LANG - User Language Indicator -->
    <div class="user-language-indicator">
      <span class="user-language-flag" id="user-language-flag">🇺🇸</span>
      <span class="user-language-code" id="user-language-code">EN</span>
    </div>

    <!-- 🧠 REGION: AI - Agent Lee Header -->
    <div class="agent-header" id="agent-header">
      <div class="agent-avatar" id="agent-avatar">🤖</div>
      <div class="agent-name">Agent Lee™</div>
      <div class="agent-status" id="agent-status">All Systems Ready</div>
    </div>    <!-- 🔵 REGION: UI - Echo Launcher Body -->
    <div class="phone-body" id="phone-body">
      
      <!-- Echo Launcher Content Area -->
      <div class="echo-launcher">
        <div class="launcher-carousel" id="launcher-carousel">
          
          <!-- Page 1: My Computer Apps -->
          <div class="launcher-page">
            <div class="page-title">My Computer</div>
            <div class="device-label">Desktop Applications</div>
            <div class="app-grid">
              <div class="app-tile detected" data-app="chrome">
                <div class="app-icon">🌐</div>
                <div class="app-name">Chrome Echo</div>
              </div>
              <div class="app-tile detected" data-app="word">
                <div class="app-icon">📝</div>
                <div class="app-name">Word Echo</div>
              </div>
              <div class="app-tile detected" data-app="explorer">
                <div class="app-icon">📁</div>
                <div class="app-name">Explorer Echo</div>
              </div>
              <div class="app-tile" data-app="outlook">
                <div class="app-icon">📧</div>
                <div class="app-name">Outlook Echo</div>
              </div>
              <div class="app-tile" data-app="teams">
                <div class="app-icon">💬</div>
                <div class="app-name">Teams Echo</div>
              </div>
              <div class="app-tile" data-app="vscode">
                <div class="app-icon">💻</div>
                <div class="app-name">VS Code Echo</div>
              </div>
            </div>
          </div>
          
          <!-- Page 2: My Phone Apps -->
          <div class="launcher-page">
            <div class="page-title">My Phone</div>
            <div class="device-label">Mobile Applications</div>
            <div class="app-grid">
              <div class="app-tile detected" data-app="whatsapp">
                <div class="app-icon">💬</div>
                <div class="app-name">WhatsApp Echo</div>
                <div class="device-tag">iPhone</div>
              </div>
              <div class="app-tile detected" data-app="photos">
                <div class="app-icon">📷</div>
                <div class="app-name">Photos Echo</div>
                <div class="device-tag">iPhone</div>
              </div>
              <div class="app-tile" data-app="instagram">
                <div class="app-icon">📸</div>
                <div class="app-name">Instagram Echo</div>
                <div class="device-tag">iPhone</div>
              </div>
              <div class="app-tile" data-app="spotify">
                <div class="app-icon">🎵</div>
                <div class="app-name">Spotify Echo</div>
                <div class="device-tag">iPhone</div>
              </div>
              <div class="app-tile" data-app="maps">
                <div class="app-icon">🗺️</div>
                <div class="app-name">Maps Echo</div>
                <div class="device-tag">iPhone</div>
              </div>
              <div class="app-tile" data-app="messages">
                <div class="app-icon">💭</div>
                <div class="app-name">Messages Echo</div>
                <div class="device-tag">iPhone</div>
              </div>
            </div>
          </div>          <!-- Page 3: My Tablet Apps -->
          <div class="launcher-page">
            <div class="page-title">My Tablet</div>
            <div class="device-label">Tablet Applications</div>
            <div class="app-grid">
              <div class="app-tile" data-app="notion">
                <div class="app-icon">📋</div>
                <div class="app-name">Notion Echo</div>
                <div class="device-tag">iPad</div>
              </div>
              <div class="app-tile" data-app="procreate">
                <div class="app-icon">🎨</div>
                <div class="app-name">Procreate Echo</div>
                <div class="device-tag">iPad</div>
              </div>
              <div class="app-tile" data-app="netflix">
                <div class="app-icon">📺</div>
                <div class="app-name">Netflix Echo</div>
                <div class="device-tag">iPad</div>
              </div>
              <div class="app-tile" data-app="kindle">
                <div class="app-icon">📚</div>
                <div class="app-name">Kindle Echo</div>
                <div class="device-tag">iPad</div>
              </div>
              <div class="app-tile" data-app="zoom">
                <div class="app-icon">📹</div>
                <div class="app-name">Zoom Echo</div>
                <div class="device-tag">iPad</div>
              </div>
              <div class="app-tile" data-app="adobe">
                <div class="app-icon">🔧</div>
                <div class="app-name">Adobe Echo</div>
                <div class="device-tag">iPad</div>
              </div>
            </div>
          </div>
          
          <!-- Page 4: My Top Echoes -->
          <div class="launcher-page">
            <div class="page-title">My Top Echoes</div>
            <div class="device-label">Most Used Applications</div>
            <div class="app-grid">
              <div class="app-tile detected" data-app="chrome-top">
                <div class="app-icon">🌐</div>
                <div class="app-name">Chrome Echo</div>
                <div class="device-tag">Computer</div>
              </div>
              <div class="app-tile detected" data-app="whatsapp-top">
                <div class="app-icon">💬</div>
                <div class="app-name">WhatsApp Echo</div>
                <div class="device-tag">Phone</div>
              </div>
              <div class="app-tile detected" data-app="outlook-top">
                <div class="app-icon">📧</div>
                <div class="app-name">Outlook Echo</div>
                <div class="device-tag">Computer</div>
              </div>
              <div class="app-tile detected" data-app="instagram-top">
                <div class="app-icon">📸</div>
                <div class="app-name">Instagram Echo</div>
                <div class="device-tag">Phone</div>
              </div>
              <div class="app-tile detected" data-app="notion-top">
                <div class="app-icon">📋</div>
                <div class="app-name">Notion Echo</div>
                <div class="device-tag">Tablet</div>
              </div>
              <div class="app-tile detected" data-app="vscode-top">
                <div class="app-icon">💻</div>
                <div class="app-name">VS Code Echo</div>
                <div class="device-tag">Computer</div>
              </div>
            </div>
          </div>
          
        </div>
      </div>
      
      <!-- Navigation Dots -->
      <div class="nav-dots">
        <div class="nav-dot active" id="nav-dot-0"></div>
        <div class="nav-dot" id="nav-dot-1"></div>
        <div class="nav-dot" id="nav-dot-2"></div>
        <div class="nav-dot" id="nav-dot-3"></div>
      </div>
      
      <!-- Voice Control Section -->
      <div class="voice-control-section">
        <div style="display: flex; align-items: center; gap: 12px;">
          <span class="voice-label">Voice Control</span>
          <select class="language-selector" id="language-selector"><option value="en-US">🇺🇸 English (US)</option>
<option value="es-ES">🇪🇸 Español</option>
<option value="fr-FR">🇫🇷 Français</option>
<option value="de-DE">🇩🇪 Deutsch</option>
<option value="pt-BR">🇧🇷 Português</option>
<option value="ru-RU">🇷🇺 Русский</option>
<option value="ja-JP">🇯🇵 日本語</option>
<option value="ko-KR">🇰🇷 한국어</option>
<option value="zh-CN">🇨🇳 中文</option></select>
</div>
        <button class="voice-main-btn" id="voice-main-btn">🎤</button>
      </div>
      
    </div>

    <!-- 🟠 REGION: UTIL - Integrated Feature Strip at Bottom -->
    <div class="feature-strip">
      <button class="feature-tag" id="weather-btn" title="Weather">
        <span class="feature-icon">🌤️</span>
        <span>Weather</span>
      </button>
      <button class="feature-tag" id="news-btn" title="News">
        <span class="feature-icon">📰</span>
        <span>News</span>
      </button>
      <button class="feature-tag" id="traffic-btn" title="Traffic">
        <span class="feature-icon">🚗</span>
        <span>Traffic</span>
      </button>
      <button class="feature-tag" id="translate-btn" title="Translate">
        <span class="feature-icon">🌐</span>
        <span>Translate</span>
      </button>
    </div>    <!-- Resize Handle -->
    <div class="resize-handle" id="resize-handle"></div>
  </div>

  <!-- Voice Status Indicator -->
  <div class="voice-status" id="voice-status">Ready to listen...</div>

  <script><script>
    // 🟢 REGION: CORE - Agent Lee Echo Engine
    
    class AgentLeeEchoEngine {
      constructor() {
        this.currentPage = 0;
        this.totalPages = 4;
        this.isListening = false;
        this.isCollapsed = false;
        this.recognition = null;
        this.synthesis = window.speechSynthesis;
        this.currentLanguage = 'en-US';
        this.guiManager = new GUIManager();
        this.voiceStatusTimeout = null;
        this.init();
      }
      
      async init() {
        console.log('🚀 Agent Lee Echo Engine Initializing...');
        
        try {
          await this.initVoiceSystem();
          await this.initEventListeners();
          await this.makeDraggable();
          await this.initSwipeNavigation();
          
          this.updateStatus('All Systems Ready');
          this.showVoiceStatus('Voice system ready - Click microphone to start', 3000);
          console.log('✅ Agent Lee Echo Engine Ready');
        } catch (error) {
          console.error('❌ Failed to initialize Agent Lee:', error);
          this.updateStatus('System Error');
        }
      }
      
      async initVoiceSystem() {
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
          this.showVoiceStatus('Speech recognition not supported in this browser', 5000);
          return;
        }
        
        const SpeechRecognition = window.webkitSpeechRecognition || window.SpeechRecognition;
        this.recognition = new SpeechRecognition();
        
        this.recognition.continuous = false;
        this.recognition.interimResults = false;
        this.recognition.lang = this.currentLanguage;
        this.recognition.maxAlternatives = 1;
        
        this.recognition.onstart = () => {
          this.isListening = true;
          document.getElementById('voice-main-btn').classList.add('listening');
          document.getElementById('voice-main-btn').innerHTML = '🔴';
          this.updateStatus('Listening...');
          this.showVoiceStatus('Listening... Speak now!');
        };
        
        this.recognition.onresult = (event) => {
          const transcript = event.results[0][0].transcript;
          const confidence = event.results[0][0].confidence;
          
          console.log('Voice Command:', transcript, 'Confidence:', confidence);
          this.showVoiceStatus(`Heard: "${transcript}"`, 3000);
          this.processVoiceCommand(transcript);
        };
        
        this.recognition.onerror = (event) => {
          console.error('Speech recognition error:', event.error);
          this.showVoiceStatus(`Voice error: ${event.error}`, 3000);
          this.stopListening();
        };
        
        this.recognition.onend = () => {
          this.stopListening();
        };
      }
      
      showVoiceStatus(message, duration = 2000) {
        const statusEl = document.getElementById('voice-status');
        statusEl.textContent = message;
        statusEl.classList.add('show');
        
        if (this.voiceStatusTimeout) {
          clearTimeout(this.voiceStatusTimeout);
        }
        
        this.voiceStatusTimeout = setTimeout(() => {
          statusEl.classList.remove('show');
        }, duration);
      }
      
      startListening() {
        if (!this.recognition) {
          this.showVoiceStatus('Voice recognition not available', 3000);
          return;
        }
        
        if (this.isListening) {
          this.stopListening();
          return;
        }
        
        try {
          this.recognition.start();
        } catch (error) {
          console.error('Failed to start listening:', error);
          this.showVoiceStatus('Failed to start listening', 3000);
        }
      }
      
      stopListening() {
        this.isListening = false;
        const btn = document.getElementById('voice-main-btn');
        btn.classList.remove('listening');
        btn.innerHTML = '🎤';
        this.updateStatus('All Systems Ready');
        
        if (this.recognition) {
          this.recognition.stop();
        }
      }
      
      processVoiceCommand(command) {
        this.updateStatus(`Processing: "${command}"`);
        
        const cmd = command.toLowerCase();
        
        if (cmd.includes('weather')) {
          this.spawnWeatherGUI();
          this.speak('Opening weather information');
        } else if (cmd.includes('news')) {
          this.spawnNewsGUI();
          this.speak('Opening news dashboard');
        } else if (cmd.includes('traffic')) {
          this.spawnTrafficGUI();
          this.speak('Opening traffic information');
        } else if (cmd.includes('translate') || cmd.includes('translation')) {
          this.spawnTranslationGUI();
          this.speak('Opening translation system');
        } else if (cmd.includes('settings')) {
          this.spawnSettingsGUI();
          this.speak('Opening settings panel');
        } else if (cmd.includes('voice') || cmd.includes('dashboard')) {
          this.spawnVoiceDashboard();
          this.speak('Opening voice dashboard');
        } else if (cmd.includes('page') && cmd.includes('next')) {
          this.nextPage();
          this.speak('Next page');
        } else if (cmd.includes('page') && cmd.includes('previous')) {
          this.prevPage();
          this.speak('Previous page');
        } else if (cmd.includes('chrome') || cmd.includes('browser')) {
          this.launchEcho('chrome');
        } else if (cmd.includes('word') || cmd.includes('document')) {
          this.launchEcho('word');
        } else {
          this.speak(`I heard: ${command}. How can I help you?`);
        }
        
        setTimeout(() => {
          this.updateStatus('All Systems Ready');
        }, 2000);
      }
      
      speak(text) {
        if (!this.synthesis) {
          console.log('Speech synthesis not available');
          return;
        }
        
        // Cancel any ongoing speech
        this.synthesis.cancel();
        
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = this.currentLanguage;
        utterance.rate = 0.9;
        utterance.pitch = 1.0;
        utterance.volume = 0.8;
        
        utterance.onstart = () => {
          this.updateStatus('Speaking...');
        };
        
        utterance.onend = () => {
          this.updateStatus('All Systems Ready');
        };
        
        this.synthesis.speak(utterance);
      }
      
      switchLanguage(language) {
        this.currentLanguage = language;
        
        if (this.recognition) {
          this.recognition.lang = language;
        }
        
        // Update UI indicators
        const langInfo = this.getLanguageInfo(language);
        if (langInfo) {
          document.getElementById('user-language-flag').textContent = langInfo.flag;
          document.getElementById('user-language-code').textContent = langInfo.code;
        }
        
        this.speak(`Language switched to ${langInfo.name}`);
      }
      
      getLanguageInfo(language) {
        const languages = {
          'en-US': { flag: '🇺🇸', name: 'English', code: 'EN' },
          'es-ES': { flag: '🇪🇸', name: 'Spanish', code: 'ES' },
          'fr-FR': { flag: '🇫🇷', name: 'French', code: 'FR' },
          'de-DE': { flag: '🇩🇪', name: 'German', code: 'DE' },
          'pt-BR': { flag: '🇧🇷', name: 'Portuguese', code: 'PT' },
          'ru-RU': { flag: '🇷🇺', name: 'Russian', code: 'RU' },
          'ja-JP': { flag: '🇯🇵', name: 'Japanese', code: 'JA' },
          'ko-KR': { flag: '🇰🇷', name: 'Korean', code: 'KO' },
          'zh-CN': { flag: '🇨🇳', name: 'Chinese', code: 'ZH' }
        };
        return languages[language] || languages['en-US'];
      }
      
      async initEventListeners() {
        // Window controls
        document.getElementById('minimize-btn').addEventListener('click', () => this.toggleCollapse());
        document.getElementById('expand-btn').addEventListener('click', () => this.toggleExpand());
        document.getElementById('close-btn').addEventListener('click', () => this.closeAgent());
        document.getElementById('settings-btn').addEventListener('click', () => this.spawnSettingsGUI());
        
        // Feature buttons
        document.getElementById('weather-btn').addEventListener('click', () => this.spawnWeatherGUI());
        document.getElementById('news-btn').addEventListener('click', () => this.spawnNewsGUI());
        document.getElementById('traffic-btn').addEventListener('click', () => this.spawnTrafficGUI());
        document.getElementById('translate-btn').addEventListener('click', () => this.spawnTranslationGUI());
        
        // Voice control
        document.getElementById('voice-main-btn').addEventListener('click', () => this.startListening());
        
        // Language selector
        document.getElementById('language-selector').addEventListener('change', (e) => {
          this.switchLanguage(e.target.value);
        });
        
        // Navigation dots
        for (let i = 0; i < this.totalPages; i++) {
          document.getElementById(`nav-dot-${i}`).addEventListener('click', () => this.goToPage(i));
        }
        
        // App tiles
        document.querySelectorAll('.app-tile').forEach(tile => {
          tile.addEventListener('click', () => this.launchEcho(tile.dataset.app));
        });
      }
      
      // GUI Spawning Functions
      spawnWeatherGUI() {
        const content = `
          <div style="text-align: center;">
            <div style="font-size: 48px; margin-bottom: 20px; color: #3b82f6;">72°F</div>
            <div style="font-size: 18px; margin-bottom: 10px;">Milwaukee, Wisconsin</div>
            <div style="color: #94a3b8; margin-bottom: 20px;">Partly Cloudy</div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
              <div style="background: #0f172a; padding: 12px; border-radius: 8px; text-align: center;">
                <div style="font-size: 12px; color: #64748b;">HUMIDITY</div>
                <div style="font-size: 18px; font-weight: bold;">68%</div>
              </div>
              <div style="background: #0f172a; padding: 12px; border-radius: 8px; text-align: center;">
                <div style="font-size: 12px; color: #64748b;">WIND</div>
                <div style="font-size: 18px; font-weight: bold;">12 mph</div>
              </div>
            </div>
          </div>
        `;
        this.guiManager.createGUI(content, '🌤️ Weather', 300, 250);
      }
      
      spawnNewsGUI() {
        const content = `
          <div>
            <div style="background: #0f172a; padding: 15px; border-radius: 8px; margin-bottom: 15px; border-left: 4px solid #ef4444;">
              <div style="font-weight: bold; margin-bottom: 8px;">Tech Giants Report Strong Q4 Earnings</div>
              <div style="font-size: 14px; color: #94a3b8;">Major technology companies exceeded expectations in their latest quarterly reports...</div>
            </div>
            <div style="background: #0f172a; padding: 15px; border-radius: 8px; margin-bottom: 15px; border-left: 4px solid #10b981;">
              <div style="font-weight: bold; margin-bottom: 8px;">Climate Summit Reaches Agreement</div>
              <div style="font-size: 14px; color: #94a3b8;">World leaders announce new framework for environmental cooperation...</div>
            </div>
          </div>
        `;
        this.guiManager.createGUI(content, '📰 News Dashboard', 350, 300);
      }
      
      spawnTrafficGUI() {
        const content = `
          <div>
            <div style="text-align: center; margin-bottom: 20px;">
              <div style="font-size: 18px; font-weight: bold;">Milwaukee Traffic</div>
              <div style="color: #94a3b8; font-size: 14px;">Live conditions</div>
            </div>
            <div style="background: #0f172a; padding: 15px; border-radius: 8px; margin-bottom: 15px; border-left: 4px solid #10b981;">
              <div style="font-weight: bold; margin-bottom: 8px;">I-94 East to Downtown</div>
              <div style="display: flex; justify-content: space-between;">
                <div style="color: #10b981; font-weight: bold;">12 min</div>
                <div style="background: #10b981; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Light</div>
              </div>
            </div>
          </div>
        `;
        this.guiManager.createGUI(content, '🚗 Traffic Info', 320, 250);
      }
      
      spawnTranslationGUI() {
        const content = `
          <div style="padding: 15px;">
            <div style="margin-bottom: 20px;">
              <select class="language-selector" style="width: 100%; margin-bottom: 10px;">
                <option>Auto-Detect Source</option>
                <option>English</option>
                <option>Spanish</option>
                <option>French</option>
                <option>German</option>
              </select>
              <select class="language-selector" style="width: 100%;">
                <option>English</option>
                <option>Spanish</option>
                <option>French</option>
                <option>German</option>
              </select>
            </div>
            <textarea placeholder="Speak or type text to translate..." style="width: 100%; height: 80px; background: #000; border: 1px solid #4b5563; border-radius: 6px; color: white; padding: 10px; margin-bottom: 15px;"></textarea>
            <textarea placeholder="Translation will appear here..." readonly style="width: 100%; height: 80px; background: #0f172a; border: 1px solid #4b5563; border-radius: 6px; color: #10b981; padding: 10px; margin-bottom: 15px;"></textarea>
            <div style="display: flex; gap: 10px;">
              <button class="professional-btn" style="flex: 1;">🎤 Record</button>
              <button class="professional-btn" style="flex: 1;">🌐 Translate</button>
              <button class="professional-btn" style="flex: 1;">🔊 Speak</button>
            </div>
          </div>
        `;
        this.guiManager.createGUI(content, '🌐 Universal Translator', 400, 400);
      }
      
      spawnSettingsGUI() {
        const content = `
          <div>
            <div style="margin-bottom: 20px;">
              <h3 style="color: #3b82f6; margin-bottom: 15px;">Agent Lee Settings</h3>
              <div style="background: #0f172a; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                <label style="display: flex; align-items: center; margin-bottom: 10px;">
                  <input type="checkbox" checked style="margin-right: 8px;">
                  Voice Recognition Enabled
                </label>
                <label style="display: flex; align-items: center; margin-bottom: 10px;">
                  <input type="checkbox" checked style="margin-right: 8px;">
                  Auto-Launch Echo Apps
                </label>
                <label style="display: flex; align-items: center;">
                  <input type="checkbox" style="margin-right: 8px;">
                  Always On Top
                </label>
              </div>
              <div style="display: grid; gap: 10px; margin-bottom: 15px;">
                <button class="professional-btn" id="btn-todo-list" style="width: 100%;">📋 To-Do List</button>
                <button class="professional-btn" id="btn-agent-center" style="width: 100%;">🤖 Agent Center</button>
                <button class="professional-btn" id="btn-database" style="width: 100%;">🗄️ Database Manager</button>
                <button class="professional-btn" id="btn-llm" style="width: 100%;">🧠 LLM Brain</button>
                <button class="professional-btn" id="btn-workers" style="width: 100%;">⚙️ Workers Center</button>
              </div>
              <div style="font-size: 12px; color: #94a3b8; text-align: center; margin-top: 5px;">Agent Lee v1.0.2</div>
            </div>
          </div>
        `;
        const settingsGUI = this.guiManager.createGUI(content, '⚙️ Settings Panel', 350, 450);
        
        // Add event listeners for buttons
        if (settingsGUI) {
          const todoBtn = settingsGUI.querySelector('#btn-todo-list');
          if (todoBtn) {
            todoBtn.addEventListener('click', () => {
              this.loadDiagnosticTool('uvatg81isz.html', '📋 To-Do List');
            });
          }
          
          const agentBtn = settingsGUI.querySelector('#btn-agent-center');
          if (agentBtn) {
            agentBtn.addEventListener('click', () => {
              this.loadDiagnosticTool('7qwzcbv3up.html', '🤖 Agent Center');
            });
          }
          
          const dbBtn = settingsGUI.querySelector('#btn-database');
          if (dbBtn) {
            dbBtn.addEventListener('click', () => {
              this.loadDiagnosticTool('b8bwgglju1.html', '🗄️ Database Manager');
            });
          }
          
          const llmBtn = settingsGUI.querySelector('#btn-llm');
          if (llmBtn) {
            llmBtn.addEventListener('click', () => {
              this.loadDiagnosticTool('buhlvyi8so.html', '🧠 LLM Brain');
            });
          }
          
          const workersBtn = settingsGUI.querySelector('#btn-workers');
          if (workersBtn) {
            workersBtn.addEventListener('click', () => {
              this.loadDiagnosticTool('2fjr3w3whb.html', '⚙️ Workers Center');
            });
          }
        }
      }
      
      loadDiagnosticTool(filePath, title) {
        // Create an iframe to load the diagnostic tool embedded in the GUI
        const content = `
          <div style="height: 100%; width: 100%; display: flex; flex-direction: column;">
            <iframe src="${filePath}" style="width: 100%; height: 100%; border: none; flex: 1;"></iframe>
          </div>
        `;
        
        this.guiManager.createGUI(content, title, 900, 700);
      }
      
      spawnVoiceDashboard() {
        const content = `
          <div style="padding: 15px;">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
              <button class="professional-btn primary">🎤 Start Listening</button>
              <button class="professional-btn">🔇 Stop Speech</button>
              <button class="professional-btn">⌨️ Keyboard</button>
              <button class="professional-btn">⚙️ Voice Settings</button>
            </div>
            <div style="background: #0f172a; padding: 15px; border-radius: 8px;">
              <div style="font-weight: bold; margin-bottom: 10px;">Voice Status</div>
              <div style="color: #10b981;">Ready to listen</div>
              <div style="font-size: 12px; color: #94a3b8; margin-top: 5px;">Language: ${this.getLanguageInfo(this.currentLanguage).name}</div>
            </div>
          </div>
        `;
        this.guiManager.createGUI(content, '🎤 Voice Dashboard', 300, 250);
      }
      
      launchEcho(appName) {
        this.updateStatus(`Launching ${appName} Echo...`);
        this.speak(`Launching ${appName} Echo`);
        
        const content = `
          <div style="text-align: center; padding: 20px;">
            <div style="font-size: 48px; margin-bottom: 20px;">🖥️</div>
            <h3 style="color: #3b82f6; margin-bottom: 15px;">${appName} Echo</h3>
            <div style="background: #0f172a; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
              <div style="font-size: 14px; color: #94a3b8;">Status: Connected</div>
              <div style="font-size: 14px; color: #10b981;">Mirror: Active</div>
            </div>
            <div style="background: #000; height: 150px; border-radius: 8px; display: flex; align-items: center; justify-content: center; border: 2px dashed #4b5563;">
              <div style="color: #64748b;">App Mirror Area</div>
            </div>
          </div>
        `;
        this.guiManager.createGUI(content, `🖥️ ${appName} Echo`, 400, 350);
      }
      
      // Navigation functions
      async initSwipeNavigation() {
        const carousel = document.getElementById('launcher-carousel');
        let startX = 0;
        let currentX = 0;
        let isDragging = false;
        
        carousel.addEventListener('touchstart', (e) => {
          startX = e.touches[0].clientX;
          isDragging = true;
        });
        
        carousel.addEventListener('touchmove', (e) => {
          if (!isDragging) return;
          currentX = e.touches[0].clientX;
          const diff = startX - currentX;
          
          if (Math.abs(diff) > 50) {
            if (diff > 0 && this.currentPage < this.totalPages - 1) {
              this.nextPage();
            } else if (diff < 0 && this.currentPage > 0) {
              this.prevPage();
            }
            isDragging = false;
          }
        });
        
        carousel.addEventListener('touchend', () => {
          isDragging = false;
        });
        
        // Mouse events for desktop
        carousel.addEventListener('mousedown', (e) => {
          startX = e.clientX;
          isDragging = true;
        });
        
        carousel.addEventListener('mousemove', (e) => {
          if (!isDragging) return;
          currentX = e.clientX;
          const diff = startX - currentX;
          
          if (Math.abs(diff) > 100) {
            if (diff > 0 && this.currentPage < this.totalPages - 1) {
              this.nextPage();
            } else if (diff < 0 && this.currentPage > 0) {
              this.prevPage();
            }
            isDragging = false;
          }
        });
        
        carousel.addEventListener('mouseup', () => {
          isDragging = false;
        });
      }
      
      nextPage() {
        if (this.currentPage < this.totalPages - 1) {
          this.currentPage++;
          this.updateCarousel();
        }
      }
      
      prevPage() {
        if (this.currentPage > 0) {
          this.currentPage--;
          this.updateCarousel();
        }
      }
      
      goToPage(page) {
        this.currentPage = page;
        this.updateCarousel();
      }
      
      updateCarousel() {
        const carousel = document.getElementById('launcher-carousel');
        const translateX = -this.currentPage * 100;
        carousel.style.transform = `translateX(${translateX}%)`;
        
        // Update navigation dots
        for (let i = 0; i < this.totalPages; i++) {
          const dot = document.getElementById(`nav-dot-${i}`);
          if (i === this.currentPage) {
            dot.classList.add('active');
          } else {
            dot.classList.remove('active');
          }
        }
      }
      
      // Window management
      toggleCollapse() {
        this.isCollapsed = !this.isCollapsed;
        const phone = document.getElementById('phone-interface');
        
        if (this.isCollapsed) {
          phone.classList.add('collapsed');
          this.updateStatus('Minimized');
        } else {
          phone.classList.remove('collapsed');
          this.updateStatus('All Systems Ready');
        }
      }
      
      toggleExpand() {
        const phone = document.getElementById('phone-interface');
        if (phone.style.width === '450px') {
          phone.style.width = '320px';
          phone.style.height = '640px';
        } else {
          phone.style.width = '450px';
          phone.style.height = '750px';
        }
      }
      
      closeAgent() {
        if (confirm('Close Agent Lee Echo Engine?')) {
          document.getElementById('phone-interface').style.display = 'none';
        }
      }
      
      updateStatus(message) {
        document.getElementById('agent-status').textContent = message;
      }
      
      // Draggable functionality
      async makeDraggable() {
        let isDragging = false;
        let currentX = 0, currentY = 0, initialX = 0, initialY = 0;
        
        const dragHandle = document.getElementById('agent-header');
        const phone = document.getElementById('phone-interface');
        
        dragHandle.addEventListener('mousedown', dragStart);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', dragEnd);
        
        function dragStart(e) {
          initialX = e.clientX - currentX;
          initialY = e.clientY - currentY;
          isDragging = true;
        }
        
        function drag(e) {
          if (isDragging) {
            e.preventDefault();
            currentX = e.clientX - initialX;
            currentY = e.clientY - initialY;
            
            phone.style.transform = `translate(calc(-50% + ${currentX}px), calc(-50% + ${currentY}px))`;
          }
        }
        
        function dragEnd() {
          isDragging = false;
        }
      }
    }
    
    // 🟪 REGION: FLOATING WINDOWS - GUI Management System
    class GUIManager {
      constructor() {
        this.guis = [];
        this.zIndex = 10001;
        this.spawnOffset = 0;
      }
      
      createGUI(content, title, width = 300, height = 400) {
        const id = `gui-${Date.now()}`;
        const left = 450 + this.spawnOffset;
        const top = 100 + this.spawnOffset;
        
        const guiHtml = `
          <div id="${id}" class="gui-card" style="width:${width}px;height:${height}px;top:${top}px;left:${left}px;">
            <div class="gui-card-header">
              <span>${title}</span>
              <div class="gui-card-controls">
                <button class="gui-card-control gui-card-minimize">−</button>
                <button class="gui-card-control gui-card-close">×</button>
              </div>
            </div>
            <div class="gui-card-content">${content}</div>
          </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', guiHtml);
        const guiEl = document.getElementById(id);
        
        this.makeDraggable(guiEl.querySelector('.gui-card-header'));
        
        guiEl.querySelector('.gui-card-close').addEventListener('click', () => {
          guiEl.remove();
          this.guis = this.guis.filter(gui => gui !== guiEl);
        });
        
        guiEl.querySelector('.gui-card-minimize').addEventListener('click', () => {
          if (guiEl.style.height === '65px') {
            guiEl.style.height = height + 'px';
          } else {
            guiEl.style.height = '65px';
          }
        });
        
        this.focusGUI(guiEl);
        this.guis.push(guiEl);
        
        // Increment spawn offset for next GUI
        this.spawnOffset += 30;
        if (this.spawnOffset > 150) this.spawnOffset = 0;
        
        return guiEl;
      }
      
      makeDraggable(header) {
        let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
        header.onmousedown = dragMouseDown;
        
        function dragMouseDown(e) {
          e.preventDefault();
          pos3 = e.clientX;
          pos4 = e.clientY;
          document.onmouseup = closeDragElement;
          document.onmousemove = elementDrag;
        }
        
        function elementDrag(e) {
          e.preventDefault();
          pos1 = pos3 - e.clientX;
          pos2 = pos4 - e.clientY;
          pos3 = e.clientX;
          pos4 = e.clientY;
          const element = header.parentElement;
          element.style.top = (element.offsetTop - pos2) + "px";
          element.style.left = (element.offsetLeft - pos1) + "px";
        }
        
        function closeDragElement() {
          document.onmouseup = null;
          document.onmousemove = null;
        }
      }
      
      focusGUI(guiEl) {
        this.zIndex += 1;
        guiEl.style.zIndex = this.zIndex;
        guiEl.addEventListener('mousedown', () => {
          this.zIndex += 1;
          guiEl.style.zIndex = this.zIndex;
        });
      }
    }
    
    // 🟢 REGION: CORE - Initialize Agent Lee Echo Engine
    let agentLee;
    
    document.addEventListener('DOMContentLoaded', () => {
      console.log('🚀 Launching Agent Lee Echo Engine...');
      agentLee = new AgentLeeEchoEngine();
    });
    
    if (document.readyState !== 'loading') {
      agentLee = new AgentLeeEchoEngine();
    }
  </script></script>
</body>
</html>
