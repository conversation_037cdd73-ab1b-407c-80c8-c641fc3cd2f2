<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Agent Lee™ - Advanced Cognitive AI Assistant with Self-Healing Architecture">
    <meta name="keywords" content="<PERSON>, Assistant, Cognitive Architecture, Agent Lee, Machine Learning">
    <meta name="author" content="Agent Lee Development Team">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://agentlee.ai/">
    <meta property="og:title" content="Agent Lee™ - Cognitive AI Assistant">
    <meta property="og:description" content="Advanced Cognitive AI Assistant with Self-Healing Architecture">
    <meta property="og:image" content="/assets/agent-lee-og.png">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://agentlee.ai/">
    <meta property="twitter:title" content="Agent Lee™ - Cognitive AI Assistant">
    <meta property="twitter:description" content="Advanced Cognitive AI Assistant with Self-Healing Architecture">
    <meta property="twitter:image" content="/assets/agent-lee-twitter.png">
    
    <!-- Apple Touch Icon (single, most important size) -->
    <link rel="apple-touch-icon" sizes="180x180" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='40' fill='%233b82f6'/><text x='50' y='60' text-anchor='middle' fill='white' font-size='40' font-family='Arial'>🧠</text></svg>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='40' fill='%233b82f6'/><text x='50' y='60' text-anchor='middle' fill='white' font-size='40' font-family='Arial'>🧠</text></svg>">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.webmanifest">
    
    <!-- Theme Color -->
    <meta name="theme-color" content="#3b82f6">
    <meta name="msapplication-TileColor" content="#3b82f6">
    
    <title>Agent Lee™ - Cognitive AI Assistant</title>
    
    <!-- Cognitive Architecture Scripts (Optional - will be loaded if available) -->
    <script>
        // Load cognitive scripts only if available
        const cognitiveScripts = [
            'frontend/js/cognitive/CognitiveArchitecture.js',
            'frontend/js/cognitive/CognitiveHypergraph.js',
            'frontend/js/cognitive/HematopoieticAgentFactory.js',
            'frontend/js/cognitive/CerebrospinalOptimizationEngine.js',
            'frontend/js/cognitive/CognitiveUIOptimizer.js',
            'frontend/js/cognitive/CognitiveUIDiagnostics.js',
            'frontend/js/cognitive/CognitiveVideoConference.js',
            'frontend/js/cognitive/CognitiveIntegration.js'
        ];

        // Load scripts asynchronously and handle errors gracefully
        cognitiveScripts.forEach(src => {
            const script = document.createElement('script');
            script.src = src;
            script.defer = true;
            script.onerror = () => {
                console.log(`Cognitive script not available: ${src}`);
            };
            document.head.appendChild(script);
        });
    </script>
    
    <style>
        /* Production-optimized CSS with cross-browser compatibility */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        html, body {
            background: transparent !important;
            overflow: hidden;
            width: 100% !important;
            height: 100% !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* Professional Phone Design */
        .phone-container {
            width: 320px;
            height: 640px;
            background: linear-gradient(145deg, #1e293b 0%, #0f172a 100%);
            border-radius: 25px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.8), 0 0 0 2px rgba(59, 130, 246, 0.2);
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            overflow: hidden;
            cursor: move;
            z-index: 10000;
            transition: all 0.3s ease;
            resize: both;
            min-width: 300px;
            min-height: 580px;
            max-width: 450px;
            max-height: 800px;
            border: 1px solid rgba(59, 130, 246, 0.3);
        }

        .phone-container:hover {
            box-shadow: 0 25px 80px rgba(0,0,0,0.9), 0 0 0 2px rgba(59, 130, 246, 0.4);
        }

        /* Cross-browser backdrop filter support */
        .backdrop-blur {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
        }

        /* Agent Lee Header */
        .agent-header {
            position: relative;
            height: 100px;
            background: linear-gradient(135deg, #1e40af 0%, #3730a3 50%, #374151 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 15px;
            cursor: grab;
            z-index: 10;
            border-bottom: 1px solid rgba(59, 130, 246, 0.2);
        }

        .agent-header:active {
            cursor: grabbing;
        }

        .agent-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            margin-bottom: 6px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.3), 0 0 0 2px rgba(59, 130, 246, 0.3);
            border: 2px solid rgba(255,255,255,0.2);
            font-weight: 500;
        }

        .agent-name {
            font-size: 16px;
            font-weight: bold;
            color: white;
            text-shadow: 0 2px 10px rgba(0,0,0,0.5);
            margin-bottom: 2px;
        }

        .agent-status {
            font-size: 10px;
            color: rgba(255,255,255,0.8);
            text-align: center;
        }

        /* Window Controls */
        .window-controls {
            position: absolute;
            top: 8px;
            right: 10px;
            display: flex;
            gap: 5px;
            z-index: 1000;
        }

        .window-control {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            font-size: 10px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            type: button;
        }

        .window-control:hover {
            transform: scale(1.2);
        }

        .minimize-btn { background: #fbbf24; }
        .expand-btn { background: #10b981; }
        .close-btn { background: #ef4444; }
        .settings-wheel { 
            background: rgba(59, 130, 246, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.3);
        }

        /* Phone Body */
        .phone-body {
            flex: 1;
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            color: white;
            display: flex;
            flex-direction: column;
            padding: 20px;
            gap: 15px;
        }

        /* Voice Control Section */
        .voice-control-section {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            padding: 15px;
            border-radius: 12px;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .voice-control-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 15px;
        }

        .voice-label {
            font-size: 14px;
            font-weight: 600;
            color: #e2e8f0;
        }

        .language-selector {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.3);
            color: white;
            padding: 6px 10px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            outline: none;
        }

        .voice-main-btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.4);
            margin: 0 auto;
            display: block;
            type: button;
        }

        .voice-main-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(220, 38, 38, 0.6);
        }

        /* Feature Strip */
        .feature-strip {
            position: absolute;
            bottom: 8px;
            left: 10px;
            right: 10px;
            display: flex;
            justify-content: space-between;
            gap: 4px;
            z-index: 1000;
            padding: 0 5px;
        }

        .feature-tag {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 8px;
            padding: 4px 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 3px;
            font-size: 9px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0,0,0,0.8);
            flex: 1;
            justify-content: center;
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
            border: none;
            type: button;
        }

        .feature-tag:hover {
            background: rgba(59, 130, 246, 0.3);
            border-color: rgba(59, 130, 246, 0.5);
            transform: translateY(-1px);
            box-shadow: 
                inset 0 1px 0 rgba(255, 255, 255, 0.2),
                0 4px 12px rgba(59, 130, 246, 0.2);
        }

        .feature-icon {
            font-size: 11px;
        }

        /* Cognitive Status Indicator */
        .cognitive-status {
            position: absolute;
            top: 20px;
            left: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(0, 0, 0, 0.3);
            padding: 6px 12px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
            font-size: 11px;
        }

        .cognitive-pulse {
            width: 8px;
            height: 8px;
            background: #00ff88;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { 
                opacity: 1; 
                transform: scale(1);
            }
            50% { 
                opacity: 0.5; 
                transform: scale(1.2);
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .phone-container {
                width: 100vw;
                height: 100vh;
                border-radius: 0;
                top: 0;
                left: 0;
                transform: none;
                position: fixed;
            }
        }

        /* Accessibility */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Loading State */
        .loading {
            opacity: 0.7;
            pointer-events: none;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid #3b82f6;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Cognitive Status Indicator -->
    <div class="cognitive-status" id="cognitive-status">
        <div class="cognitive-pulse"></div>
        <span>Cognitive AI Active</span>
    </div>

    <!-- Main Phone Interface -->
    <div class="phone-container" id="phone-interface" role="main" aria-label="Agent Lee AI Assistant Interface">
        <!-- Window Controls -->
        <div class="window-controls">
            <button class="window-control minimize-btn" id="minimize-btn" title="Minimize" type="button" aria-label="Minimize window">−</button>
            <button class="window-control expand-btn" id="expand-btn" title="Expand" type="button" aria-label="Expand window">□</button>
            <button class="window-control settings-wheel" id="settings-btn" title="Settings" type="button" aria-label="Open settings">⚙️</button>
            <button class="window-control close-btn" id="close-btn" title="Close" type="button" aria-label="Close application">×</button>
        </div>

        <!-- Agent Header -->
        <div class="agent-header" id="agent-header" role="banner">
            <div class="agent-avatar" aria-label="Agent Lee Avatar">🧠</div>
            <div class="agent-name">Agent Lee™</div>
            <div class="agent-status" id="agent-status">Cognitive AI Ready</div>
        </div>

        <!-- Phone Body -->
        <div class="phone-body">
            <!-- Voice Control Section -->
            <div class="voice-control-section">
                <div class="voice-control-header">
                    <span class="voice-label">Voice Control</span>
                    <select class="language-selector" id="language-selector" title="Select Language" aria-label="Language selection">
                        <option value="en-US">🇺🇸 English (US)</option>
                        <option value="es-ES">🇪🇸 Español</option>
                        <option value="fr-FR">🇫🇷 Français</option>
                        <option value="de-DE">🇩🇪 Deutsch</option>
                        <option value="it-IT">🇮🇹 Italiano</option>
                        <option value="pt-BR">🇧🇷 Português</option>
                        <option value="ru-RU">🇷🇺 Русский</option>
                        <option value="ja-JP">🇯🇵 日本語</option>
                        <option value="ko-KR">🇰🇷 한국어</option>
                        <option value="zh-CN">🇨🇳 中文</option>
                    </select>
                </div>
                <button class="voice-main-btn" id="voice-main-btn" type="button" aria-label="Voice control button">🎤</button>
            </div>
        </div>

        <!-- Feature Strip -->
        <div class="feature-strip">
            <button class="feature-tag" id="weather-btn" title="Weather" type="button" aria-label="Weather information">
                <span class="feature-icon">🌤️</span>
                <span>Weather</span>
            </button>
            <button class="feature-tag" id="news-btn" title="News" type="button" aria-label="News updates">
                <span class="feature-icon">📰</span>
                <span>News</span>
            </button>
            <button class="feature-tag" id="traffic-btn" title="Traffic" type="button" aria-label="Traffic information">
                <span class="feature-icon">🚗</span>
                <span>Traffic</span>
            </button>
            <button class="feature-tag" id="translate-btn" title="Translate" type="button" aria-label="Translation service">
                <span class="feature-icon">🌐</span>
                <span>Translate</span>
            </button>
        </div>
    </div>

    <!-- Screen reader announcements -->
    <div id="announcements" class="sr-only" aria-live="polite" aria-atomic="true"></div>

    <script>
        // Production-ready JavaScript with error handling
        (function() {
            'use strict';
            
            console.log('🧠 Agent Lee™ Cognitive AI Assistant - Production v3.0');
            
            // Global error handler
            window.addEventListener('error', function(e) {
                console.error('Global error:', e.error);
                announceToScreenReader('An error occurred. Please try again.');
            });

            // Unhandled promise rejection handler
            window.addEventListener('unhandledrejection', function(e) {
                console.error('Unhandled promise rejection:', e.reason);
                e.preventDefault();
            });

            // Screen reader announcements
            function announceToScreenReader(message) {
                const announcements = document.getElementById('announcements');
                if (announcements) {
                    announcements.textContent = message;
                }
            }

            // Initialize when DOM is ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initializeAgentLee);
            } else {
                initializeAgentLee();
            }

            function initializeAgentLee() {
                console.log('🚀 Initializing Agent Lee Cognitive System...');
                
                try {
                    // Initialize basic UI interactions
                    initializeWindowControls();
                    initializeVoiceControls();
                    initializeFeatureButtons();
                    initializeDragFunctionality();
                    
                    // Update status
                    updateAgentStatus('All Systems Ready');
                    announceToScreenReader('Agent Lee is ready');
                    
                    console.log('✅ Agent Lee initialization complete');
                    
                } catch (error) {
                    console.error('❌ Agent Lee initialization failed:', error);
                    updateAgentStatus('Initialization Error');
                    announceToScreenReader('Agent Lee failed to initialize');
                }
            }

            function initializeWindowControls() {
                const minimizeBtn = document.getElementById('minimize-btn');
                const expandBtn = document.getElementById('expand-btn');
                const settingsBtn = document.getElementById('settings-btn');
                const closeBtn = document.getElementById('close-btn');
                const phoneInterface = document.getElementById('phone-interface');

                if (minimizeBtn) {
                    minimizeBtn.addEventListener('click', function() {
                        phoneInterface.style.height = '100px';
                        announceToScreenReader('Window minimized');
                    });
                }

                if (expandBtn) {
                    expandBtn.addEventListener('click', function() {
                        phoneInterface.style.height = '640px';
                        announceToScreenReader('Window expanded');
                    });
                }

                if (settingsBtn) {
                    settingsBtn.addEventListener('click', function() {
                        announceToScreenReader('Settings opened');
                        // Settings functionality will be implemented by cognitive integration
                    });
                }

                if (closeBtn) {
                    closeBtn.addEventListener('click', function() {
                        if (confirm('Close Agent Lee?')) {
                            phoneInterface.style.display = 'none';
                            announceToScreenReader('Agent Lee closed');
                        }
                    });
                }
            }

            function initializeVoiceControls() {
                const voiceBtn = document.getElementById('voice-main-btn');
                const languageSelector = document.getElementById('language-selector');

                if (voiceBtn) {
                    voiceBtn.addEventListener('click', function() {
                        console.log('🎤 Voice control activated');
                        announceToScreenReader('Voice control activated');
                        // Voice functionality will be implemented by cognitive integration
                    });
                }

                if (languageSelector) {
                    languageSelector.addEventListener('change', function() {
                        console.log('🌐 Language changed to:', this.value);
                        announceToScreenReader('Language changed to ' + this.options[this.selectedIndex].text);
                    });
                }
            }

            function initializeFeatureButtons() {
                const featureButtons = document.querySelectorAll('.feature-tag');
                
                featureButtons.forEach(function(button) {
                    button.addEventListener('click', function() {
                        const feature = this.querySelector('span:last-child').textContent;
                        console.log('🔧 Feature activated:', feature);
                        announceToScreenReader(feature + ' activated');
                        // Feature functionality will be implemented by cognitive integration
                    });
                });
            }

            function initializeDragFunctionality() {
                const phoneInterface = document.getElementById('phone-interface');
                const agentHeader = document.getElementById('agent-header');
                
                if (!phoneInterface || !agentHeader) return;

                let isDragging = false;
                let currentX = 0;
                let currentY = 0;
                let initialX = 0;
                let initialY = 0;

                agentHeader.addEventListener('mousedown', function(e) {
                    initialX = e.clientX - currentX;
                    initialY = e.clientY - currentY;
                    isDragging = true;
                    phoneInterface.style.cursor = 'grabbing';
                });

                document.addEventListener('mousemove', function(e) {
                    if (isDragging) {
                        e.preventDefault();
                        currentX = e.clientX - initialX;
                        currentY = e.clientY - initialY;
                        
                        phoneInterface.style.transform = `translate(calc(-50% + ${currentX}px), calc(-50% + ${currentY}px))`;
                    }
                });

                document.addEventListener('mouseup', function() {
                    isDragging = false;
                    phoneInterface.style.cursor = 'move';
                });
            }

            function updateAgentStatus(status) {
                const statusElement = document.getElementById('agent-status');
                if (statusElement) {
                    statusElement.textContent = status;
                }
            }

        })();
    </script>
</body>
</html>
