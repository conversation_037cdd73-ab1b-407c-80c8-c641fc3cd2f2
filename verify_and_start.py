#!/usr/bin/env python3
"""
Agent Lee™ Production Verification and Startup Script
Comprehensive verification and clean startup for production deployment
"""

import os
import sys
import time
import subprocess
import platform
import webbrowser
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("agentlee_verification.log")
    ]
)

logger = logging.getLogger("agentlee.verification")

class AgentLeeVerification:
    """Production verification and startup system"""
    
    def __init__(self):
        self.start_time = time.time()
        self.backend_process = None
        self.verification_passed = False
        self.issues_found = []
        
        logger.info("🔍 Agent Lee™ Production Verification System")
        logger.info("=" * 60)
    
    def run_complete_verification(self):
        """Run complete verification process"""
        logger.info("🚀 Starting comprehensive verification...")
        
        try:
            # Phase 1: System Requirements
            if not self.verify_system_requirements():
                return False
            
            # Phase 2: File Structure
            if not self.verify_file_structure():
                return False
            
            # Phase 3: Dependencies
            if not self.verify_dependencies():
                return False
            
            # Phase 4: Configuration
            if not self.verify_configuration():
                return False
            
            # Phase 5: Code Quality
            if not self.verify_code_quality():
                return False
            
            self.verification_passed = True
            logger.info("✅ All verifications passed!")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Verification failed: {e}")
            return False
    
    def verify_system_requirements(self):
        """Verify system requirements"""
        logger.info("🔍 Verifying system requirements...")
        
        # Check Python version
        python_version = sys.version_info
        if python_version < (3, 8):
            self.issues_found.append(f"Python 3.8+ required, found {python_version[0]}.{python_version[1]}")
            return False
        
        logger.info(f"✅ Python {python_version[0]}.{python_version[1]}.{python_version[2]}")
        
        # Check platform
        system = platform.system()
        logger.info(f"✅ Platform: {system} {platform.release()}")
        
        # Check available memory
        try:
            import psutil
            memory = psutil.virtual_memory()
            memory_gb = memory.total / (1024**3)
            logger.info(f"✅ Available Memory: {memory_gb:.1f} GB")
            
            if memory_gb < 2:
                logger.warning("⚠️ Low memory detected. Performance may be affected.")
        except ImportError:
            logger.warning("⚠️ Cannot check memory - psutil not available")
        
        return True
    
    def verify_file_structure(self):
        """Verify required file structure"""
        logger.info("📁 Verifying file structure...")
        
        required_files = [
            "backend/agentlee_controller_v2.py",
            "backend/config.py", 
            "backend/models.py",
            "backend/llm_service.py",
            "backend/requirements_minimal.txt",
            "index_production.html",
            "manifest.webmanifest"
        ]
        
        required_dirs = [
            "backend",
            "backend/services",
            "frontend/js/cognitive"
        ]
        
        missing_files = []
        missing_dirs = []
        
        # Check files
        for file_path in required_files:
            if not Path(file_path).exists():
                missing_files.append(file_path)
        
        # Check directories
        for dir_path in required_dirs:
            if not Path(dir_path).exists():
                missing_dirs.append(dir_path)
        
        if missing_files:
            logger.error(f"❌ Missing files: {missing_files}")
            self.issues_found.extend(missing_files)
            return False
        
        if missing_dirs:
            logger.error(f"❌ Missing directories: {missing_dirs}")
            self.issues_found.extend(missing_dirs)
            return False
        
        logger.info("✅ File structure verified")
        return True
    
    def verify_dependencies(self):
        """Verify minimal dependencies"""
        logger.info("📦 Verifying dependencies...")
        
        minimal_deps = [
            ('fastapi', 'fastapi'),
            ('uvicorn', 'uvicorn'),
            ('pydantic', 'pydantic'),
            ('sqlalchemy', 'sqlalchemy'),
            ('psutil', 'psutil'),
            ('requests', 'requests'),
            ('python-dotenv', 'dotenv'),
            ('google-generativeai', 'google.generativeai'),
            ('pyttsx3', 'pyttsx3')
        ]
        
        missing_deps = []
        
        for package_name, import_name in minimal_deps:
            try:
                __import__(import_name)
                logger.info(f"✅ {package_name}")
            except ImportError:
                missing_deps.append(package_name)
                logger.warning(f"❌ {package_name} - Missing")
        
        if missing_deps:
            logger.error(f"❌ Missing dependencies: {missing_deps}")
            logger.info("💡 Install with: pip install -r backend/requirements_minimal.txt")
            self.issues_found.extend(missing_deps)
            return False
        
        logger.info("✅ All minimal dependencies satisfied")
        return True
    
    def verify_configuration(self):
        """Verify configuration"""
        logger.info("⚙️ Verifying configuration...")
        
        # Check .env file
        env_file = Path(".env")
        if not env_file.exists():
            logger.warning("⚠️ No .env file found - using defaults")
        else:
            logger.info("✅ .env file found")
        
        # Check environment variables
        required_env_vars = ["GEMINI_API_KEY"]
        missing_env_vars = []
        
        for var in required_env_vars:
            if not os.getenv(var):
                missing_env_vars.append(var)
        
        if missing_env_vars:
            logger.warning(f"⚠️ Missing environment variables: {missing_env_vars}")
            logger.info("💡 Set these in your .env file or environment")
        
        return True
    
    def verify_code_quality(self):
        """Verify code quality and syntax"""
        logger.info("🔍 Verifying code quality...")
        
        # Check main backend file
        backend_file = Path("backend/agentlee_controller_v2.py")
        if backend_file.exists():
            try:
                with open(backend_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Basic syntax check
                compile(content, str(backend_file), 'exec')
                logger.info("✅ Backend code syntax valid")
                
            except SyntaxError as e:
                logger.error(f"❌ Syntax error in backend: {e}")
                self.issues_found.append(f"Syntax error: {e}")
                return False
            except Exception as e:
                logger.warning(f"⚠️ Could not verify backend syntax: {e}")
        
        # Check production HTML
        html_file = Path("index_production.html")
        if html_file.exists():
            try:
                with open(html_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Basic HTML validation
                if "<!DOCTYPE html>" in content and "</html>" in content:
                    logger.info("✅ Production HTML structure valid")
                else:
                    logger.warning("⚠️ Production HTML may have structure issues")
                    
            except Exception as e:
                logger.warning(f"⚠️ Could not verify HTML: {e}")
        
        return True
    
    def start_production_system(self):
        """Start the production system"""
        if not self.verification_passed:
            logger.error("❌ Cannot start - verification failed")
            return False
        
        logger.info("🚀 Starting Agent Lee Production System...")
        
        try:
            # Start backend
            if not self.start_backend():
                return False
            
            # Wait for backend
            if not self.wait_for_backend():
                return False
            
            # Open frontend
            self.open_frontend()
            
            # Display success info
            self.display_success_info()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Startup failed: {e}")
            return False
    
    def start_backend(self):
        """Start the backend server"""
        logger.info("🔧 Starting backend server...")
        
        try:
            backend_dir = Path("backend")
            
            cmd = [
                sys.executable, "-m", "uvicorn",
                "agentlee_controller_v2:app",
                "--host", "localhost",
                "--port", "8000",
                "--log-level", "info"
            ]
            
            self.backend_process = subprocess.Popen(
                cmd,
                cwd=backend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True
            )
            
            logger.info("✅ Backend server started")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start backend: {e}")
            return False
    
    def wait_for_backend(self, timeout=30):
        """Wait for backend to be ready"""
        logger.info("⏳ Waiting for backend to be ready...")
        
        import requests
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get("http://localhost:8000/", timeout=5)
                if response.status_code == 200:
                    logger.info("✅ Backend is ready")
                    return True
            except requests.exceptions.RequestException:
                pass
            
            time.sleep(1)
        
        logger.error("❌ Backend failed to start within timeout")
        return False
    
    def open_frontend(self):
        """Open the frontend"""
        logger.info("🌐 Opening Agent Lee frontend...")
        
        try:
            # Use production HTML
            frontend_url = "http://localhost:8000/app"
            webbrowser.open(frontend_url)
            logger.info(f"✅ Frontend opened: {frontend_url}")
        except Exception as e:
            logger.error(f"❌ Failed to open frontend: {e}")
            logger.info("💡 Manual access: http://localhost:8000/app")
    
    def display_success_info(self):
        """Display success information"""
        uptime = time.time() - self.start_time
        
        print("\n" + "="*60)
        print("🧠 AGENT LEE™ PRODUCTION SYSTEM - READY")
        print("="*60)
        print(f"🚀 Status: OPERATIONAL")
        print(f"⏱️  Startup Time: {uptime:.2f} seconds")
        print(f"🌐 Frontend: http://localhost:8000/app")
        print(f"🔧 API: http://localhost:8000")
        print(f"📚 Docs: http://localhost:8000/docs")
        print(f"🏥 Health: http://localhost:8000/api/system_status")
        print("="*60)
        print("💡 Press Ctrl+C to shutdown gracefully")
        print("="*60)
    
    def monitor_system(self):
        """Monitor the running system"""
        try:
            while True:
                if self.backend_process and self.backend_process.poll() is not None:
                    logger.error("❌ Backend process terminated unexpectedly")
                    break
                
                time.sleep(5)
                
        except KeyboardInterrupt:
            logger.info("🛑 Shutdown signal received")
            self.shutdown()
    
    def shutdown(self):
        """Graceful shutdown"""
        logger.info("🛑 Shutting down Agent Lee...")
        
        if self.backend_process:
            logger.info("🔧 Terminating backend process...")
            self.backend_process.terminate()
            
            try:
                self.backend_process.wait(timeout=10)
                logger.info("✅ Backend shutdown complete")
            except subprocess.TimeoutExpired:
                logger.warning("⚠️ Force killing backend process...")
                self.backend_process.kill()
        
        uptime = time.time() - self.start_time
        logger.info(f"✅ Agent Lee shutdown complete. Uptime: {uptime:.2f} seconds")
    
    def display_issues_summary(self):
        """Display summary of issues found"""
        if self.issues_found:
            print("\n" + "="*60)
            print("⚠️ ISSUES FOUND DURING VERIFICATION")
            print("="*60)
            for i, issue in enumerate(self.issues_found, 1):
                print(f"{i}. {issue}")
            print("="*60)
            print("💡 Please fix these issues before starting the system")
            print("="*60)

def main():
    """Main entry point"""
    verifier = AgentLeeVerification()
    
    try:
        # Run verification
        if verifier.run_complete_verification():
            # Start system
            if verifier.start_production_system():
                # Monitor system
                verifier.monitor_system()
            else:
                logger.error("❌ Failed to start production system")
                sys.exit(1)
        else:
            logger.error("❌ Verification failed")
            verifier.display_issues_summary()
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("🛑 Interrupted by user")
        verifier.shutdown()
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        verifier.shutdown()
        sys.exit(1)

if __name__ == "__main__":
    main()
