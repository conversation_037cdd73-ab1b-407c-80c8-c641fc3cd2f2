<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Lee™ Echo Engine - Full Screen Integrated</title>
    
    <!-- 🟢 REGION: CORE - Echo Engine System -->
    <!-- 🔗 MIGRATED FROM: /echo/index.html -->
    <!-- 🔍 ID: ECHO_CORE -->
    
    <!-- 🟣 REGION: MCP - Voice & Command System -->
    <!-- 🔗 MIGRATED FROM: /mcp/voice-echo.js -->
    <!-- 🔍 ID: MCP_ECHO -->
    <script>
        // MCP Command Runner for Echo Engine (In-Memory Storage)
        window.mcpEchoLogs = window.mcpEchoLogs || [];
        
        function runMCPCommand(command) {
            const timestamp = new Date().toISOString();
            const logEntry = { command, timestamp, status: 'executed', result: 'success' };
            
            window.mcpEchoLogs.push(logEntry);
            
            console.log(`🔄 Echo MCP Command: ${command}`);
            
            window.dispatchEvent(new CustomEvent('mcpEchoExecuted', { 
                detail: { command, timestamp, logEntry } 
            }));
            
            return logEntry;
        }
        
        // Voice System for Echo Engine
        let speechRecognition = null;
        let speechSynthesis = window.speechSynthesis;
        let isListening = false;
        
        function initEchoVoiceSystem() {
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                speechRecognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
                speechRecognition.continuous = false;
                speechRecognition.interimResults = false;
                speechRecognition.lang = 'en-US';
                
                speechRecognition.onerror = function(event) {
                    console.log('Echo speech recognition error:', event.error);
                    isListening = false;
                    window.dispatchEvent(new CustomEvent('echoVoiceError', { detail: event.error }));
                };
                
                speechRecognition.onend = function() {
                    isListening = false;
                    window.dispatchEvent(new CustomEvent('echoVoiceEnd'));
                };
            }
        }
        
        function startEchoListening(callback) {
            if (speechRecognition && !isListening) {
                isListening = true;
                speechRecognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    isListening = false;
                    callback(transcript);
                };
                speechRecognition.start();
                window.dispatchEvent(new CustomEvent('echoVoiceStart'));
            }
        }
        
        function echoSpeak(text, voice = 'Emma') {
            if (speechSynthesis) {
                const utterance = new SpeechSynthesisUtterance(text);
                const voices = speechSynthesis.getVoices();
                const selectedVoice = voices.find(v => v.name.includes(voice)) || voices[0];
                if (selectedVoice) utterance.voice = selectedVoice;
                utterance.rate = 0.9;
                utterance.pitch = 1.1;
                speechSynthesis.speak(utterance);
                
                window.dispatchEvent(new CustomEvent('echoSpoke', { detail: { text, voice } }));
            }
        }
    </script>

    <style>
        /* 🟠 REGION: UTIL - CSS Variables & Base Styles */
        /* 🔗 MIGRATED FROM: /styles/echo-variables.css */
        /* 🔍 ID: ECHO_VARS */
        :root {
            --echo-blue: #3b82f6;
            --echo-blue-dark: #1e40af;
            --echo-blue-darker: #3730a3;
            --echo-gray: #374151;
            --echo-dark: #1e293b;
            --echo-darker: #0f172a;
            --echo-green: #10b981;
            --echo-red: #ef4444;
            --echo-yellow: #fbbf24;
            --echo-white: #ffffff;
            --echo-light: #e2e8f0;
            --echo-glow: rgba(59, 130, 246, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, var(--echo-darker), var(--echo-dark));
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
            height: 100vh;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* 🔵 REGION: UI - Agent Lee Echo Engine Main Card */
        /* 🔗 MIGRATED FROM: /components/echo-card.html */
        /* 🔍 ID: ECHO_CARD */
        .agent-card {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: linear-gradient(145deg, #1e293b 0%, #0f172a 100%);
            border-radius: 0;
            box-shadow: 0 20px 60px rgba(0,0,0,0.8), 0 0 0 2px rgba(59, 130, 246, 0.3);
            border: 1px solid rgba(59, 130, 246, 0.2);
            z-index: 999997;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* Minimized State */
        .agent-card.minimized {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            top: 50px;
            right: 50px;
            left: auto;
            cursor: grab;
        }

        .agent-card.minimized .card-content {
            display: none;
        }

        .agent-card.minimized .minimized-bubble {
            display: flex;
        }

        /* Minimized Bubble */
        .minimized-bubble {
            display: none;
            width: 100%;
            height: 100%;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            border-radius: 50%;
            position: relative;
        }

        .bubble-face {
            font-size: 24px;
            animation: blink 3s infinite;
        }

        @keyframes blink {
            0%, 90%, 100% { transform: scale(1); }
            95% { transform: scale(0.9); }
        }

        /* Card Header */
        .card-header {
            background: linear-gradient(135deg, #1e40af 0%, #3730a3 50%, #374151 100%);
            padding: 25px 35px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: grab;
            border-bottom: 3px solid rgba(59, 130, 246, 0.3);
            position: relative;
            overflow: hidden;
        }

        .card-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: translateX(-100%);
            animation: scanline 8s linear infinite;
        }

        @keyframes scanline {
            0% { transform: translateX(-100%); }
            20%, 100% { transform: translateX(100%); }
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
            z-index: 2;
            position: relative;
        }

        .agent-avatar {
            width: 65px;
            height: 65px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.4);
            border: 3px solid rgba(255,255,255,0.2);
            position: relative;
            overflow: hidden;
        }

        .agent-avatar::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%);
            animation: avatarShine 4s infinite;
            transform: rotate(45deg);
        }

        @keyframes avatarShine {
            0% { transform: translateX(-200%) translateY(-200%) rotate(45deg); }
            50%, 100% { transform: translateX(200%) translateY(200%) rotate(45deg); }
        }

        .agent-face {
            font-size: 32px;
            animation: blink 3s infinite;
            z-index: 2;
            position: relative;
        }

        .agent-info {
            color: white;
            z-index: 2;
            position: relative;
        }

        .agent-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
            text-shadow: 0 0 20px var(--echo-blue);
            background: linear-gradient(45deg, #ffffff, var(--echo-blue));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .agent-status {
            font-size: 16px;
            color: rgba(255,255,255,0.8);
        }

        /* Header Controls */
        .header-controls {
            display: flex;
            gap: 12px;
            z-index: 2;
            position: relative;
        }

        .control-btn {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            font-size: 18px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .control-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .control-btn:hover::before {
            opacity: 1;
        }

        .minimize-btn { background: linear-gradient(135deg, #fbbf24, #f59e0b); }
        .close-btn { background: linear-gradient(135deg, #ef4444, #dc2626); }

        .control-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 25px rgba(0,0,0,0.4);
        }

        /* Card Content */
        .card-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 25px;
            gap: 20px;
            overflow-y: auto;
        }

        /* Chat Section */
        .chat-section {
            flex: 1;
            background: rgba(30, 41, 59, 0.6);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid rgba(59, 130, 246, 0.2);
            display: flex;
            flex-direction: column;
            min-height: 300px;
            position: relative;
            overflow: hidden;
        }

        .chat-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, transparent, var(--echo-blue), transparent);
            animation: scanline 5s linear infinite;
        }

        .chat-area {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 15px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            border: 2px solid rgba(59, 130, 246, 0.1);
        }

        .chat-area::-webkit-scrollbar {
            width: 8px;
        }

        .chat-area::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
        }

        .chat-area::-webkit-scrollbar-thumb {
            background: var(--echo-blue);
            border-radius: 4px;
        }

        .message {
            margin-bottom: 12px;
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 16px;
            line-height: 1.4;
            animation: messageSlide 0.4s ease-out;
        }

        @keyframes messageSlide {
            0% {
                opacity: 0;
                transform: translateY(15px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .agent-message {
            background: rgba(59, 130, 246, 0.2);
            color: #e2e8f0;
            border-left: 4px solid #3b82f6;
        }

        .user-message {
            background: rgba(16, 185, 129, 0.2);
            color: #e2e8f0;
            border-left: 4px solid #10b981;
            text-align: right;
        }

        .input-area {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            background: rgba(30, 41, 59, 0.8);
            border: 2px solid rgba(59, 130, 246, 0.3);
            border-radius: 12px;
            padding: 12px 16px;
            color: white;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
        }

        .message-input:focus {
            border-color: var(--echo-blue);
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
            background: rgba(30, 41, 59, 0.9);
        }

        .message-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .input-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            font-size: 18px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .input-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .input-btn:hover::before {
            opacity: 1;
        }

        .voice-btn {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
        }

        .send-btn {
            background: linear-gradient(135deg, #3b82f6, #1e40af);
        }

        .input-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 25px rgba(0,0,0,0.4);
        }

        .input-btn.active {
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* Echo Engine Section */
        .echo-engine {
            background: rgba(30, 41, 59, 0.6);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid rgba(59, 130, 246, 0.2);
            position: relative;
            overflow: hidden;
        }

        .echo-engine::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, transparent, var(--echo-green), transparent);
            animation: scanline 6s linear infinite;
        }

        .echo-title {
            color: white;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-shadow: 0 0 15px var(--echo-blue);
        }

        .device-tabs {
            display: flex;
            gap: 8px;
            margin-bottom: 20px;
            justify-content: center;
        }

        .device-tab {
            background: rgba(59, 130, 246, 0.2);
            border: 2px solid rgba(59, 130, 246, 0.3);
            border-radius: 10px;
            padding: 10px 16px;
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            font-weight: 600;
        }

        .device-tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .device-tab:hover::before {
            left: 100%;
        }

        .device-tab.active {
            background: rgba(59, 130, 246, 0.5);
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
        }

        .app-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 15px;
        }

        .app-tile {
            background: rgba(30, 41, 59, 0.8);
            border: 2px solid rgba(59, 130, 246, 0.3);
            border-radius: 12px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            text-align: center;
            position: relative;
            overflow: hidden;
            animation: appTileSlide 0.5s ease-out forwards;
            opacity: 0;
            transform: translateY(20px);
        }

        @keyframes appTileSlide {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .app-tile::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.1) 50%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .app-tile:hover::before {
            opacity: 1;
        }

        .app-tile:hover {
            background: rgba(59, 130, 246, 0.3);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 30px rgba(0,0,0,0.4), 0 0 20px rgba(59, 130, 246, 0.4);
            border-color: rgba(59, 130, 246, 0.6);
        }

        .app-tile:active {
            transform: translateY(-1px) scale(0.98);
            background: rgba(59, 130, 246, 0.5);
        }

        .app-icon {
            font-size: 24px;
            z-index: 2;
            position: relative;
        }

        .app-name {
            font-size: 14px;
            color: white;
            font-weight: 600;
            z-index: 2;
            position: relative;
        }

        /* Status Bar */
        .status-bar {
            background: rgba(15, 23, 42, 0.8);
            padding: 15px 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            border-top: 2px solid rgba(59, 130, 246, 0.2);
            position: relative;
            overflow: hidden;
        }

        .status-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.05), transparent);
            animation: statusScan 10s linear infinite;
        }

        @keyframes statusScan {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .listening-indicator {
            display: none;
            color: #ef4444;
            animation: pulse 1s infinite;
            font-weight: bold;
        }

        .listening-indicator.active {
            display: inline;
        }

        /* Echo Mirror Cards */
        .echo-mirror {
            position: fixed !important;
            width: 400px !important;
            height: 300px !important;
            background: linear-gradient(145deg, #1e293b 0%, #0f172a 100%) !important;
            border-radius: 12px !important;
            box-shadow: 0 20px 60px rgba(0,0,0,0.8), 0 0 0 2px rgba(59, 130, 246, 0.3) !important;
            border: 1px solid rgba(59, 130, 246, 0.2) !important;
            z-index: 999998 !important;
            cursor: move !important;
            display: flex !important;
            flex-direction: column !important;
            overflow: hidden !important;
            opacity: 1 !important;
            visibility: visible !important;
            transition: all 0.3s cubic-bezier(0.17, 0.84, 0.44, 1) !important;
        }

        .echo-mirror:hover {
            box-shadow: 0 30px 100px rgba(0,0,0,0.9), 0 0 0 3px rgba(59, 130, 246, 0.5) !important;
        }

        .echo-header {
            background: linear-gradient(135deg, #1e40af 0%, #3730a3 50%, #374151 100%) !important;
            padding: 8px 12px !important;
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            cursor: grab !important;
            border-bottom: 1px solid rgba(59, 130, 246, 0.3) !important;
            position: relative !important;
            overflow: hidden !important;
        }

        .echo-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: translateX(-100%);
            animation: scanline 8s linear infinite;
        }

        .echo-header:active {
            cursor: grabbing !important;
        }

        .echo-mirror-title {
            color: white !important;
            font-size: 11px !important;
            font-weight: bold !important;
            z-index: 2 !important;
            position: relative !important;
        }

        .echo-content {
            flex: 1 !important;
            position: relative !important;
            background: linear-gradient(180deg, #000000 0%, #1a1a2e 100%) !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            color: white !important;
            text-align: center !important;
            padding: 20px !important;
        }

        .echo-video {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .echo-placeholder {
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            justify-content: center !important;
            animation: echoGlow 2s ease-in-out infinite alternate !important;
            color: white !important;
            text-align: center !important;
            font-size: 11px !important;
            padding: 20px !important;
        }

        @keyframes echoGlow {
            0% { opacity: 0.8; }
            100% { opacity: 1; }
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .app-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        @media (max-width: 768px) {
            .card-header {
                padding: 20px 25px;
            }

            .agent-avatar {
                width: 50px;
                height: 50px;
            }

            .agent-name {
                font-size: 20px;
            }

            .card-content {
                padding: 20px;
                gap: 15px;
            }

            .app-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 10px;
            }

            .echo-mirror {
                width: 90vw;
                height: 60vh;
            }
        }
    </style>
</head>
<body>
    <!-- 🔵 REGION: UI - Agent Lee Echo Engine Main Card -->
    <!-- 🔗 MIGRATED FROM: /components/echo-main.html -->
    <!-- 🔍 ID: ECHO_MAIN -->
    <div class="agent-card" id="agentCard">
        <!-- Minimized Bubble -->
        <div class="minimized-bubble" id="minimizedBubble">
            <div class="bubble-face">🤖</div>
        </div>

        <!-- Full Card Content -->
        <div class="card-content" id="cardContent">
            <!-- Header -->
            <div class="card-header" id="cardHeader">
                <div class="header-left">
                    <div class="agent-avatar">
                        <div class="agent-face" id="agentFace">🤖</div>
                    </div>
                    <div class="agent-info">
                        <div class="agent-name">Agent Lee™ Echo Engine</div>
                        <div class="agent-status" id="agentStatus">Echo Engine Ready</div>
                    </div>
                </div>
                
                <div class="header-controls">
                    <button class="control-btn minimize-btn" id="minimizeBtn" title="Minimize">−</button>
                    <button class="control-btn close-btn" id="closeBtn" title="Close">×</button>
                </div>
            </div>

            <!-- Chat Section -->
            <div class="chat-section">
                <div class="chat-area" id="chatArea">
                    <div class="message agent-message">
                        🔄 Hello! I'm Agent Lee with Echo Engine. Click any app to mirror it in a floating window! The Echo Engine can mirror applications from multiple devices and create floating displays for seamless multitasking.
                    </div>
                </div>
                
                <div class="input-area">
                    <input type="text" class="message-input" id="messageInput" placeholder="Type to Agent Lee Echo Engine...">
                    <button class="input-btn voice-btn" id="voiceBtn" title="Voice">🎤</button>
                    <button class="input-btn send-btn" id="sendBtn" title="Send">➤</button>
                </div>

                <!-- Debug Controls -->
                <div style="display: flex; gap: 10px; margin-top: 10px; justify-content: center;">
                    <button onclick="window.echoEngine && window.echoEngine.debugInfo()" style="background: linear-gradient(135deg, #3b82f6, #1e40af); border: none; padding: 8px 12px; border-radius: 8px; color: white; font-size: 12px; cursor: pointer;">🔍 Debug Info</button>
                    <button onclick="window.echoEngine && window.echoEngine.closeAllMirrors()" style="background: linear-gradient(135deg, #ef4444, #dc2626); border: none; padding: 8px 12px; border-radius: 8px; color: white; font-size: 12px; cursor: pointer;">🗑️ Close All</button>
                </div>
            </div>

            <!-- Echo Engine -->
            <div class="echo-engine">
                <div class="echo-title">🔄 Echo Engine - Mirror Apps</div>
                
                <div class="device-tabs">
                    <div class="device-tab active" data-device="computer">💻 Computer</div>
                    <div class="device-tab" data-device="phone">📱 Phone</div>
                    <div class="device-tab" data-device="tablet">📱 Tablet</div>
                    <div class="device-tab" data-device="laptop">💻 Laptop</div>
                </div>
                
                <div class="app-grid" id="appGrid">
                    <!-- Apps populated by JavaScript -->
                </div>
            </div>

            <!-- Status Bar -->
            <div class="status-bar">
                <span id="connectionStatus">🟢 Echo Engine Ready</span>
                <span class="listening-indicator" id="listeningIndicator">🎤 Listening</span>
                <span id="timeStatus"></span>
            </div>
        </div>
    </div>

    <!-- 🧠 REGION: AI - Echo Engine JavaScript -->
    <!-- 🔗 MIGRATED FROM: /js/echo-engine.js -->
    <!-- 🔍 ID: ECHO_JS -->
    <script>
        // 💾 REGION: DATA - Echo Engine System Class
        class AgentLeeEchoEngine {
            constructor() {
                this.state = {
                    isMinimized: false,
                    isListening: false,
                    currentDevice: 'computer',
                    echoMirrors: [],
                    messages: [],
                    apps: {
                        computer: [
                            { id: 'chrome', name: 'Chrome', icon: '🌐' },
                            { id: 'notepad', name: 'Notepad', icon: '📝' },
                            { id: 'calculator', name: 'Calculator', icon: '🧮' },
                            { id: 'paint', name: 'Paint', icon: '🎨' },
                            { id: 'word', name: 'Word', icon: '📄' },
                            { id: 'excel', name: 'Excel', icon: '📊' },
                            { id: 'vscode', name: 'VS Code', icon: '💻' },
                            { id: 'discord', name: 'Discord', icon: '💬' },
                            { id: 'spotify', name: 'Spotify', icon: '🎵' },
                            { id: 'photoshop', name: 'Photoshop', icon: '🖼️' },
                            { id: 'terminal', name: 'Terminal', icon: '⚡' },
                            { id: 'folder', name: 'Files', icon: '📁' }
                        ],
                        phone: [
                            { id: 'whatsapp', name: 'WhatsApp', icon: '💬' },
                            { id: 'instagram', name: 'Instagram', icon: '📷' },
                            { id: 'youtube', name: 'YouTube', icon: '📺' },
                            { id: 'gmail', name: 'Gmail', icon: '📧' },
                            { id: 'maps', name: 'Maps', icon: '🗺️' },
                            { id: 'camera', name: 'Camera', icon: '📸' },
                            { id: 'tiktok', name: 'TikTok', icon: '🎬' },
                            { id: 'twitter', name: 'Twitter', icon: '🐦' },
                            { id: 'spotify-mobile', name: 'Spotify', icon: '🎵' },
                            { id: 'uber', name: 'Uber', icon: '🚗' },
                            { id: 'netflix', name: 'Netflix', icon: '🎭' },
                            { id: 'settings', name: 'Settings', icon: '⚙️' }
                        ],
                        tablet: [
                            { id: 'netflix-tablet', name: 'Netflix', icon: '🎬' },
                            { id: 'kindle', name: 'Kindle', icon: '📚' },
                            { id: 'procreate', name: 'Procreate', icon: '🎨' },
                            { id: 'notes', name: 'Notes', icon: '📝' },
                            { id: 'youtube-tablet', name: 'YouTube', icon: '📺' },
                            { id: 'games', name: 'Games', icon: '🎮' },
                            { id: 'photos', name: 'Photos', icon: '🖼️' },
                            { id: 'calendar', name: 'Calendar', icon: '📅' },
                            { id: 'email', name: 'Email', icon: '📧' },
                            { id: 'browser', name: 'Browser', icon: '🌐' },
                            { id: 'music', name: 'Music', icon: '🎵' },
                            { id: 'drawing', name: 'Drawing', icon: '✏️' }
                        ],
                        laptop: [
                            { id: 'firefox', name: 'Firefox', icon: '🦊' },
                            { id: 'photoshop-laptop', name: 'Photoshop', icon: '🎨' },
                            { id: 'teams', name: 'Teams', icon: '👥' },
                            { id: 'outlook', name: 'Outlook', icon: '📧' },
                            { id: 'slack', name: 'Slack', icon: '💬' },
                            { id: 'zoom', name: 'Zoom', icon: '📹' },
                            { id: 'git', name: 'Git', icon: '📋' },
                            { id: 'docker', name: 'Docker', icon: '🐳' },
                            { id: 'figma', name: 'Figma', icon: '🎯' },
                            { id: 'sublime', name: 'Sublime', icon: '📝' },
                            { id: 'postman', name: 'Postman', icon: '📮' },
                            { id: 'database', name: 'Database', icon: '🗄️' }
                        ]
                    }
                };
                
                this.init();
            }

            init() {
                console.log('🔄 Agent Lee Echo Engine Initializing...');
                
                // Initialize voice system
                initEchoVoiceSystem();
                
                // Bind events
                this.bindEvents();
                
                // Initialize drag functionality
                this.initializeDragFunctionality();
                
                // Load applications immediately
                this.loadApplications();
                
                // Start status updates
                this.startStatusUpdates();
                
                // Load saved data
                try {
                    this.loadFromStorage();
                } catch (error) {
                    console.log('💾 No previous data found, starting fresh');
                }
                
                // Initial welcome with delay
                setTimeout(() => {
                    this.addMessage('🔄 Echo Engine fully loaded! Click any app tile to create a floating mirror window. Switch between devices to see different app collections.', 'agent');
                    echoSpeak('Echo Engine ready. Click any app to create floating mirrors.');
                }, 1000);
                
                // Debug info
                setTimeout(() => {
                    console.log('✅ Agent Lee Echo Engine Online');
                    console.log('Current device:', this.state.currentDevice);
                    console.log('Available apps:', this.state.apps[this.state.currentDevice]);
                    
                    const appGrid = document.getElementById('appGrid');
                    console.log('App grid element:', appGrid);
                    console.log('App tiles count:', appGrid.children.length);
                }, 1500);
            }

            bindEvents() {
                // Header controls
                document.getElementById('minimizeBtn').addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.toggleMinimize();
                });

                document.getElementById('closeBtn').addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.closeEngine();
                });

                // Chat functionality
                document.getElementById('sendBtn').addEventListener('click', () => {
                    this.sendMessage();
                });

                document.getElementById('messageInput').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.sendMessage();
                    }
                });

                // Voice functionality
                document.getElementById('voiceBtn').addEventListener('click', () => {
                    this.toggleVoice();
                });

                // Device tab switching
                document.querySelectorAll('.device-tab').forEach(tab => {
                    tab.addEventListener('click', (e) => {
                        document.querySelectorAll('.device-tab').forEach(t => t.classList.remove('active'));
                        e.target.classList.add('active');
                        
                        const device = e.target.dataset.device;
                        this.switchDevice(device);
                    });
                });

                // Restore from minimized
                document.getElementById('minimizedBubble').addEventListener('dblclick', () => {
                    this.toggleMinimize();
                });

                // Voice system events
                window.addEventListener('echoVoiceStart', () => {
                    this.updateVoiceState(true);
                });

                window.addEventListener('echoVoiceEnd', () => {
                    this.updateVoiceState(false);
                });

                window.addEventListener('echoVoiceError', (e) => {
                    this.updateVoiceState(false);
                    this.addMessage('🎤 Voice error: ' + e.detail, 'agent');
                });

                // MCP command events
                window.addEventListener('mcpEchoExecuted', (e) => {
                    this.addMessage('🔄 Echo MCP: ' + e.detail.command + ' executed', 'agent');
                });
            }

            initializeDragFunctionality() {
                let dragTarget = null;
                let startX = 0;
                let startY = 0;
                let startLeft = 0;
                let startTop = 0;

                const startDrag = (element, handle, e) => {
                    dragTarget = element;
                    startX = e.clientX;
                    startY = e.clientY;
                    const rect = element.getBoundingClientRect();
                    startLeft = rect.left;
                    startTop = rect.top;
                    element.style.cursor = 'grabbing';
                    e.preventDefault();
                };

                // Main card dragging (only when minimized)
                document.getElementById('cardHeader').addEventListener('mousedown', (e) => {
                    if (this.state.isMinimized) {
                        startDrag(document.getElementById('agentCard'), document.getElementById('cardHeader'), e);
                    }
                });

                // Minimized bubble dragging
                document.getElementById('minimizedBubble').addEventListener('mousedown', (e) => {
                    startDrag(document.getElementById('agentCard'), document.getElementById('minimizedBubble'), e);
                });

                document.addEventListener('mousemove', (e) => {
                    if (dragTarget) {
                        const deltaX = e.clientX - startX;
                        const deltaY = e.clientY - startY;
                        const newLeft = startLeft + deltaX;
                        const newTop = startTop + deltaY;

                        // Keep within screen bounds
                        const maxLeft = window.innerWidth - dragTarget.offsetWidth;
                        const maxTop = window.innerHeight - dragTarget.offsetHeight;

                        dragTarget.style.left = Math.max(0, Math.min(newLeft, maxLeft)) + 'px';
                        dragTarget.style.top = Math.max(0, Math.min(newTop, maxTop)) + 'px';
                        dragTarget.style.right = 'auto';
                        dragTarget.style.bottom = 'auto';
                    }
                });

                document.addEventListener('mouseup', () => {
                    if (dragTarget) {
                        dragTarget.style.cursor = this.state.isMinimized ? 'grab' : 'default';
                        dragTarget = null;
                    }
                });
            }

            toggleMinimize() {
                this.state.isMinimized = !this.state.isMinimized;
                const agentCard = document.getElementById('agentCard');
                agentCard.classList.toggle('minimized', this.state.isMinimized);
                
                if (this.state.isMinimized) {
                    this.updateStatus('Minimized');
                    agentCard.style.cursor = 'grab';
                    this.addMessage('📦 Echo Engine minimized to floating bubble', 'agent');
                    runMCPCommand('minimize_echo_engine');
                } else {
                    this.updateStatus('Echo Engine Ready');
                    agentCard.style.cursor = 'default';
                    this.addMessage('📺 Echo Engine restored to full screen', 'agent');
                    runMCPCommand('restore_echo_engine');
                }
            }

            switchDevice(deviceType) {
                this.state.currentDevice = deviceType;
                this.loadApplications();
                this.addMessage('📱 Switched to ' + deviceType + ' device view', 'agent');
                runMCPCommand('switch_echo_device ' + deviceType);
            }

            loadApplications() {
                const appGrid = document.getElementById('appGrid');
                const apps = this.state.apps[this.state.currentDevice] || [];
                
                console.log('Loading apps for device:', this.state.currentDevice, 'Apps:', apps);
                
                appGrid.innerHTML = '';
                
                apps.forEach((app, index) => {
                    const appTile = document.createElement('div');
                    appTile.className = 'app-tile';
                    appTile.style.animationDelay = (index * 0.1) + 's';
                    appTile.innerHTML = `
                        <div class="app-icon">${app.icon}</div>
                        <div class="app-name">${app.name}</div>
                    `;
                    
                    // Add click handler with proper binding
                    appTile.addEventListener('click', (e) => {
                        e.stopPropagation();
                        console.log('🖱️ App tile clicked:', app.name, app);
                        
                        // Visual feedback
                        appTile.style.transform = 'scale(0.95)';
                        appTile.style.background = 'rgba(59, 130, 246, 0.5)';
                        
                        // Create the echo mirror
                        const mirror = this.createEchoMirror(app);
                        console.log('📱 Mirror created and added to DOM:', mirror);
                        
                        // Reset visual feedback
                        setTimeout(() => {
                            appTile.style.transform = 'scale(1)';
                            appTile.style.background = 'rgba(30, 41, 59, 0.8)';
                        }, 150);
                    });
                    
                    // Add hover effects
                    appTile.addEventListener('mouseenter', () => {
                        appTile.style.background = 'rgba(59, 130, 246, 0.3)';
                        appTile.style.transform = 'translateY(-3px) scale(1.05)';
                    });
                    
                    appTile.addEventListener('mouseleave', () => {
                        appTile.style.background = 'rgba(30, 41, 59, 0.8)';
                        appTile.style.transform = 'translateY(0) scale(1)';
                    });
                    
                    appGrid.appendChild(appTile);
                });
                
                console.log('✅ Loaded', apps.length, 'apps for', this.state.currentDevice);
            }

            createEchoMirror(app) {
                const mirrorId = 'echo_mirror_' + app.id + '_' + Date.now();
                const mirror = document.createElement('div');
                mirror.className = 'echo-mirror';
                mirror.id = mirrorId;
                
                // Position with offset for multiple mirrors
                const offsetX = this.state.echoMirrors.length * 50;
                const offsetY = this.state.echoMirrors.length * 50;
                mirror.style.position = 'fixed';
                mirror.style.left = (200 + offsetX) + 'px';
                mirror.style.top = (150 + offsetY) + 'px';
                mirror.style.width = '400px';
                mirror.style.height = '300px';
                mirror.style.zIndex = '999998';
                mirror.style.display = 'flex';
                mirror.style.flexDirection = 'column';
                mirror.style.opacity = '1';
                mirror.style.visibility = 'visible';
                
                mirror.innerHTML = `
                    <div class="echo-header" style="background: linear-gradient(135deg, #1e40af 0%, #3730a3 50%, #374151 100%); padding: 8px 12px; display: flex; justify-content: space-between; align-items: center; cursor: grab; border-bottom: 1px solid rgba(59, 130, 246, 0.3); position: relative; overflow: hidden;">
                        <div class="echo-mirror-title" style="color: white; font-size: 11px; font-weight: bold; z-index: 2; position: relative;">🔄 Echo: ${app.name} (${this.state.currentDevice})</div>
                        <button onclick="window.echoEngine.closeEchoMirror('${mirrorId}')" style="background: #ef4444; width: 20px; height: 20px; font-size: 12px; border: none; border-radius: 50%; color: white; cursor: pointer; display: flex; align-items: center; justify-content: center; z-index: 3;">×</button>
                    </div>
                    <div class="echo-content" style="flex: 1; position: relative; background: linear-gradient(180deg, #000000 0%, #1a1a2e 100%); display: flex; align-items: center; justify-content: center; color: white; text-align: center; padding: 20px;">
                        <div class="echo-placeholder" style="display: flex; flex-direction: column; align-items: center; justify-content: center; animation: echoGlow 2s ease-in-out infinite alternate; color: white; text-align: center;">
                            <div style="font-size: 48px; margin-bottom: 20px;">${app.icon}</div>
                            <div style="font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #3b82f6;">🔄 ${app.name} Mirror Active</div>
                            <div style="font-size: 14px; opacity: 0.8; line-height: 1.4; color: #e2e8f0;">
                                Mirroring ${app.name} from ${this.state.currentDevice}<br>
                                Echo window ready for content streaming<br>
                                <span style="color: #10b981;">Drag to reposition</span> • <span style="color: #ef4444;">Click × to close</span>
                            </div>
                        </div>
                    </div>
                `;
                
                // Make mirror draggable
                const header = mirror.querySelector('.echo-header');
                this.makeElementDraggable(mirror, header);
                
                // Add to DOM first
                document.body.appendChild(mirror);
                
                // Force visibility and positioning
                setTimeout(() => {
                    mirror.style.transform = 'scale(1)';
                    mirror.style.opacity = '1';
                    mirror.style.visibility = 'visible';
                    mirror.style.display = 'flex';
                }, 10);
                
                // Track the mirror
                this.state.echoMirrors.push({ id: mirrorId, element: mirror, app: app });
                
                // Animate in
                mirror.style.transform = 'scale(0.8)';
                mirror.style.transition = 'all 0.3s ease';
                setTimeout(() => {
                    mirror.style.transform = 'scale(1)';
                }, 50);
                
                this.addMessage('🔄 Created Echo mirror for ' + app.name + ' from ' + this.state.currentDevice, 'agent');
                
                // Voice feedback
                try {
                    echoSpeak('Echo mirror created for ' + app.name);
                } catch (error) {
                    console.log('Voice not available');
                }
                
                runMCPCommand('create_echo_mirror ' + app.id + ' ' + this.state.currentDevice);
                
                console.log('✅ Echo mirror created:', mirrorId, mirror);
                console.log('Mirror position:', mirror.style.left, mirror.style.top);
                console.log('Mirror visibility:', mirror.style.visibility, mirror.style.display);
                
                return mirror;
            }

            makeElementDraggable(element, handle) {
                let isDragging = false;
                let startX = 0;
                let startY = 0;
                let startLeft = 0;
                let startTop = 0;

                handle.addEventListener('mousedown', (e) => {
                    isDragging = true;
                    startX = e.clientX;
                    startY = e.clientY;
                    const rect = element.getBoundingClientRect();
                    startLeft = rect.left;
                    startTop = rect.top;
                    handle.style.cursor = 'grabbing';
                    e.preventDefault();
                });

                document.addEventListener('mousemove', (e) => {
                    if (isDragging) {
                        const deltaX = e.clientX - startX;
                        const deltaY = e.clientY - startY;
                        element.style.left = (startLeft + deltaX) + 'px';
                        element.style.top = (startTop + deltaY) + 'px';
                    }
                });

                document.addEventListener('mouseup', () => {
                    if (isDragging) {
                        isDragging = false;
                        handle.style.cursor = 'grab';
                    }
                });
            }

            closeEchoMirror(mirrorId) {
                const mirrorIndex = this.state.echoMirrors.findIndex(mirror => mirror.id === mirrorId);
                if (mirrorIndex !== -1) {
                    this.state.echoMirrors[mirrorIndex].element.remove();
                    this.state.echoMirrors.splice(mirrorIndex, 1);
                    this.addMessage('🗑️ Echo mirror ' + mirrorId + ' closed', 'agent');
                    runMCPCommand('close_echo_mirror ' + mirrorId);
                }
            }

            debugInfo() {
                console.log('🔍 Echo Engine Debug Info:');
                console.log('- Current device:', this.state.currentDevice);
                console.log('- Available apps:', this.state.apps[this.state.currentDevice]);
                console.log('- Active mirrors:', this.state.echoMirrors.length);
                console.log('- App grid element:', document.getElementById('appGrid'));
                console.log('- App tiles count:', document.getElementById('appGrid').children.length);
                
                this.addMessage('🔍 Debug info logged to console. Open DevTools to see details.', 'agent');
                
                // Test creating a mirror directly if no mirrors exist
                if (this.state.echoMirrors.length === 0 && this.state.apps[this.state.currentDevice].length > 0) {
                    const firstApp = this.state.apps[this.state.currentDevice][0];
                    this.addMessage('🧪 Creating demo mirror for: ' + firstApp.name, 'agent');
                    this.createEchoMirror(firstApp);
                }
            }

            closeAllMirrors() {
                console.log('🗑️ Closing all mirrors...');
                this.state.echoMirrors.forEach(mirror => {
                    mirror.element.remove();
                });
                this.state.echoMirrors = [];
                this.addMessage('🗑️ All Echo mirrors closed!', 'agent');
            }

            sendMessage() {
                const input = document.getElementById('messageInput');
                const message = input.value.trim();
                
                if (message) {
                    this.addMessage(message, 'user');
                    input.value = '';
                    
                    setTimeout(() => {
                        this.processCommand(message);
                    }, 500);
                }
            }

            addMessage(text, type) {
                const chatArea = document.getElementById('chatArea');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message ' + type + '-message';
                messageDiv.textContent = text;
                
                chatArea.appendChild(messageDiv);
                chatArea.scrollTop = chatArea.scrollHeight;
                
                this.state.messages.push({ text, type, timestamp: Date.now() });
                
                if (this.state.messages.length > 50) {
                    this.state.messages = this.state.messages.slice(-50);
                }

                // Safe storage save
                try {
                    this.saveToStorage();
                } catch (error) {
                    console.log('💾 Storage not available, using memory only');
                }
            }

            processCommand(message) {
                const lowerMessage = message.toLowerCase();
                let response = '';

                if (lowerMessage.includes('mirror') || lowerMessage.includes('echo')) {
                    response = '🔄 Click any app tile to create an Echo mirror window that will float above other applications.';
                } else if (lowerMessage.includes('device') || lowerMessage.includes('switch')) {
                    response = '📱 Use the device tabs to switch between Computer, Phone, Tablet, and Laptop app collections.';
                } else if (lowerMessage.includes('close') || lowerMessage.includes('remove')) {
                    response = '🗑️ Click the × button on any Echo mirror window to close it.';
                } else if (lowerMessage.includes('minimize')) {
                    this.toggleMinimize();
                    response = '📦 Echo Engine minimized to floating bubble. Double-click to restore.';
                } else if (lowerMessage.includes('help')) {
                    response = '🔄 Echo Engine Help: Click app tiles to create floating mirrors, switch devices with tabs, drag windows to reposition, and use voice commands for control.';
                } else {
                    const responses = [
                        '🔄 Echo Engine processing your request...',
                        '📱 Ready to create new Echo mirrors for any application.',
                        '🌐 All devices connected and ready for mirroring.',
                        '⚡ Echo Engine responding optimally.',
                        '🔄 Standing by for Echo mirror creation commands.'
                    ];
                    response = responses[Math.floor(Math.random() * responses.length)];
                }

                this.addMessage(response, 'agent');
                runMCPCommand('process_echo_message: ' + message);
                echoSpeak(response.replace(/[🔄📱🌐⚡🗑️📦📺]/g, ''));
            }

            toggleVoice() {
                if (this.state.isListening) {
                    this.stopListening();
                } else {
                    this.startListening();
                }
            }

            startListening() {
                this.state.isListening = true;
                const voiceBtn = document.getElementById('voiceBtn');
                const indicator = document.getElementById('listeningIndicator');
                
                if (voiceBtn) voiceBtn.classList.add('active');
                if (indicator) indicator.classList.add('active');
                
                this.updateStatus('Listening...');
                
                startEchoListening((transcript) => {
                    this.state.isListening = false;
                    if (voiceBtn) voiceBtn.classList.remove('active');
                    if (indicator) indicator.classList.remove('active');
                    
                    document.getElementById('messageInput').value = transcript;
                    this.addMessage(transcript, 'user');
                    
                    setTimeout(() => {
                        this.processCommand(transcript);
                    }, 500);
                    
                    this.updateStatus('Echo Engine Ready');
                });
            }

            stopListening() {
                this.state.isListening = false;
                const voiceBtn = document.getElementById('voiceBtn');
                const indicator = document.getElementById('listeningIndicator');
                
                if (voiceBtn) voiceBtn.classList.remove('active');
                if (indicator) indicator.classList.remove('active');
                
                this.updateStatus('Echo Engine Ready');
            }

            updateVoiceState(listening) {
                this.state.isListening = listening;
                const voiceBtn = document.getElementById('voiceBtn');
                const indicator = document.getElementById('listeningIndicator');
                
                if (listening) {
                    if (voiceBtn) voiceBtn.classList.add('active');
                    if (indicator) indicator.classList.add('active');
                    this.updateStatus('Listening...');
                } else {
                    if (voiceBtn) voiceBtn.classList.remove('active');
                    if (indicator) indicator.classList.remove('active');
                    this.updateStatus('Echo Engine Ready');
                }
            }

            startStatusUpdates() {
                // Update time every second
                setInterval(() => {
                    document.getElementById('timeStatus').textContent = new Date().toLocaleTimeString();
                }, 1000);

                // Auto-save every 30 seconds (safely)
                setInterval(() => {
                    try {
                        this.saveToStorage();
                    } catch (error) {
                        // Silently continue if storage fails
                    }
                }, 30000);
            }

            updateStatus(status) {
                document.getElementById('agentStatus').textContent = status;
            }

            closeEngine() {
                if (confirm('Close Agent Lee Echo Engine?')) {
                    document.getElementById('agentCard').style.display = 'none';
                    this.addMessage('👋 Echo Engine shutting down...', 'agent');
                    echoSpeak('Echo Engine shutting down. Goodbye!');
                    runMCPCommand('shutdown_echo_engine');
                }
            }

            saveToStorage() {
                // Use in-memory storage instead of localStorage
                try {
                    window.echoEngineState = JSON.stringify(this.state);
                    window.echoEngineMessages = JSON.stringify(this.state.messages);
                } catch (error) {
                    console.log('💾 Using in-memory storage only');
                }
            }

            loadFromStorage() {
                // Load from in-memory storage if available
                try {
                    if (window.echoEngineState) {
                        const saved = JSON.parse(window.echoEngineState);
                        this.state = { ...this.state, ...saved };
                    }
                    
                    if (window.echoEngineMessages) {
                        this.state.messages = JSON.parse(window.echoEngineMessages);
                        this.state.messages.slice(-3).forEach(msg => {
                            const chatArea = document.getElementById('chatArea');
                            const messageDiv = document.createElement('div');
                            messageDiv.className = 'message ' + msg.type + '-message';
                            messageDiv.textContent = msg.text;
                            chatArea.appendChild(messageDiv);
                        });
                    }
                } catch (error) {
                    console.log('💾 Starting with fresh state - no previous data');
                }
            }
        }

        // Initialize Echo Engine when page loads
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🔄 Starting Agent Lee Echo Engine...');
            
            try {
                window.echoEngine = new AgentLeeEchoEngine();
                console.log('✅ Agent Lee Echo Engine initialized successfully!');
                
                // Initialize voice system
                if (window.speechSynthesis) {
                    window.speechSynthesis.onvoiceschanged = () => {
                        console.log('🎤 Echo voice system ready');
                    };
                }
            } catch (error) {
                console.log('⚠️ Initialization error (retrying):', error.message);
                
                // Retry with minimal configuration
                setTimeout(() => {
                    try {
                        window.echoEngine = new AgentLeeEchoEngine();
                        console.log('✅ Echo Engine initialized on retry');
                    } catch (retryError) {
                        console.log('❌ Failed to initialize:', retryError.message);
                        
                        // Fallback: Try to at least load the UI
                        setTimeout(() => {
                            console.log('🔄 Attempting fallback initialization...');
                            try {
                                // Create minimal engine instance
                                const basicEngine = {
                                    state: { currentDevice: 'computer', apps: {
                                        computer: [
                                            { id: 'chrome', name: 'Chrome', icon: '🌐' },
                                            { id: 'notepad', name: 'Notepad', icon: '📝' },
                                            { id: 'calculator', name: 'Calculator', icon: '🧮' },
                                            { id: 'paint', name: 'Paint', icon: '🎨' },
                                            { id: 'word', name: 'Word', icon: '📄' },
                                            { id: 'excel', name: 'Excel', icon: '📊' }
                                        ]
                                    }},
                                    loadApplications: function() {
                                        const appGrid = document.getElementById('appGrid');
                                        if (appGrid) {
                                            appGrid.innerHTML = '';
                                            this.state.apps.computer.forEach(app => {
                                                const tile = document.createElement('div');
                                                tile.className = 'app-tile';
                                                tile.innerHTML = `<div class="app-icon">${app.icon}</div><div class="app-name">${app.name}</div>`;
                                                tile.addEventListener('click', () => {
                                                    console.log('Basic click handler for:', app.name);
                                                });
                                                appGrid.appendChild(tile);
                                            });
                                        }
                                    }
                                };
                                basicEngine.loadApplications();
                                console.log('✅ Fallback UI loaded');
                            } catch (fallbackError) {
                                console.log('❌ Complete initialization failure');
                            }
                        }, 500);
                    }
                }, 1000);
            }
        });
    </script>
</body>
</html>