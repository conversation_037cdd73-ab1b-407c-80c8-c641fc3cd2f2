<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Lee™ - Complete AI Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: transparent;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Agent Lee Card - Full Screen */
        .agent-lee-card {
            width: 100vw;
            height: 100vh;
            background: linear-gradient(145deg, #1e293b 0%, #0f172a 100%);
            position: relative;
            overflow: hidden;
            cursor: move;
            border: none;
            display: flex;
            flex-direction: column;
        }

        /* Card Header */
        .card-header {
            background: linear-gradient(135deg, #1e40af 0%, #3730a3 50%, #374151 100%);
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: grab;
            border-bottom: 1px solid rgba(59, 130, 246, 0.3);
            min-height: 80px;
        }

        .card-header:active {
            cursor: grabbing;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .agent-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.4), 0 0 0 2px rgba(59, 130, 246, 0.3);
            border: 2px solid rgba(255,255,255,0.2);
        }

        .agent-face {
            font-size: 24px;
            animation: blink 3s infinite;
        }

        @keyframes blink {
            0%, 90%, 100% { transform: scale(1); }
            95% { transform: scale(0.9); }
        }

        .agent-info {
            color: white;
        }

        .agent-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 3px;
        }

        .agent-status {
            font-size: 12px;
            color: rgba(255,255,255,0.8);
        }

        /* Window Controls */
        .window-controls {
            display: flex;
            gap: 8px;
        }

        .control-btn {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            font-size: 12px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .minimize-btn { background: #fbbf24; }
        .close-btn { background: #ef4444; }

        .control-btn:hover {
            transform: scale(1.2);
        }

        /* Main Content Area */
        .card-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            gap: 20px;
            overflow-y: auto;
        }

        /* Quick Info Panel */
        .quick-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .info-card {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 12px;
            padding: 15px;
            color: white;
            text-align: center;
        }

        .info-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .info-title {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .info-value {
            font-size: 14px;
            color: #3b82f6;
        }

        /* Conversation Area */
        .conversation-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .conversation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
        }

        .conversation-title {
            font-size: 16px;
            font-weight: bold;
        }

        .voice-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
        }

        .conversation-area {
            flex: 1;
            background: rgba(30, 41, 59, 0.6);
            border-radius: 12px;
            padding: 15px;
            overflow-y: auto;
            border: 1px solid rgba(59, 130, 246, 0.2);
            min-height: 200px;
        }

        .message {
            margin-bottom: 12px;
            padding: 10px 12px;
            border-radius: 8px;
            font-size: 14px;
            line-height: 1.4;
        }

        .agent-message {
            background: rgba(59, 130, 246, 0.2);
            color: #e2e8f0;
            border-left: 3px solid #3b82f6;
        }

        .user-message {
            background: rgba(16, 185, 129, 0.2);
            color: #e2e8f0;
            border-left: 3px solid #10b981;
            text-align: right;
        }

        /* Input Controls */
        .input-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 8px;
            padding: 12px;
            color: white;
            font-size: 14px;
            outline: none;
        }

        .message-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .control-button {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            font-size: 18px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .voice-btn {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
        }

        .send-btn {
            background: linear-gradient(135deg, #3b82f6, #1e40af);
        }

        .control-button:hover {
            transform: scale(1.1);
        }

        .control-button.active {
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* Feature Grid */
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
            margin-top: 20px;
        }

        .feature-btn {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 10px;
            padding: 12px 8px;
            color: white;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 6px;
            text-align: center;
        }

        .feature-btn:hover {
            background: rgba(59, 130, 246, 0.3);
            transform: translateY(-2px);
        }

        .feature-icon {
            font-size: 20px;
        }

        /* Echo Launcher */
        .echo-launcher {
            background: rgba(30, 41, 59, 0.6);
            border-radius: 12px;
            padding: 15px;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .echo-header {
            color: white;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }

        .device-tabs {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
            justify-content: center;
        }

        .device-tab {
            background: rgba(59, 130, 246, 0.2);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 6px;
            padding: 6px 12px;
            color: white;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .device-tab.active {
            background: rgba(59, 130, 246, 0.5);
        }

        .app-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 8px;
        }

        .app-tile {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 8px;
            padding: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            text-align: center;
        }

        .app-tile:hover {
            background: rgba(59, 130, 246, 0.3);
            transform: translateY(-1px);
        }

        .app-icon {
            font-size: 16px;
        }

        .app-name {
            font-size: 9px;
            color: white;
            font-weight: 500;
        }

        /* Status Bar */
        .status-bar {
            background: rgba(15, 23, 42, 0.8);
            padding: 8px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 11px;
            color: rgba(255, 255, 255, 0.7);
            border-top: 1px solid rgba(59, 130, 246, 0.2);
        }

        .listening-indicator {
            display: none;
            color: #ef4444;
            animation: pulse 1s infinite;
        }

        .listening-indicator.active {
            display: block;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .quick-info {
                grid-template-columns: 1fr;
            }
            
            .feature-grid {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .app-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="agent-lee-card" id="agentCard">
        <!-- Card Header -->
        <div class="card-header" id="cardHeader">
            <div class="header-left">
                <div class="agent-avatar">
                    <div class="agent-face" id="agentFace">🤖</div>
                </div>
                <div class="agent-info">
                    <div class="agent-name">Agent Lee™</div>
                    <div class="agent-status" id="agentStatus">Ready to assist you</div>
                </div>
            </div>
            
            <div class="window-controls">
                <button class="control-btn minimize-btn" id="minimizeBtn" title="Minimize">−</button>
                <button class="control-btn close-btn" id="closeBtn" title="Close">×</button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="card-content">
            <!-- Quick Info Panel -->
            <div class="quick-info">
                <div class="info-card" id="weatherCard">
                    <div class="info-icon">🌤️</div>
                    <div class="info-title">Weather</div>
                    <div class="info-value" id="weatherInfo">Loading...</div>
                </div>
                <div class="info-card" id="trafficCard">
                    <div class="info-icon">🚗</div>
                    <div class="info-title">Traffic</div>
                    <div class="info-value" id="trafficInfo">Loading...</div>
                </div>
            </div>

            <!-- Conversation Section -->
            <div class="conversation-section">
                <div class="conversation-header">
                    <div class="conversation-title">Chat with Agent Lee</div>
                    <div class="voice-status">
                        <span class="listening-indicator" id="listeningIndicator">🎤 Listening...</span>
                        <span id="voiceStatus">Voice Ready</span>
                    </div>
                </div>
                
                <div class="conversation-area" id="conversationArea">
                    <div class="message agent-message">
                        Hello! I'm Agent Lee, your AI assistant. I can help you with applications, weather, traffic, and much more. What would you like me to do?
                    </div>
                </div>

                <!-- Input Controls -->
                <div class="input-controls">
                    <input type="text" class="message-input" id="messageInput" placeholder="Type your message or ask me anything...">
                    <button class="control-button voice-btn" id="voiceBtn" title="Voice Input">🎤</button>
                    <button class="control-button send-btn" id="sendBtn" title="Send Message">➤</button>
                </div>
            </div>

            <!-- Feature Buttons -->
            <div class="feature-grid">
                <button class="feature-btn" id="appsBtn" title="Applications">
                    <span class="feature-icon">📱</span>
                    <span>Apps</span>
                </button>
                <button class="feature-btn" id="webBtn" title="Web Search">
                    <span class="feature-icon">🌐</span>
                    <span>Web</span>
                </button>
                <button class="feature-btn" id="emailBtn" title="Email">
                    <span class="feature-icon">📧</span>
                    <span>Email</span>
                </button>
                <button class="feature-btn" id="weatherBtn" title="Weather">
                    <span class="feature-icon">🌤️</span>
                    <span>Weather</span>
                </button>
                <button class="feature-btn" id="trafficBtn" title="Traffic">
                    <span class="feature-icon">🚗</span>
                    <span>Traffic</span>
                </button>
                <button class="feature-btn" id="newsBtn" title="News">
                    <span class="feature-icon">📰</span>
                    <span>News</span>
                </button>
                <button class="feature-btn" id="calendarBtn" title="Calendar">
                    <span class="feature-icon">📅</span>
                    <span>Calendar</span>
                </button>
                <button class="feature-btn" id="settingsBtn" title="Settings">
                    <span class="feature-icon">⚙️</span>
                    <span>Settings</span>
                </button>
            </div>

            <!-- Echo Launcher -->
            <div class="echo-launcher">
                <div class="echo-header">🔄 Echo Engine - Mirror Apps Across Devices</div>
                
                <div class="device-tabs">
                    <div class="device-tab active" data-device="computer">🖥️ Computer</div>
                    <div class="device-tab" data-device="phone">📱 Phone</div>
                    <div class="device-tab" data-device="tablet">📱 Tablet</div>
                    <div class="device-tab" data-device="laptop">💻 Laptop</div>
                </div>
                
                <div class="app-grid" id="appGrid">
                    <!-- Apps will be populated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
            <span id="connectionStatus">🟢 Connected</span>
            <span class="listening-indicator" id="listeningIndicator2">🎤 Listening...</span>
            <span id="timeStatus"></span>
        </div>
    </div>

    <script src="agent_lee_complete.js"></script>
</body>
</html>
