/**
 * Agent Lee™ Application Scanner Client
 * Communicates with Electron main process to get the list of installed apps
 * on the current device and potentially synced lists from other connected devices
 */

class ApplicationScannerClient {
    constructor() {
        this.isElectron = typeof window.electronAPI !== 'undefined';
        this.scannedApps = {
            computer: [],
            phone: [],
            tablet: [],
            laptop: []
        };
        this.isScanning = false;
        this.initialized = false;
    }

    async initialize() {
        console.log('🔍 Initializing Application Scanner Client...');
        
        try {
            // Set up scanning capabilities
            this.setupScanningHandlers();
            
            // Initialize Electron bridge if available
            if (this.isElectron) {
                await this.initializeElectronBridge();
            }
            
            this.initialized = true;
            console.log('✅ Application Scanner Client initialized successfully');
            
        } catch (error) {
            console.error('❌ Application Scanner Client initialization failed:', error);
            throw error;
        }
    }

    async initializeElectronBridge() {
        if (this.isElectron && window.electronAPI) {
            // Set up IPC handlers for app scanning
            window.electronAPI.onAppsScanned((deviceType, apps) => {
                this.handleAppsScanned(deviceType, apps);
            });

            window.electronAPI.onScanProgress((progress) => {
                this.handleScanProgress(progress);
            });
        }
    }

    setupScanningHandlers() {
        // Listen for scan requests
        document.addEventListener('scanApplications', (event) => {
            this.scanApplications(event.detail.deviceType);
        });

        // Listen for scan all devices requests
        document.addEventListener('scanAllDevices', () => {
            this.scanAllDevices();
        });
    }

    /**
     * Scan applications on all connected devices
     */
    async scanAllDevices() {
        if (this.isScanning) {
            console.log('⚠️ Scan already in progress');
            return;
        }

        console.log('🔍 Starting application scan for all devices...');
        this.isScanning = true;
        
        try {
            // Update UI to show scanning state
            this.updateScanningStatus(true);

            // Scan current computer
            await this.scanApplications('computer');

            // Scan other devices through DeviceSyncAgent
            if (window.DeviceSyncAgent) {
                await this.scanRemoteDevices();
            }

            console.log('✅ Application scan completed for all devices');
            
        } catch (error) {
            console.error('❌ Application scan failed:', error);
        } finally {
            this.isScanning = false;
            this.updateScanningStatus(false);
        }
    }

    /**
     * Scan applications on a specific device
     * @param {string} deviceType - Type of device (computer, phone, tablet, laptop)
     */
    async scanApplications(deviceType) {
        console.log(`🔍 Scanning applications for ${deviceType}...`);

        try {
            if (deviceType === 'computer' && this.isElectron) {
                // Scan local computer applications
                await this.scanLocalApplications();
            } else {
                // Scan remote device applications
                await this.scanRemoteApplications(deviceType);
            }

        } catch (error) {
            console.error(`❌ Failed to scan ${deviceType} applications:`, error);
            throw error;
        }
    }

    async scanLocalApplications() {
        try {
            // Try to get applications from backend API first
            const response = await fetch('/api/scan_apps');
            const data = await response.json();

            if (data.status === 'success' && data.apps) {
                this.scannedApps.computer = data.apps;
                this.populateAppGrid('computer', data.apps);
                console.log(`✅ Found ${data.apps.length} applications on computer via API`);
                return;
            }
        } catch (error) {
            console.error('❌ Failed to scan via API:', error);
        }

        // Fallback to Electron API if available
        if (this.isElectron) {
            try {
                const apps = await window.electronAPI.scanInstalledApps();
                this.scannedApps.computer = apps;
                this.populateAppGrid('computer', apps);
                console.log(`✅ Found ${apps.length} applications on computer via Electron`);
                return;
            } catch (error) {
                console.error('❌ Failed to scan via Electron:', error);
            }
        }

        // Final fallback to mock data
        console.log('⚠️ Using mock application data');
        this.scannedApps.computer = this.getMockApplications('computer');
        this.populateAppGrid('computer', this.scannedApps.computer);
    }

    async scanRemoteApplications(deviceType) {
        try {
            if (window.DeviceSyncAgent) {
                // Request remote device scan through DeviceSyncAgent
                const apps = await window.DeviceSyncAgent.requestDeviceAppScan(deviceType);
                
                this.scannedApps[deviceType] = apps;
                this.populateAppGrid(deviceType, apps);
                
                console.log(`✅ Found ${apps.length} applications on ${deviceType}`);
            } else {
                // Fallback to mock data
                this.scannedApps[deviceType] = this.getMockApplications(deviceType);
                this.populateAppGrid(deviceType, this.scannedApps[deviceType]);
            }
            
        } catch (error) {
            console.error(`❌ Failed to scan ${deviceType} applications:`, error);
            
            // Fallback to mock data
            this.scannedApps[deviceType] = this.getMockApplications(deviceType);
            this.populateAppGrid(deviceType, this.scannedApps[deviceType]);
        }
    }

    async scanRemoteDevices() {
        const remoteDevices = ['phone', 'tablet', 'laptop'];
        
        for (const deviceType of remoteDevices) {
            try {
                await this.scanRemoteApplications(deviceType);
                
                // Small delay between scans
                await new Promise(resolve => setTimeout(resolve, 500));
                
            } catch (error) {
                console.error(`❌ Failed to scan ${deviceType}:`, error);
            }
        }
    }

    populateAppGrid(deviceType, apps) {
        const appGrid = document.getElementById(`${deviceType}Apps`);
        const statusElement = document.getElementById(`${deviceType}Status`);
        
        if (!appGrid) {
            console.error(`❌ App grid not found for ${deviceType}`);
            return;
        }

        // Clear existing apps
        appGrid.innerHTML = '';

        // Update status
        if (statusElement) {
            statusElement.textContent = `${apps.length} apps found`;
        }

        // Populate with apps
        apps.forEach(app => {
            const appTile = this.createAppTile(app, deviceType);
            appGrid.appendChild(appTile);
        });

        console.log(`✅ Populated ${apps.length} apps for ${deviceType}`);
    }

    createAppTile(app, deviceType) {
        const tile = document.createElement('div');
        tile.className = 'app-tile';
        tile.dataset.appId = app.id;
        tile.dataset.deviceType = deviceType;
        
        tile.innerHTML = `
            <div class="app-icon">${app.icon}</div>
            <div class="app-name">${app.name}</div>
        `;

        // Add click handler for app launching
        tile.addEventListener('click', () => {
            this.handleAppTileClick(app, deviceType);
        });

        return tile;
    }

    handleAppTileClick(app, deviceType) {
        console.log(`🚀 App tile clicked: ${app.name} on ${deviceType}`);
        
        // Dispatch event for EchoEngineClient to handle
        const event = new CustomEvent('appLaunchRequested', {
            detail: {
                app: app,
                deviceType: deviceType
            }
        });
        document.dispatchEvent(event);
    }

    getMockApplications(deviceType) {
        const mockApps = {
            computer: [
                { id: 'chrome', name: 'Chrome', icon: '🌐', path: 'chrome.exe', category: 'Browser' },
                { id: 'notepad', name: 'Notepad', icon: '📝', path: 'notepad.exe', category: 'Editor' },
                { id: 'calculator', name: 'Calculator', icon: '🧮', path: 'calc.exe', category: 'Utility' },
                { id: 'paint', name: 'Paint', icon: '🎨', path: 'mspaint.exe', category: 'Graphics' },
                { id: 'word', name: 'Word', icon: '📄', path: 'winword.exe', category: 'Office' },
                { id: 'excel', name: 'Excel', icon: '📊', path: 'excel.exe', category: 'Office' },
                { id: 'vscode', name: 'VS Code', icon: '💻', path: 'code.exe', category: 'Development' },
                { id: 'discord', name: 'Discord', icon: '💬', path: 'discord.exe', category: 'Communication' },
                { id: 'spotify', name: 'Spotify', icon: '🎵', path: 'spotify.exe', category: 'Media' }
            ],
            phone: [
                { id: 'whatsapp', name: 'WhatsApp', icon: '💬', package: 'com.whatsapp', category: 'Communication' },
                { id: 'instagram', name: 'Instagram', icon: '📷', package: 'com.instagram.android', category: 'Social' },
                { id: 'youtube', name: 'YouTube', icon: '📺', package: 'com.google.android.youtube', category: 'Media' },
                { id: 'gmail', name: 'Gmail', icon: '📧', package: 'com.google.android.gm', category: 'Email' },
                { id: 'maps', name: 'Maps', icon: '🗺️', package: 'com.google.android.apps.maps', category: 'Navigation' },
                { id: 'camera', name: 'Camera', icon: '📸', package: 'com.android.camera2', category: 'Media' }
            ],
            tablet: [
                { id: 'netflix', name: 'Netflix', icon: '🎬', package: 'com.netflix.mediaclient', category: 'Media' },
                { id: 'kindle', name: 'Kindle', icon: '📚', package: 'com.amazon.kindle', category: 'Reading' },
                { id: 'procreate', name: 'Procreate', icon: '🎨', package: 'com.procreate.app', category: 'Graphics' },
                { id: 'notes', name: 'Notes', icon: '📝', package: 'com.apple.mobilenotes', category: 'Productivity' }
            ],
            laptop: [
                { id: 'firefox', name: 'Firefox', icon: '🦊', path: 'firefox.exe', category: 'Browser' },
                { id: 'photoshop', name: 'Photoshop', icon: '🎨', path: 'photoshop.exe', category: 'Graphics' },
                { id: 'teams', name: 'Teams', icon: '👥', path: 'teams.exe', category: 'Communication' },
                { id: 'outlook', name: 'Outlook', icon: '📧', path: 'outlook.exe', category: 'Email' }
            ]
        };

        return mockApps[deviceType] || [];
    }

    updateScanningStatus(isScanning) {
        const loadingElement = document.getElementById('loadingState');
        
        if (loadingElement) {
            loadingElement.classList.toggle('active', isScanning);
        }

        // Update device status indicators
        const deviceTypes = ['computer', 'phone', 'tablet', 'laptop'];
        deviceTypes.forEach(deviceType => {
            const statusElement = document.getElementById(`${deviceType}Status`);
            if (statusElement) {
                statusElement.textContent = isScanning ? 'Scanning...' : 'Ready';
            }
        });
    }

    handleAppsScanned(deviceType, apps) {
        console.log(`📱 Apps scanned for ${deviceType}:`, apps);
        this.scannedApps[deviceType] = apps;
        this.populateAppGrid(deviceType, apps);
    }

    handleScanProgress(progress) {
        console.log('📊 Scan progress:', progress);
        
        // Update progress indicators if available
        const progressElement = document.getElementById('scanProgress');
        if (progressElement) {
            progressElement.textContent = `${progress.completed}/${progress.total} devices scanned`;
        }
    }

    getScannedApps(deviceType) {
        return this.scannedApps[deviceType] || [];
    }

    getAllScannedApps() {
        return this.scannedApps;
    }

    refreshApplications(deviceType) {
        if (deviceType) {
            this.scanApplications(deviceType);
        } else {
            this.scanAllDevices();
        }
    }
}

// Create global instance
window.ApplicationScannerClient = new ApplicationScannerClient();
