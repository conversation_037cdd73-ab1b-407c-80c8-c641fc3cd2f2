/**
 * Agent Lee™ Device Sync Agent
 * Handles communication between Agent <PERSON> instances on different devices
 * Manages WebRTC connections for cross-device Echo streaming
 */

class DeviceSyncAgent {
    constructor() {
        this.deviceId = this.generateDeviceId();
        this.connectedDevices = new Map();
        this.webrtcConnections = new Map();
        this.websocketConnection = null;
        this.backendUrl = 'http://localhost:8000';
        this.initialized = false;
        this.deviceInfo = {
            id: this.deviceId,
            type: this.detectDeviceType(),
            name: this.getDeviceName(),
            capabilities: this.getDeviceCapabilities()
        };
    }

    async initialize() {
        console.log('🔗 Initializing Device Sync Agent...');
        
        try {
            // Connect to backend WebSocket for device coordination
            await this.connectToBackend();
            
            // Set up WebRTC capabilities
            this.setupWebRTC();
            
            // Register this device
            await this.registerDevice();
            
            // Set up event handlers
            this.setupEventHandlers();
            
            this.initialized = true;
            console.log('✅ Device Sync Agent initialized successfully');
            
        } catch (error) {
            console.error('❌ Device Sync Agent initialization failed:', error);
            // Continue without backend connection for now
            this.initialized = true;
        }
    }

    generateDeviceId() {
        // Generate unique device ID
        let deviceId = localStorage.getItem('agentlee_device_id');
        if (!deviceId) {
            deviceId = 'device_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('agentlee_device_id', deviceId);
        }
        return deviceId;
    }

    detectDeviceType() {
        const userAgent = navigator.userAgent.toLowerCase();
        const platform = navigator.platform.toLowerCase();
        
        if (/mobile|android|iphone|ipad|ipod|blackberry|iemobile|opera mini/.test(userAgent)) {
            if (/ipad/.test(userAgent) || (platform.includes('mac') && 'ontouchend' in document)) {
                return 'tablet';
            }
            return 'phone';
        } else if (/mac/.test(platform)) {
            return 'laptop';
        } else {
            return 'computer';
        }
    }

    getDeviceName() {
        const deviceType = this.deviceInfo?.type || this.detectDeviceType();
        const platform = navigator.platform;
        return `${platform} ${deviceType.charAt(0).toUpperCase() + deviceType.slice(1)}`;
    }

    getDeviceCapabilities() {
        return {
            screenCapture: 'getDisplayMedia' in navigator.mediaDevices,
            webrtc: 'RTCPeerConnection' in window,
            websockets: 'WebSocket' in window,
            notifications: 'Notification' in window,
            fullscreen: 'requestFullscreen' in document.documentElement
        };
    }

    async connectToBackend() {
        return new Promise((resolve, reject) => {
            try {
                const wsUrl = this.backendUrl.replace('http', 'ws') + '/ws/device_sync';
                this.websocketConnection = new WebSocket(wsUrl);

                this.websocketConnection.onopen = () => {
                    console.log('🔗 Connected to backend WebSocket');
                    resolve();
                };

                this.websocketConnection.onmessage = (event) => {
                    this.handleBackendMessage(JSON.parse(event.data));
                };

                this.websocketConnection.onclose = () => {
                    console.log('🔗 Backend WebSocket connection closed');
                };

                this.websocketConnection.onerror = (error) => {
                    console.error('❌ Backend WebSocket error:', error);
                    reject(error);
                };

                // Timeout after 5 seconds
                setTimeout(() => {
                    if (this.websocketConnection.readyState !== WebSocket.OPEN) {
                        reject(new Error('WebSocket connection timeout'));
                    }
                }, 5000);

            } catch (error) {
                console.error('❌ Failed to connect to backend:', error);
                reject(error);
            }
        });
    }

    setupWebRTC() {
        // WebRTC configuration
        this.rtcConfiguration = {
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' }
            ]
        };
    }

    async registerDevice() {
        if (this.websocketConnection && this.websocketConnection.readyState === WebSocket.OPEN) {
            const message = {
                type: 'register_device',
                device: this.deviceInfo
            };
            
            this.websocketConnection.send(JSON.stringify(message));
            console.log('📱 Device registered:', this.deviceInfo);
        }
    }

    setupEventHandlers() {
        // Listen for device scan requests
        document.addEventListener('requestDeviceAppScan', (event) => {
            this.handleDeviceAppScanRequest(event.detail);
        });

        // Listen for remote app launch requests
        document.addEventListener('launchRemoteApp', (event) => {
            this.handleRemoteAppLaunchRequest(event.detail);
        });
    }

    handleBackendMessage(message) {
        console.log('📨 Backend message:', message);
        // Handle backend messages here
    }

    /**
     * Request application scan from a remote device
     * @param {string} deviceType - Type of device to scan
     * @returns {Promise<Array>} - List of applications
     */
    async requestDeviceAppScan(deviceType) {
        console.log(`🔍 Requesting app scan for ${deviceType}...`);
        
        // For now, return mock data since we don't have real device connections
        return this.getMockAppsForDevice(deviceType);
    }

    /**
     * Launch an application on a remote device
     * @param {string} deviceType - Type of device
     * @param {Object} app - Application to launch
     * @param {string} echoId - Echo session ID
     * @returns {Promise<Object>} - Launch result
     */
    async launchRemoteApp(deviceType, app, echoId) {
        console.log(`🚀 Launching ${app.name} on ${deviceType}...`);
        
        // Mock remote app launch
        return {
            sessionId: `remote_${echoId}_${Date.now()}`,
            success: true,
            message: `${app.name} launched on ${deviceType}`
        };
    }

    /**
     * Start remote mirroring via WebRTC
     * @param {string} deviceType - Type of device
     * @param {string} remoteSessionId - Remote session ID
     * @param {string} echoId - Echo session ID
     * @returns {Promise<Object>} - Mirror result
     */
    async startRemoteMirroring(deviceType, remoteSessionId, echoId) {
        console.log(`🔄 Starting remote mirroring for ${echoId}...`);
        
        // Mock remote mirroring
        return {
            streamId: echoId,
            type: 'mock'
        };
    }

    getMockAppsForDevice(deviceType) {
        const mockApps = {
            phone: [
                { id: 'whatsapp', name: 'WhatsApp', icon: '💬', package: 'com.whatsapp', category: 'Communication' },
                { id: 'instagram', name: 'Instagram', icon: '📷', package: 'com.instagram.android', category: 'Social' },
                { id: 'youtube', name: 'YouTube', icon: '📺', package: 'com.google.android.youtube', category: 'Media' },
                { id: 'gmail', name: 'Gmail', icon: '📧', package: 'com.google.android.gm', category: 'Email' },
                { id: 'maps', name: 'Maps', icon: '🗺️', package: 'com.google.android.apps.maps', category: 'Navigation' },
                { id: 'camera', name: 'Camera', icon: '📸', package: 'com.android.camera2', category: 'Media' }
            ],
            tablet: [
                { id: 'netflix', name: 'Netflix', icon: '🎬', package: 'com.netflix.mediaclient', category: 'Media' },
                { id: 'kindle', name: 'Kindle', icon: '📚', package: 'com.amazon.kindle', category: 'Reading' },
                { id: 'procreate', name: 'Procreate', icon: '🎨', package: 'com.procreate.app', category: 'Graphics' },
                { id: 'notes', name: 'Notes', icon: '📝', package: 'com.apple.mobilenotes', category: 'Productivity' },
                { id: 'photos', name: 'Photos', icon: '🖼️', package: 'com.apple.mobileslideshow', category: 'Media' }
            ],
            laptop: [
                { id: 'firefox', name: 'Firefox', icon: '🦊', path: 'firefox.exe', category: 'Browser' },
                { id: 'photoshop', name: 'Photoshop', icon: '🎨', path: 'photoshop.exe', category: 'Graphics' },
                { id: 'teams', name: 'Teams', icon: '👥', path: 'teams.exe', category: 'Communication' },
                { id: 'outlook', name: 'Outlook', icon: '📧', path: 'outlook.exe', category: 'Email' },
                { id: 'slack', name: 'Slack', icon: '💬', path: 'slack.exe', category: 'Communication' }
            ]
        };

        return mockApps[deviceType] || [];
    }

    getConnectedDevices() {
        return Array.from(this.connectedDevices.values());
    }

    getDeviceInfo() {
        return this.deviceInfo;
    }

    async stopRemoteMirroring(streamId) {
        const webrtcData = this.webrtcConnections.get(streamId);
        if (webrtcData) {
            webrtcData.connection.close();
            this.webrtcConnections.delete(streamId);
            console.log('🔄 Remote mirroring stopped:', streamId);
        }
    }
}

// Create global instance
window.DeviceSyncAgent = new DeviceSyncAgent();
