<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Lee™ - Echo Engine</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Samsung-Style Phone Container */
        .phone-container {
            width: 350px;
            height: 640px;
            background: linear-gradient(145deg, #1e293b 0%, #0f172a 100%);
            border-radius: 25px;
            box-shadow: 0 25px 80px rgba(0,0,0,0.8), 0 0 0 3px rgba(59, 130, 246, 0.3);
            position: relative;
            overflow: hidden;
            cursor: move;
            border: 2px solid rgba(59, 130, 246, 0.2);
        }

        /* Agent Header */
        .agent-header {
            background: linear-gradient(135deg, #1e40af 0%, #3730a3 50%, #374151 100%);
            padding: 20px;
            text-align: center;
            position: relative;
            cursor: grab;
            border-bottom: 1px solid rgba(59, 130, 246, 0.3);
        }

        .agent-header:active {
            cursor: grabbing;
        }

        .agent-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.4), 0 0 0 2px rgba(59, 130, 246, 0.3);
            border: 2px solid rgba(255,255,255,0.2);
            position: relative;
            overflow: hidden;
        }

        .agent-face {
            font-size: 30px;
            animation: blink 3s infinite;
        }

        @keyframes blink {
            0%, 90%, 100% { transform: scale(1); }
            95% { transform: scale(0.9); }
        }

        .agent-name {
            font-size: 18px;
            font-weight: bold;
            color: white;
            margin-bottom: 5px;
        }

        .agent-status {
            font-size: 11px;
            color: rgba(255,255,255,0.8);
        }

        /* Window Controls */
        .window-controls {
            position: absolute;
            top: 10px;
            right: 15px;
            display: flex;
            gap: 6px;
        }

        .control-btn {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            font-size: 10px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .minimize-btn { background: #fbbf24; }
        .close-btn { background: #ef4444; }

        .control-btn:hover {
            transform: scale(1.2);
        }

        /* Echo Launcher Container */
        .echo-launcher {
            height: calc(100% - 120px);
            position: relative;
            overflow: hidden;
        }

        /* Device Pages Container */
        .device-pages {
            display: flex;
            height: 100%;
            transition: transform 0.3s ease;
            width: 400%; /* 4 pages */
        }

        .device-page {
            width: 25%; /* Each page takes 1/4 of container */
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        /* Device Page Header */
        .device-header {
            text-align: center;
            color: white;
            margin-bottom: 10px;
        }

        .device-icon {
            font-size: 40px;
            margin-bottom: 8px;
        }

        .device-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .device-status {
            font-size: 11px;
            color: rgba(255,255,255,0.7);
        }

        /* App Grid */
        .app-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            flex: 1;
            overflow-y: auto;
        }

        .app-tile {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 12px;
            padding: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            text-align: center;
            min-height: 80px;
        }

        .app-tile:hover {
            background: rgba(59, 130, 246, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
        }

        .app-icon {
            font-size: 24px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(59, 130, 246, 0.2);
            border-radius: 8px;
        }

        .app-name {
            font-size: 10px;
            color: white;
            font-weight: 500;
            line-height: 1.2;
        }

        /* Navigation Dots */
        .nav-dots {
            position: absolute;
            bottom: 15px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
        }

        .nav-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-dot.active {
            background: #3b82f6;
            transform: scale(1.2);
        }

        /* Status Bar */
        .status-bar {
            position: absolute;
            bottom: 5px;
            left: 10px;
            right: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 9px;
            color: rgba(255, 255, 255, 0.6);
            z-index: 1000;
        }

        /* Loading State */
        .loading {
            display: none;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            text-align: center;
        }

        .loading.active {
            display: block;
        }

        .spinner {
            width: 30px;
            height: 30px;
            border: 3px solid rgba(255,255,255,0.3);
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .phone-container {
                width: 100vw;
                height: 100vh;
                border-radius: 0;
                position: fixed;
                top: 0;
                left: 0;
                transform: none;
            }
        }
    </style>
</head>
<body>
    <div class="phone-container" id="phoneContainer">
        <!-- Window Controls -->
        <div class="window-controls">
            <button class="control-btn minimize-btn" id="minimizeBtn" title="Minimize">−</button>
            <button class="control-btn close-btn" id="closeBtn" title="Close">×</button>
        </div>

        <!-- Agent Header -->
        <div class="agent-header" id="agentHeader">
            <div class="agent-avatar">
                <div class="agent-face" id="agentFace">🤖</div>
            </div>
            <div class="agent-name">Agent Lee™</div>
            <div class="agent-status" id="agentStatus">Echo Engine Ready</div>
        </div>

        <!-- Echo Launcher -->
        <div class="echo-launcher">
            <!-- Loading State -->
            <div class="loading" id="loadingState">
                <div class="spinner"></div>
                <div>Scanning Applications...</div>
            </div>

            <!-- Device Pages -->
            <div class="device-pages" id="devicePages">
                <!-- My Computer Page -->
                <div class="device-page" id="computerPage">
                    <div class="device-header">
                        <div class="device-icon">🖥️</div>
                        <div class="device-name">My Computer</div>
                        <div class="device-status" id="computerStatus">Scanning...</div>
                    </div>
                    <div class="app-grid" id="computerApps">
                        <!-- Apps will be populated by ApplicationScannerClient.js -->
                    </div>
                </div>

                <!-- My Phone Page -->
                <div class="device-page" id="phonePage">
                    <div class="device-header">
                        <div class="device-icon">📱</div>
                        <div class="device-name">My Phone</div>
                        <div class="device-status" id="phoneStatus">Connecting...</div>
                    </div>
                    <div class="app-grid" id="phoneApps">
                        <!-- Apps will be populated by DeviceSyncAgent.js -->
                    </div>
                </div>

                <!-- My Tablet Page -->
                <div class="device-page" id="tabletPage">
                    <div class="device-header">
                        <div class="device-icon">📱</div>
                        <div class="device-name">My Tablet</div>
                        <div class="device-status" id="tabletStatus">Connecting...</div>
                    </div>
                    <div class="app-grid" id="tabletApps">
                        <!-- Apps will be populated by DeviceSyncAgent.js -->
                    </div>
                </div>

                <!-- My Laptop Page -->
                <div class="device-page" id="laptopPage">
                    <div class="device-header">
                        <div class="device-icon">💻</div>
                        <div class="device-name">My Laptop</div>
                        <div class="device-status" id="laptopStatus">Connecting...</div>
                    </div>
                    <div class="app-grid" id="laptopApps">
                        <!-- Apps will be populated by DeviceSyncAgent.js -->
                    </div>
                </div>
            </div>

            <!-- Navigation Dots -->
            <div class="nav-dots">
                <div class="nav-dot active" data-page="0"></div>
                <div class="nav-dot" data-page="1"></div>
                <div class="nav-dot" data-page="2"></div>
                <div class="nav-dot" data-page="3"></div>
            </div>
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
            <span id="connectionStatus">🟢 Echo Engine Active</span>
            <span id="timeStatus"></span>
        </div>
    </div>

    <!-- Include Echo Engine Scripts -->
    <script src="frontend/js/GUIManager.js"></script>
    <script src="frontend/js/ApplicationScannerClient.js"></script>
    <script src="frontend/js/EchoEngineClient.js"></script>
    <script src="frontend/js/PermissionManagerClient.js"></script>
    <script src="frontend/js/DeviceSyncAgent.js"></script>
    
    <script src="frontend/js/echo-engine-main.js"></script>
</body>
</html>
