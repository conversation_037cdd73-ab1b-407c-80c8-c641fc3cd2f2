/**
 * Agent Lee™ Echo Engine Main Initialization
 * Coordinates all Echo Engine components and manages the 4-device interface
 */

class AgentLeeEchoEngineMain {
    constructor() {
        this.currentPage = 0;
        this.totalPages = 4;
        this.isInitialized = false;
        this.components = {
            guiManager: null,
            applicationScanner: null,
            echoEngine: null,
            permissionManager: null,
            deviceSync: null
        };
    }

    async initialize() {
        console.log('🧠 Initializing Agent Lee Echo Engine...');
        
        try {
            this.initializeElements();
            this.initializeEventListeners();
            this.initializeDragFunctionality();
            this.startStatusUpdates();
            
            // Initialize all Echo Engine components
            await this.initializeEchoComponents();
            
            // Start the Echo Engine
            await this.startEchoEngine();
            
            this.isInitialized = true;
            console.log('✅ Agent Lee Echo Engine fully initialized');
            
        } catch (error) {
            console.error('❌ Echo Engine initialization failed:', error);
            this.showInitializationError(error);
        }
    }

    initializeElements() {
        this.elements = {
            container: document.getElementById('phoneContainer'),
            header: document.getElementById('agentHeader'),
            face: document.getElementById('agentFace'),
            status: document.getElementById('agentStatus'),
            devicePages: document.getElementById('devicePages'),
            navDots: document.querySelectorAll('.nav-dot'),
            minimizeBtn: document.getElementById('minimizeBtn'),
            closeBtn: document.getElementById('closeBtn'),
            loadingState: document.getElementById('loadingState'),
            connectionStatus: document.getElementById('connectionStatus'),
            timeStatus: document.getElementById('timeStatus')
        };
    }

    initializeEventListeners() {
        // Window controls
        this.elements.minimizeBtn?.addEventListener('click', () => this.minimizeWindow());
        this.elements.closeBtn?.addEventListener('click', () => this.closeWindow());

        // Navigation dots
        this.elements.navDots?.forEach((dot, index) => {
            dot.addEventListener('click', () => this.navigateToPage(index));
        });

        // Swipe gestures
        this.initializeSwipeGestures();

        // Echo Engine events
        this.setupEchoEngineEvents();
    }

    setupEchoEngineEvents() {
        // Listen for device updates
        document.addEventListener('devicesUpdated', (event) => {
            this.handleDevicesUpdated(event.detail);
        });

        // Listen for app scan completion
        document.addEventListener('appScanComplete', (event) => {
            this.handleAppScanComplete(event.detail);
        });

        // Listen for Echo creation
        document.addEventListener('echoCreated', (event) => {
            this.handleEchoCreated(event.detail);
        });
    }

    initializeSwipeGestures() {
        if (!this.elements.devicePages) return;

        let startX = 0;
        let startY = 0;
        let isSwipe = false;

        this.elements.devicePages.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
            isSwipe = true;
        });

        this.elements.devicePages.addEventListener('touchmove', (e) => {
            if (!isSwipe) return;
            
            const deltaX = Math.abs(e.touches[0].clientX - startX);
            const deltaY = Math.abs(e.touches[0].clientY - startY);
            
            if (deltaY > deltaX) {
                isSwipe = false; // Vertical scroll
            }
        });

        this.elements.devicePages.addEventListener('touchend', (e) => {
            if (!isSwipe) return;
            
            const endX = e.changedTouches[0].clientX;
            const deltaX = startX - endX;
            
            if (Math.abs(deltaX) > 50) { // Minimum swipe distance
                if (deltaX > 0 && this.currentPage < this.totalPages - 1) {
                    this.navigateToPage(this.currentPage + 1);
                } else if (deltaX < 0 && this.currentPage > 0) {
                    this.navigateToPage(this.currentPage - 1);
                }
            }
            
            isSwipe = false;
        });
    }

    navigateToPage(pageIndex) {
        if (pageIndex < 0 || pageIndex >= this.totalPages) return;
        
        this.currentPage = pageIndex;
        const translateX = -pageIndex * 25; // 25% per page
        
        if (this.elements.devicePages) {
            this.elements.devicePages.style.transform = `translateX(${translateX}%)`;
        }
        
        // Update navigation dots
        this.elements.navDots?.forEach((dot, index) => {
            dot.classList.toggle('active', index === pageIndex);
        });

        // Update status based on current page
        this.updatePageStatus();
    }

    updatePageStatus() {
        const pageNames = ['Computer', 'Phone', 'Tablet', 'Laptop'];
        if (this.elements.status) {
            this.elements.status.textContent = `Echo Engine - ${pageNames[this.currentPage]}`;
        }
    }

    initializeDragFunctionality() {
        if (!this.elements.header || !this.elements.container) return;

        let isDragging = false;
        let currentX = 0;
        let currentY = 0;
        let initialX = 0;
        let initialY = 0;

        this.elements.header.addEventListener('mousedown', (e) => {
            initialX = e.clientX - currentX;
            initialY = e.clientY - currentY;
            isDragging = true;
            this.elements.container.style.cursor = 'grabbing';
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                e.preventDefault();
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;
                
                this.elements.container.style.transform = `translate(calc(-50% + ${currentX}px), calc(-50% + ${currentY}px))`;
            }
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
            if (this.elements.container) {
                this.elements.container.style.cursor = 'move';
            }
        });
    }

    async initializeEchoComponents() {
        this.showLoading(true);
        this.updateStatus('Initializing Echo Components...');

        try {
            // Initialize components in order
            if (window.GUIManager) {
                await window.GUIManager.initialize();
                this.components.guiManager = window.GUIManager;
                console.log('✅ GUI Manager initialized');
            }

            if (window.PermissionManagerClient) {
                await window.PermissionManagerClient.initialize();
                this.components.permissionManager = window.PermissionManagerClient;
                console.log('✅ Permission Manager initialized');
            }

            if (window.DeviceSyncAgent) {
                await window.DeviceSyncAgent.initialize();
                this.components.deviceSync = window.DeviceSyncAgent;
                console.log('✅ Device Sync Agent initialized');
            }

            if (window.EchoEngineClient) {
                await window.EchoEngineClient.initialize();
                this.components.echoEngine = window.EchoEngineClient;
                console.log('✅ Echo Engine Client initialized');
            }

            if (window.ApplicationScannerClient) {
                await window.ApplicationScannerClient.initialize();
                this.components.applicationScanner = window.ApplicationScannerClient;
                console.log('✅ Application Scanner initialized');
            }

        } catch (error) {
            console.error('❌ Component initialization failed:', error);
            throw error;
        }
    }

    async startEchoEngine() {
        this.updateStatus('Starting Echo Engine...');

        try {
            // Start application scanning for all devices
            if (this.components.applicationScanner) {
                await this.components.applicationScanner.scanAllDevices();
            }

            // Update device status
            this.updateDeviceStatuses();

            this.showLoading(false);
            this.updateStatus('Echo Engine Ready');
            
            // Show welcome message
            this.showWelcomeMessage();

        } catch (error) {
            console.error('❌ Echo Engine startup failed:', error);
            this.showLoading(false);
            this.updateStatus('Echo Engine Error');
        }
    }

    updateDeviceStatuses() {
        const deviceTypes = ['computer', 'phone', 'tablet', 'laptop'];
        
        deviceTypes.forEach(deviceType => {
            const statusElement = document.getElementById(`${deviceType}Status`);
            if (statusElement) {
                if (this.components.applicationScanner) {
                    const apps = this.components.applicationScanner.getScannedApps(deviceType);
                    statusElement.textContent = `${apps.length} apps found`;
                } else {
                    statusElement.textContent = 'Ready';
                }
            }
        });
    }

    showWelcomeMessage() {
        // Animate Agent Lee's face
        if (this.elements.face) {
            this.elements.face.style.animation = 'none';
            setTimeout(() => {
                this.elements.face.style.animation = 'blink 3s infinite';
            }, 100);
        }

        // Show welcome notification
        this.showNotification(
            '🧠 Agent Lee Echo Engine Ready!',
            'Tap any app icon to create an Echo and start mirroring applications across your devices.',
            5000
        );
    }

    showNotification(title, message, duration = 3000) {
        const notification = document.createElement('div');
        notification.className = 'echo-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-title">${title}</div>
                <div class="notification-message">${message}</div>
            </div>
        `;

        // Add styles if not already added
        if (!document.getElementById('echo-notification-styles')) {
            const styles = document.createElement('style');
            styles.id = 'echo-notification-styles';
            styles.textContent = `
                .echo-notification {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: linear-gradient(145deg, #3b82f6, #1e40af);
                    color: white;
                    border-radius: 12px;
                    padding: 15px 20px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
                    z-index: 10000;
                    max-width: 350px;
                    animation: slideInNotification 0.3s ease;
                }

                @keyframes slideInNotification {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }

                .notification-title {
                    font-weight: bold;
                    margin-bottom: 5px;
                    font-size: 14px;
                }

                .notification-message {
                    font-size: 12px;
                    opacity: 0.9;
                    line-height: 1.4;
                }
            `;
            document.head.appendChild(styles);
        }

        document.body.appendChild(notification);

        // Auto-remove
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.animation = 'slideInNotification 0.3s ease reverse';
                setTimeout(() => notification.remove(), 300);
            }
        }, duration);
    }

    handleDevicesUpdated(data) {
        console.log('📱 Devices updated:', data);
        this.updateDeviceStatuses();
    }

    handleAppScanComplete(data) {
        console.log('🔍 App scan complete:', data);
        this.updateDeviceStatuses();
    }

    handleEchoCreated(data) {
        console.log('🔄 Echo created:', data);
        this.showNotification(
            `🔄 Echo Created`,
            `${data.appName} is now mirrored and ready to use!`,
            3000
        );
    }

    showLoading(show) {
        if (this.elements.loadingState) {
            this.elements.loadingState.classList.toggle('active', show);
        }
    }

    updateStatus(status) {
        if (this.elements.status) {
            this.elements.status.textContent = status;
        }
    }

    startStatusUpdates() {
        setInterval(() => {
            const now = new Date();
            if (this.elements.timeStatus) {
                this.elements.timeStatus.textContent = now.toLocaleTimeString();
            }
        }, 1000);

        // Update connection status
        setInterval(() => {
            if (this.elements.connectionStatus) {
                const isConnected = this.components.deviceSync?.initialized || false;
                this.elements.connectionStatus.textContent = isConnected ? 
                    '🟢 Echo Engine Active' : '🟡 Echo Engine Local';
            }
        }, 5000);
    }

    minimizeWindow() {
        if (this.elements.container) {
            this.elements.container.style.height = '80px';
            this.elements.container.style.overflow = 'hidden';
        }
    }

    closeWindow() {
        if (confirm('Close Agent Lee Echo Engine?')) {
            if (this.elements.container) {
                this.elements.container.style.display = 'none';
            }
        }
    }

    showInitializationError(error) {
        this.showLoading(false);
        this.updateStatus('Initialization Failed');
        
        this.showNotification(
            '❌ Echo Engine Error',
            `Failed to initialize: ${error.message}`,
            10000
        );
    }

    // Public API methods
    getCurrentPage() {
        return this.currentPage;
    }

    getComponents() {
        return this.components;
    }

    isReady() {
        return this.isInitialized;
    }
}

// Initialize Agent Lee Echo Engine when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.agentLeeEchoEngine = new AgentLeeEchoEngineMain();
    window.agentLeeEchoEngine.initialize();
});
