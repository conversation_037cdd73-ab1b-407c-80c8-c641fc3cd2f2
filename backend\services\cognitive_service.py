"""
Agent Lee™ Cognitive Service
Backend integration for the cognitive architecture
"""

import asyncio
import json
import logging
import time
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Union
import networkx as nx
import numpy as np
from dataclasses import dataclass, asdict
from pathlib import Path

logger = logging.getLogger("agentlee.cognitive")

@dataclass
class CognitiveNode:
    """Represents a node in the cognitive hypergraph"""
    id: str
    node_type: str
    data: Dict[str, Any]
    semantic_vector: Optional[List[float]] = None
    created_at: float = None
    last_accessed: float = None
    access_count: int = 0
    decay_factor: float = 0.95
    integrity_hash: str = ""
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = time.time()
        if self.last_accessed is None:
            self.last_accessed = self.created_at

@dataclass
class CognitiveHyperedge:
    """Represents a hyperedge connecting multiple nodes"""
    id: str
    edge_type: str
    node_ids: List[str]
    weight: float = 0.5
    confidence: float = 0.8
    created_at: float = None
    last_verified: float = None
    volatility: float = 0.3
    chrono_field: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = time.time()
        if self.last_verified is None:
            self.last_verified = self.created_at
        if self.chrono_field is None:
            self.chrono_field = self._generate_chrono_field()
    
    def _generate_chrono_field(self) -> Dict[str, Any]:
        """Generate temporal context metadata"""
        dt = datetime.fromtimestamp(self.created_at)
        return {
            "timestamp": self.created_at,
            "day_of_week": dt.weekday(),
            "hour_of_day": dt.hour,
            "relevance_window": self._determine_relevance_window(dt),
            "decay_rate": "medium"
        }
    
    def _determine_relevance_window(self, dt: datetime) -> List[str]:
        """Determine temporal relevance window"""
        hour = dt.hour
        day = dt.weekday()
        
        if day < 5:  # Weekdays
            if 9 <= hour <= 17:
                return ["work", "weekdays"]
            elif 18 <= hour <= 22:
                return ["evening", "weekdays"]
        
        return ["weekend", "leisure"]

class CognitiveService:
    """Backend cognitive service for Agent Lee"""
    
    def __init__(self):
        self.hypergraph = nx.MultiDiGraph()
        self.nodes: Dict[str, CognitiveNode] = {}
        self.hyperedges: Dict[str, CognitiveHyperedge] = {}
        self.vector_index = None  # Will be initialized with actual vector search
        self.is_initialized = False
        
        # Cognitive metrics
        self.metrics = {
            "total_nodes": 0,
            "total_edges": 0,
            "average_connectivity": 0.0,
            "memory_pressure": 0.0,
            "last_optimization": time.time(),
            "optimization_cycles": 0,
            "healing_events": 0
        }
        
        # Configuration
        self.config = {
            "max_nodes": 100000,
            "max_edges": 500000,
            "optimization_interval": 300,  # 5 minutes
            "healing_interval": 600,       # 10 minutes
            "consolidation_interval": 900, # 15 minutes
            "pruning_threshold": 0.1,
            "volatility_threshold": 0.8
        }
        
        logger.info("Cognitive Service initialized")
    
    async def initialize(self):
        """Initialize the cognitive service"""
        try:
            logger.info("🧠 Initializing Cognitive Service...")
            
            # Initialize vector search (placeholder)
            await self._initialize_vector_search()
            
            # Load existing cognitive data
            await self._load_cognitive_data()
            
            # Start autonomic processes
            self._start_autonomic_processes()
            
            self.is_initialized = True
            logger.info("✅ Cognitive Service initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Cognitive Service: {e}")
            raise
    
    async def _initialize_vector_search(self):
        """Initialize vector search capabilities"""
        # Placeholder for vector search initialization
        # In production, this would initialize FAISS, Pinecone, or similar
        self.vector_index = {
            "vectors": {},
            "dimension": 384  # Typical for sentence transformers
        }
        logger.info("Vector search initialized")
    
    async def _load_cognitive_data(self):
        """Load existing cognitive data from persistence"""
        # Placeholder for loading from database/file system
        logger.info("Cognitive data loaded")
    
    def _start_autonomic_processes(self):
        """Start background autonomic processes"""
        # Start optimization cycle
        asyncio.create_task(self._optimization_loop())
        
        # Start healing cycle
        asyncio.create_task(self._healing_loop())
        
        # Start memory consolidation
        asyncio.create_task(self._consolidation_loop())
        
        logger.info("Autonomic processes started")
    
    async def create_node(self, node_type: str, data: Dict[str, Any], 
                         semantic_vector: Optional[List[float]] = None) -> str:
        """Create a new cognitive node"""
        node_id = f"node_{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"
        
        node = CognitiveNode(
            id=node_id,
            node_type=node_type,
            data=data,
            semantic_vector=semantic_vector,
            integrity_hash=self._calculate_hash(data)
        )
        
        self.nodes[node_id] = node
        self.hypergraph.add_node(node_id, **asdict(node))
        
        # Add to vector index if vector provided
        if semantic_vector and self.vector_index:
            self.vector_index["vectors"][node_id] = semantic_vector
        
        self._update_metrics()
        logger.debug(f"Created cognitive node: {node_id} ({node_type})")
        
        return node_id
    
    async def create_hyperedge(self, edge_type: str, node_ids: List[str], 
                              weight: float = 0.5, confidence: float = 0.8) -> str:
        """Create a hyperedge connecting multiple nodes"""
        edge_id = f"edge_{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"
        
        # Validate that all nodes exist
        for node_id in node_ids:
            if node_id not in self.nodes:
                raise ValueError(f"Node {node_id} does not exist")
        
        hyperedge = CognitiveHyperedge(
            id=edge_id,
            edge_type=edge_type,
            node_ids=node_ids,
            weight=weight,
            confidence=confidence,
            volatility=self._calculate_volatility(edge_type)
        )
        
        self.hyperedges[edge_id] = hyperedge
        
        # Add edges to NetworkX graph
        for i in range(len(node_ids)):
            for j in range(i + 1, len(node_ids)):
                self.hypergraph.add_edge(
                    node_ids[i], 
                    node_ids[j], 
                    edge_id=edge_id,
                    edge_type=edge_type,
                    weight=weight,
                    confidence=confidence
                )
        
        self._update_metrics()
        logger.debug(f"Created hyperedge: {edge_id} ({edge_type}) connecting {len(node_ids)} nodes")
        
        return edge_id
    
    async def semantic_search(self, query_vector: List[float], k: int = 10) -> List[Dict[str, Any]]:
        """Perform semantic search across the cognitive hypergraph"""
        if not self.vector_index or not self.vector_index["vectors"]:
            return []
        
        # Simple cosine similarity search (placeholder)
        # In production, use proper vector search library
        similarities = []
        query_np = np.array(query_vector)
        
        for node_id, vector in self.vector_index["vectors"].items():
            if node_id in self.nodes:
                vector_np = np.array(vector)
                similarity = np.dot(query_np, vector_np) / (
                    np.linalg.norm(query_np) * np.linalg.norm(vector_np)
                )
                similarities.append({
                    "node_id": node_id,
                    "similarity": float(similarity),
                    "node": self.nodes[node_id]
                })
        
        # Sort by similarity and return top k
        similarities.sort(key=lambda x: x["similarity"], reverse=True)
        results = similarities[:k]
        
        # Update access patterns
        for result in results:
            node = self.nodes[result["node_id"]]
            node.last_accessed = time.time()
            node.access_count += 1
        
        return results
    
    async def get_connected_nodes(self, node_id: str, max_depth: int = 2) -> List[str]:
        """Get nodes connected to the given node via hyperedges"""
        if node_id not in self.nodes:
            return []
        
        connected = set()
        queue = [(node_id, 0)]
        visited = {node_id}
        
        while queue:
            current_id, depth = queue.pop(0)
            
            if depth >= max_depth:
                continue
            
            # Get neighbors from NetworkX graph
            neighbors = list(self.hypergraph.neighbors(current_id))
            
            for neighbor_id in neighbors:
                if neighbor_id not in visited:
                    visited.add(neighbor_id)
                    connected.add(neighbor_id)
                    queue.append((neighbor_id, depth + 1))
        
        return list(connected)
    
    async def consolidate_memories(self, node_ids: List[str]) -> bool:
        """Consolidate related memories by strengthening connections"""
        try:
            # Find edges connecting these nodes
            relevant_edges = []
            for edge_id, edge in self.hyperedges.items():
                if any(node_id in edge.node_ids for node_id in node_ids):
                    relevant_edges.append(edge)
            
            # Strengthen connections between frequently accessed nodes
            for edge in relevant_edges:
                connected_nodes = [self.nodes[nid] for nid in edge.node_ids if nid in self.nodes]
                avg_access = sum(node.access_count for node in connected_nodes) / len(connected_nodes)
                
                if avg_access > 5:  # Frequently accessed
                    edge.weight = min(1.0, edge.weight * 1.1)  # Strengthen by 10%
                    edge.confidence = min(1.0, edge.confidence * 1.05)  # Increase confidence
            
            logger.debug(f"Consolidated memories for {len(node_ids)} nodes")
            return True
            
        except Exception as e:
            logger.error(f"Memory consolidation failed: {e}")
            return False
    
    async def prune_weak_connections(self) -> int:
        """Remove weak or volatile connections"""
        pruned_count = 0
        current_time = time.time()
        
        edges_to_remove = []
        
        for edge_id, edge in self.hyperedges.items():
            age = current_time - edge.created_at
            should_prune = (
                edge.weight < self.config["pruning_threshold"] or
                edge.volatility > self.config["volatility_threshold"] or
                (age > 86400 and edge.confidence < 0.3)  # 24 hours old with low confidence
            )
            
            if should_prune:
                edges_to_remove.append(edge_id)
        
        # Remove edges
        for edge_id in edges_to_remove:
            edge = self.hyperedges[edge_id]
            
            # Remove from NetworkX graph
            for i in range(len(edge.node_ids)):
                for j in range(i + 1, len(edge.node_ids)):
                    if self.hypergraph.has_edge(edge.node_ids[i], edge.node_ids[j]):
                        self.hypergraph.remove_edge(edge.node_ids[i], edge.node_ids[j])
            
            # Remove from hyperedges
            del self.hyperedges[edge_id]
            pruned_count += 1
        
        self._update_metrics()
        logger.debug(f"Pruned {pruned_count} weak connections")
        
        return pruned_count
    
    async def perform_integrity_check(self) -> Dict[str, Any]:
        """Check data integrity and identify corruption"""
        corrupted_nodes = []
        
        for node_id, node in self.nodes.items():
            current_hash = self._calculate_hash(node.data)
            if current_hash != node.integrity_hash:
                corrupted_nodes.append(node_id)
        
        integrity_report = {
            "timestamp": time.time(),
            "total_nodes": len(self.nodes),
            "corrupted_nodes": len(corrupted_nodes),
            "corruption_rate": len(corrupted_nodes) / len(self.nodes) if self.nodes else 0,
            "corrupted_node_ids": corrupted_nodes
        }
        
        if corrupted_nodes:
            logger.warning(f"Integrity check found {len(corrupted_nodes)} corrupted nodes")
        
        return integrity_report
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current cognitive metrics"""
        return {
            **self.metrics,
            "timestamp": time.time(),
            "nodes_count": len(self.nodes),
            "edges_count": len(self.hyperedges),
            "graph_density": nx.density(self.hypergraph) if self.hypergraph.number_of_nodes() > 1 else 0
        }
    
    def _update_metrics(self):
        """Update internal metrics"""
        self.metrics["total_nodes"] = len(self.nodes)
        self.metrics["total_edges"] = len(self.hyperedges)
        
        if self.hypergraph.number_of_nodes() > 0:
            total_degree = sum(dict(self.hypergraph.degree()).values())
            self.metrics["average_connectivity"] = total_degree / self.hypergraph.number_of_nodes()
        else:
            self.metrics["average_connectivity"] = 0
    
    def _calculate_hash(self, data: Dict[str, Any]) -> str:
        """Calculate integrity hash for data"""
        import hashlib
        data_str = json.dumps(data, sort_keys=True)
        return hashlib.sha256(data_str.encode()).hexdigest()[:16]
    
    def _calculate_volatility(self, edge_type: str) -> float:
        """Calculate volatility based on edge type"""
        volatility_rates = {
            "causal": 0.2,
            "temporal": 0.4,
            "semantic": 0.3,
            "emotional": 0.6,
            "procedural": 0.1
        }
        return volatility_rates.get(edge_type, 0.3)
    
    async def _optimization_loop(self):
        """Background optimization loop"""
        while True:
            try:
                await asyncio.sleep(self.config["optimization_interval"])
                
                # Perform optimization cycle
                await self._perform_optimization()
                
                self.metrics["optimization_cycles"] += 1
                self.metrics["last_optimization"] = time.time()
                
            except Exception as e:
                logger.error(f"Optimization loop error: {e}")
    
    async def _healing_loop(self):
        """Background healing loop"""
        while True:
            try:
                await asyncio.sleep(self.config["healing_interval"])
                
                # Perform integrity check and healing
                integrity_report = await self.perform_integrity_check()
                
                if integrity_report["corrupted_nodes"] > 0:
                    # Trigger healing process
                    await self._heal_corrupted_nodes(integrity_report["corrupted_node_ids"])
                    self.metrics["healing_events"] += 1
                
            except Exception as e:
                logger.error(f"Healing loop error: {e}")
    
    async def _consolidation_loop(self):
        """Background memory consolidation loop"""
        while True:
            try:
                await asyncio.sleep(self.config["consolidation_interval"])
                
                # Identify important memories for consolidation
                important_nodes = self._identify_important_nodes()
                
                if important_nodes:
                    await self.consolidate_memories(important_nodes)
                
            except Exception as e:
                logger.error(f"Consolidation loop error: {e}")
    
    async def _perform_optimization(self):
        """Perform optimization cycle"""
        # Prune weak connections
        pruned = await self.prune_weak_connections()
        
        # Update metrics
        self._update_metrics()
        
        logger.debug(f"Optimization cycle complete: pruned {pruned} connections")
    
    async def _heal_corrupted_nodes(self, corrupted_node_ids: List[str]):
        """Heal corrupted nodes"""
        for node_id in corrupted_node_ids:
            try:
                # Recalculate integrity hash
                node = self.nodes[node_id]
                node.integrity_hash = self._calculate_hash(node.data)
                logger.debug(f"Healed corrupted node: {node_id}")
            except Exception as e:
                logger.error(f"Failed to heal node {node_id}: {e}")
    
    def _identify_important_nodes(self) -> List[str]:
        """Identify important nodes for consolidation"""
        # Simple heuristic: nodes with high access count
        important_nodes = []
        
        for node_id, node in self.nodes.items():
            if node.access_count > 10:  # Frequently accessed
                important_nodes.append(node_id)
        
        return important_nodes[:50]  # Limit to top 50
