#!/usr/bin/env python3
"""
Agent Lee™ Production Startup Script
Complete production verification, fixes, and startup
"""

import os
import sys
import time
import subprocess
import platform
import webbrowser
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("agentlee_production.log")
    ]
)

logger = logging.getLogger("agentlee.production")

class AgentLeeProductionStarter:
    """Complete production startup system"""
    
    def __init__(self):
        self.start_time = time.time()
        self.backend_process = None
        self.issues_fixed = []
        self.verification_passed = False
        
        print("🧠 AGENT LEE™ PRODUCTION STARTUP SYSTEM")
        print("=" * 60)
        logger.info("Agent Lee™ Production Startup System initialized")
    
    def run_production_startup(self):
        """Complete production startup process"""
        try:
            # Step 1: System verification
            if not self.verify_system():
                return False
            
            # Step 2: Fix common issues
            if not self.fix_common_issues():
                return False
            
            # Step 3: Install dependencies
            if not self.ensure_dependencies():
                return False
            
            # Step 4: Start system
            if not self.start_system():
                return False
            
            # Step 5: Verify startup
            if not self.verify_startup():
                return False
            
            # Step 6: Display success
            self.display_success()
            
            # Step 7: Monitor system
            self.monitor_system()
            
            return True
            
        except KeyboardInterrupt:
            logger.info("🛑 Interrupted by user")
            self.shutdown()
            return True
        except Exception as e:
            logger.error(f"❌ Production startup failed: {e}")
            self.shutdown()
            return False
    
    def verify_system(self):
        """Verify system requirements"""
        logger.info("🔍 Verifying system requirements...")
        
        # Check Python version
        python_version = sys.version_info
        if python_version < (3, 8):
            logger.error(f"❌ Python 3.8+ required, found {python_version[0]}.{python_version[1]}")
            return False
        
        logger.info(f"✅ Python {python_version[0]}.{python_version[1]}.{python_version[2]}")
        
        # Check platform
        system = platform.system()
        logger.info(f"✅ Platform: {system} {platform.release()}")
        
        return True
    
    def fix_common_issues(self):
        """Fix common production issues"""
        logger.info("🔧 Fixing common issues...")
        
        # Create missing directories
        required_dirs = [
            "backend",
            "backend/services", 
            "frontend/js/cognitive",
            "logs",
            "data"
        ]
        
        for dir_path in required_dirs:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
            self.issues_fixed.append(f"Created directory: {dir_path}")
        
        # Create .env file if missing
        env_file = Path(".env")
        if not env_file.exists():
            env_content = """# Agent Lee™ Production Environment
AGENTLEE_ENV=production
AGENTLEE_LOG_LEVEL=INFO
SECRET_KEY=your-secret-key-here
GEMINI_API_KEY=your-gemini-api-key-here
"""
            env_file.write_text(env_content)
            self.issues_fixed.append("Created .env file")
            logger.info("✅ Created .env file with defaults")
        
        # Ensure production HTML exists
        prod_html = Path("index_production.html")
        if not prod_html.exists():
            logger.warning("⚠️ index_production.html not found - will use fallback")
        
        logger.info(f"✅ Fixed {len(self.issues_fixed)} issues")
        return True
    
    def ensure_dependencies(self):
        """Ensure minimal dependencies are installed"""
        logger.info("📦 Checking dependencies...")
        
        # Check if we can import essential packages
        essential_packages = [
            ('fastapi', 'fastapi'),
            ('uvicorn', 'uvicorn'),
            ('pydantic', 'pydantic'),
            ('psutil', 'psutil'),
            ('requests', 'requests'),
            ('python-dotenv', 'dotenv')
        ]
        
        missing_packages = []
        
        for package_name, import_name in essential_packages:
            try:
                __import__(import_name)
                logger.info(f"✅ {package_name}")
            except ImportError:
                missing_packages.append(package_name)
                logger.warning(f"❌ {package_name} - Missing")
        
        # Install missing packages
        if missing_packages:
            logger.info(f"📦 Installing missing packages: {missing_packages}")
            
            try:
                # Try to install minimal requirements
                requirements_file = Path("backend/requirements_minimal.txt")
                if requirements_file.exists():
                    cmd = [sys.executable, "-m", "pip", "install", "-r", str(requirements_file)]
                else:
                    # Install essential packages individually
                    cmd = [sys.executable, "-m", "pip", "install"] + missing_packages
                
                result = subprocess.run(cmd, capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info("✅ Dependencies installed successfully")
                else:
                    logger.error(f"❌ Failed to install dependencies: {result.stderr}")
                    logger.info("💡 Please install manually: pip install fastapi uvicorn pydantic psutil requests python-dotenv")
                    return False
                    
            except Exception as e:
                logger.error(f"❌ Dependency installation failed: {e}")
                return False
        
        return True
    
    def start_system(self):
        """Start the production system"""
        logger.info("🚀 Starting Agent Lee™ Production System...")
        
        try:
            # Determine which controller to use
            controllers = [
                "backend/agentlee_controller_production.py",
                "backend/agentlee_controller_v2.py"
            ]
            
            controller_file = None
            for controller in controllers:
                if Path(controller).exists():
                    controller_file = controller
                    break
            
            if not controller_file:
                logger.error("❌ No controller file found")
                return False
            
            logger.info(f"🔧 Using controller: {controller_file}")
            
            # Start the backend
            cmd = [
                sys.executable, "-m", "uvicorn",
                f"{Path(controller_file).stem}:app",
                "--host", "localhost",
                "--port", "8000",
                "--log-level", "info"
            ]
            
            self.backend_process = subprocess.Popen(
                cmd,
                cwd="backend",
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True
            )
            
            logger.info("✅ Backend process started")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start system: {e}")
            return False
    
    def verify_startup(self, timeout=30):
        """Verify the system started correctly"""
        logger.info("⏳ Verifying system startup...")
        
        # Check if we can import requests
        try:
            import requests
        except ImportError:
            logger.warning("⚠️ Cannot verify startup - requests not available")
            time.sleep(5)  # Give it some time anyway
            return True
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get("http://localhost:8000/", timeout=5)
                if response.status_code == 200:
                    logger.info("✅ System startup verified")
                    return True
            except requests.exceptions.RequestException:
                pass
            
            # Check if process is still running
            if self.backend_process and self.backend_process.poll() is not None:
                logger.error("❌ Backend process terminated unexpectedly")
                return False
            
            time.sleep(1)
        
        logger.warning("⚠️ Could not verify startup within timeout, but continuing...")
        return True
    
    def display_success(self):
        """Display success information"""
        uptime = time.time() - self.start_time
        
        print("\n" + "="*60)
        print("🧠 AGENT LEE™ PRODUCTION SYSTEM - OPERATIONAL")
        print("="*60)
        print(f"🚀 Status: READY")
        print(f"⏱️  Startup Time: {uptime:.2f} seconds")
        print(f"🌐 Frontend: http://localhost:8000/app")
        print(f"🔧 API: http://localhost:8000")
        print(f"📚 API Docs: http://localhost:8000/docs")
        print(f"🏥 Health Check: http://localhost:8000/api/health")
        print("="*60)
        
        if self.issues_fixed:
            print("🔧 ISSUES FIXED:")
            for issue in self.issues_fixed:
                print(f"   • {issue}")
            print("="*60)
        
        print("💡 Press Ctrl+C to shutdown gracefully")
        print("="*60)
        
        # Try to open browser
        try:
            webbrowser.open("http://localhost:8000/app")
            logger.info("🌐 Browser opened")
        except Exception as e:
            logger.warning(f"⚠️ Could not open browser: {e}")
    
    def monitor_system(self):
        """Monitor the running system"""
        logger.info("👁️ Monitoring system...")
        
        try:
            while True:
                # Check if backend process is still running
                if self.backend_process and self.backend_process.poll() is not None:
                    logger.error("❌ Backend process terminated unexpectedly")
                    break
                
                # Read any output from backend
                if self.backend_process and self.backend_process.stdout:
                    try:
                        line = self.backend_process.stdout.readline()
                        if line and any(keyword in line.lower() for keyword in ['error', 'warning', 'started']):
                            logger.info(f"Backend: {line.strip()}")
                    except:
                        pass
                
                time.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("🛑 Shutdown signal received")
            self.shutdown()
    
    def shutdown(self):
        """Graceful shutdown"""
        logger.info("🛑 Shutting down Agent Lee™...")
        
        if self.backend_process:
            logger.info("🔧 Terminating backend process...")
            self.backend_process.terminate()
            
            try:
                self.backend_process.wait(timeout=10)
                logger.info("✅ Backend shutdown complete")
            except subprocess.TimeoutExpired:
                logger.warning("⚠️ Force killing backend process...")
                self.backend_process.kill()
        
        uptime = time.time() - self.start_time
        logger.info(f"✅ Agent Lee™ shutdown complete. Uptime: {uptime:.2f} seconds")

def main():
    """Main entry point"""
    starter = AgentLeeProductionStarter()
    
    try:
        success = starter.run_production_startup()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
