# Agent Lee™ Minimal Production Requirements
# Essential dependencies only - install additional packages as needed

# --- Core Framework (Required) ---
fastapi==0.111.0
uvicorn[standard]==0.29.0
pydantic==2.8.2
python-multipart==0.0.9

# --- Database (Required) ---
sqlalchemy==2.0.31
aiosqlite==0.20.0

# --- AI Integration (Required) ---
google-generativeai==0.7.2

# --- System & Utilities (Required) ---
psutil==5.9.8
requests==2.31.0
python-dotenv==1.0.1
aiofiles==23.2.1

# --- Security (Required) ---
passlib[bcrypt]==1.7.4

# --- Windows Support ---
pywin32==306; sys_platform == "win32"

# --- Voice Support (Required for Agent Lee) ---
pyttsx3==2.90

# --- Optional: Install these for full cognitive features ---
# networkx==3.2.1         # Cognitive hypergraph
# redis==5.0.1            # Cognitive caching  
# openai==1.35.13         # OpenAI integration
# anthropic==0.31.1       # Claude integration
# pyaudio==0.2.13         # Microphone input
# SpeechRecognition==3.10.1  # Speech recognition
# playwright==1.44.0      # Browser automation
