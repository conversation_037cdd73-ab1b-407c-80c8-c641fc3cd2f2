/**
 * Agent Lee™ Floating Widget Implementation
 * True floating widget with minimize to circle, draggable, and card spawning
 */

class AgentLeeWidget {
    constructor() {
        this.isMinimized = false;
        this.isListening = false;
        this.isDragging = false;
        this.spawnedCards = [];
        this.currentDevice = 'computer';
        this.apiUrl = 'http://localhost:8000';
        this.conversationHistory = [];
        
        this.apps = {
            computer: [
                { id: 'chrome', name: 'Chrome', icon: '🌐' },
                { id: 'notepad', name: 'Notepad', icon: '📝' },
                { id: 'calculator', name: 'Calculator', icon: '🧮' },
                { id: 'paint', name: 'Paint', icon: '🎨' },
                { id: 'word', name: 'Word', icon: '📄' },
                { id: 'excel', name: 'Excel', icon: '📊' },
                { id: 'vscode', name: 'VS Code', icon: '💻' },
                { id: 'discord', name: 'Discord', icon: '💬' }
            ],
            phone: [
                { id: 'whatsapp', name: 'WhatsApp', icon: '💬' },
                { id: 'instagram', name: 'Instagram', icon: '📷' },
                { id: 'youtube', name: 'YouTube', icon: '📺' },
                { id: 'gmail', name: 'Gmail', icon: '📧' },
                { id: 'maps', name: 'Maps', icon: '🗺️' },
                { id: 'camera', name: 'Camera', icon: '📸' }
            ],
            tablet: [
                { id: 'netflix', name: 'Netflix', icon: '🎬' },
                { id: 'kindle', name: 'Kindle', icon: '📚' },
                { id: 'procreate', name: 'Procreate', icon: '🎨' },
                { id: 'notes', name: 'Notes', icon: '📝' }
            ],
            laptop: [
                { id: 'firefox', name: 'Firefox', icon: '🦊' },
                { id: 'photoshop', name: 'Photoshop', icon: '🎨' },
                { id: 'teams', name: 'Teams', icon: '👥' },
                { id: 'outlook', name: 'Outlook', icon: '📧' }
            ]
        };

        this.initialize();
    }

    initialize() {
        this.initializeElements();
        this.initializeEventListeners();
        this.initializeDragFunctionality();
        this.loadApplications();
        this.updateWeatherAndTraffic();
        this.startStatusUpdates();
        this.greetUser();
    }

    initializeElements() {
        this.elements = {
            widget: document.getElementById('agentWidget'),
            minimizedView: document.getElementById('minimizedView'),
            widgetContent: document.getElementById('widgetContent'),
            header: document.getElementById('widgetHeader'),
            face: document.getElementById('agentFace'),
            status: document.getElementById('agentStatus'),
            conversation: document.getElementById('conversationArea'),
            input: document.getElementById('messageInput'),
            sendBtn: document.getElementById('sendBtn'),
            voiceBtn: document.getElementById('voiceBtn'),
            minimizeBtn: document.getElementById('minimizeBtn'),
            closeBtn: document.getElementById('closeBtn'),
            spawnBtn: document.getElementById('spawnBtn'),
            weatherInfo: document.getElementById('weatherInfo'),
            trafficInfo: document.getElementById('trafficInfo'),
            appGrid: document.getElementById('appGrid'),
            connectionStatus: document.getElementById('connectionStatus'),
            timeStatus: document.getElementById('timeStatus'),
            listeningIndicator: document.getElementById('listeningIndicator')
        };
    }

    initializeEventListeners() {
        // Widget controls
        this.elements.minimizeBtn.addEventListener('click', () => this.toggleMinimize());
        this.elements.closeBtn.addEventListener('click', () => this.closeWidget());
        this.elements.spawnBtn.addEventListener('click', () => this.spawnNewCard());

        // Conversation controls
        this.elements.sendBtn.addEventListener('click', () => this.sendMessage());
        this.elements.input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.sendMessage();
        });
        this.elements.voiceBtn.addEventListener('click', () => this.toggleVoice());

        // Feature buttons
        document.getElementById('appsBtn').addEventListener('click', () => this.handleApps());
        document.getElementById('webBtn').addEventListener('click', () => this.handleWeb());
        document.getElementById('emailBtn').addEventListener('click', () => this.handleEmail());
        document.getElementById('newsBtn').addEventListener('click', () => this.handleNews());

        // Device tabs
        document.querySelectorAll('.device-tab').forEach(tab => {
            tab.addEventListener('click', () => this.switchDevice(tab.dataset.device));
        });

        // Minimize on double-click minimized view
        this.elements.minimizedView.addEventListener('dblclick', () => this.toggleMinimize());
    }

    initializeDragFunctionality() {
        let dragTarget = null;
        let startX = 0;
        let startY = 0;
        let startLeft = 0;
        let startTop = 0;

        // Make widget draggable
        const makeDraggable = (element, handle) => {
            handle.addEventListener('mousedown', (e) => {
                dragTarget = element;
                startX = e.clientX;
                startY = e.clientY;
                const rect = element.getBoundingClientRect();
                startLeft = rect.left;
                startTop = rect.top;
                element.style.cursor = 'grabbing';
                e.preventDefault();
            });
        };

        // Main widget dragging
        makeDraggable(this.elements.widget, this.elements.header);
        makeDraggable(this.elements.widget, this.elements.minimizedView);

        document.addEventListener('mousemove', (e) => {
            if (dragTarget) {
                const deltaX = e.clientX - startX;
                const deltaY = e.clientY - startY;
                const newLeft = startLeft + deltaX;
                const newTop = startTop + deltaY;

                // Keep within screen bounds
                const maxLeft = window.innerWidth - dragTarget.offsetWidth;
                const maxTop = window.innerHeight - dragTarget.offsetHeight;

                dragTarget.style.left = Math.max(0, Math.min(newLeft, maxLeft)) + 'px';
                dragTarget.style.top = Math.max(0, Math.min(newTop, maxTop)) + 'px';
                dragTarget.style.right = 'auto';
                dragTarget.style.bottom = 'auto';
            }
        });

        document.addEventListener('mouseup', () => {
            if (dragTarget) {
                dragTarget.style.cursor = 'move';
                dragTarget = null;
            }
        });
    }

    toggleMinimize() {
        this.isMinimized = !this.isMinimized;
        this.elements.widget.classList.toggle('minimized', this.isMinimized);
        
        if (this.isMinimized) {
            this.updateStatus('Minimized');
        } else {
            this.updateStatus('Ready');
        }
    }

    spawnNewCard() {
        const cardId = 'card_' + Date.now();
        const card = this.createSpawnedCard(cardId, 'New Echo Card', 'This is a spawned Echo card that mirrors an application.');
        
        // Position new card offset from main widget
        const widgetRect = this.elements.widget.getBoundingClientRect();
        card.style.left = (widgetRect.left + 50) + 'px';
        card.style.top = (widgetRect.top + 50) + 'px';
        
        document.body.appendChild(card);
        this.spawnedCards.push({ id: cardId, element: card });
        
        this.addMessage(`✅ New Echo card spawned!`, 'agent');
        this.speak('New Echo card created');
    }

    createSpawnedCard(id, title, content) {
        const card = document.createElement('div');
        card.className = 'spawned-card';
        card.id = id;
        
        card.innerHTML = `
            <div class="spawned-header">
                <div class="spawned-title">${title}</div>
                <div class="widget-controls">
                    <button class="control-btn close-btn" onclick="agentLee.closeSpawnedCard('${id}')">×</button>
                </div>
            </div>
            <div class="spawned-content">
                ${content}
            </div>
        `;

        // Make spawned card draggable
        const header = card.querySelector('.spawned-header');
        this.makeElementDraggable(card, header);

        return card;
    }

    makeElementDraggable(element, handle) {
        let isDragging = false;
        let startX = 0;
        let startY = 0;
        let startLeft = 0;
        let startTop = 0;

        handle.addEventListener('mousedown', (e) => {
            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;
            const rect = element.getBoundingClientRect();
            startLeft = rect.left;
            startTop = rect.top;
            handle.style.cursor = 'grabbing';
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                const deltaX = e.clientX - startX;
                const deltaY = e.clientY - startY;
                element.style.left = (startLeft + deltaX) + 'px';
                element.style.top = (startTop + deltaY) + 'px';
            }
        });

        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                handle.style.cursor = 'grab';
            }
        });
    }

    closeSpawnedCard(cardId) {
        const cardIndex = this.spawnedCards.findIndex(card => card.id === cardId);
        if (cardIndex !== -1) {
            this.spawnedCards[cardIndex].element.remove();
            this.spawnedCards.splice(cardIndex, 1);
        }
    }

    async sendMessage() {
        const message = this.elements.input.value.trim();
        if (!message) return;

        this.addMessage(message, 'user');
        this.elements.input.value = '';

        try {
            const response = await this.sendToBackend(message);
            this.addMessage(response.message, 'agent');
            
            if (response.speak) {
                this.speak(response.message);
            }

            if (response.action) {
                await this.handleAction(response.action);
            }
        } catch (error) {
            this.addMessage("I'm having trouble connecting. Please try again.", 'agent');
        }
    }

    async sendToBackend(message) {
        try {
            const response = await fetch(`${this.apiUrl}/api/chat`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ message, history: this.conversationHistory })
            });

            if (response.ok) {
                return await response.json();
            }
        } catch (error) {
            console.error('Backend error:', error);
        }

        // Fallback response
        return {
            message: this.generateResponse(message),
            speak: true,
            action: this.detectAction(message)
        };
    }

    generateResponse(message) {
        const lower = message.toLowerCase();
        
        if (lower.includes('hello') || lower.includes('hi')) {
            return "Hello! I'm Agent Lee, your floating AI assistant. I can open apps, create Echoes, and help with tasks!";
        } else if (lower.includes('open') || lower.includes('launch')) {
            return "I can help you open applications! Click on any app in the Echo Engine below, or tell me which specific app you'd like to open.";
        } else if (lower.includes('weather')) {
            return "The current weather is shown in the weather card above. I can also get more detailed weather information if needed.";
        } else if (lower.includes('spawn') || lower.includes('create')) {
            return "I can spawn new Echo cards for you! Click the + button in my header to create a new floating card.";
        } else {
            return "I'm Agent Lee, your floating AI assistant! I can open apps, create Echo cards, check weather, and help with many tasks. What would you like me to do?";
        }
    }

    detectAction(message) {
        const lower = message.toLowerCase();
        
        if (lower.includes('open chrome')) {
            return { type: 'open_app', app: 'chrome' };
        } else if (lower.includes('spawn') || lower.includes('create card')) {
            return { type: 'spawn_card' };
        }
        
        return null;
    }

    async handleAction(action) {
        switch (action.type) {
            case 'open_app':
                await this.openApplication(action.app);
                break;
            case 'spawn_card':
                this.spawnNewCard();
                break;
        }
    }

    async openApplication(appName) {
        try {
            const response = await fetch(`${this.apiUrl}/api/open_app`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ app: appName })
            });

            const result = await response.json();
            
            if (result.status === 'success') {
                this.addMessage(`✅ ${appName} opened successfully!`, 'agent');
                this.speak(`${appName} is now open`);
            } else {
                this.addMessage(`❌ Could not open ${appName}`, 'agent');
            }
        } catch (error) {
            this.addMessage(`❌ Error opening ${appName}`, 'agent');
        }
    }

    toggleVoice() {
        if (this.isListening) {
            this.stopListening();
        } else {
            this.startListening();
        }
    }

    startListening() {
        this.isListening = true;
        this.elements.voiceBtn.classList.add('active');
        this.elements.listeningIndicator.classList.add('active');
        this.updateStatus('Listening...');

        if ('webkitSpeechRecognition' in window) {
            const recognition = new webkitSpeechRecognition();
            recognition.continuous = false;
            recognition.interimResults = false;
            recognition.lang = 'en-US';

            recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                this.elements.input.value = transcript;
                this.sendMessage();
            };

            recognition.onerror = () => {
                this.stopListening();
                this.addMessage("Sorry, I couldn't hear you clearly.", 'agent');
            };

            recognition.onend = () => {
                this.stopListening();
            };

            recognition.start();
        } else {
            this.stopListening();
            this.addMessage("Voice recognition not supported in this browser.", 'agent');
        }
    }

    stopListening() {
        this.isListening = false;
        this.elements.voiceBtn.classList.remove('active');
        this.elements.listeningIndicator.classList.remove('active');
        this.updateStatus('Ready');
    }

    async speak(text) {
        try {
            await fetch(`${this.apiUrl}/api/speak`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ text })
            });
        } catch (error) {
            // Fallback to browser speech
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(text);
                utterance.rate = 0.9;
                speechSynthesis.speak(utterance);
            }
        }
    }

    addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        messageDiv.textContent = text;
        
        this.elements.conversation.appendChild(messageDiv);
        this.elements.conversation.scrollTop = this.elements.conversation.scrollHeight;

        this.conversationHistory.push({ sender, text, timestamp: Date.now() });
        
        if (this.conversationHistory.length > 20) {
            this.conversationHistory = this.conversationHistory.slice(-20);
        }
    }

    loadApplications() {
        this.updateAppGrid();
    }

    switchDevice(deviceType) {
        this.currentDevice = deviceType;
        
        document.querySelectorAll('.device-tab').forEach(tab => {
            tab.classList.toggle('active', tab.dataset.device === deviceType);
        });

        this.updateAppGrid();
    }

    updateAppGrid() {
        const apps = this.apps[this.currentDevice] || [];
        this.elements.appGrid.innerHTML = '';

        apps.forEach(app => {
            const appTile = document.createElement('div');
            appTile.className = 'app-tile';
            appTile.innerHTML = `
                <div class="app-icon">${app.icon}</div>
                <div class="app-name">${app.name}</div>
            `;
            
            appTile.addEventListener('click', () => this.handleAppClick(app));
            this.elements.appGrid.appendChild(appTile);
        });
    }

    handleAppClick(app) {
        if (this.currentDevice === 'computer') {
            this.openApplication(app.id);
        } else {
            // Create Echo for remote device app
            const echoCard = this.createSpawnedCard(
                `echo_${app.id}_${Date.now()}`,
                `Echo: ${app.name}`,
                `🔄 Mirroring ${app.name} from your ${this.currentDevice}...`
            );
            
            const widgetRect = this.elements.widget.getBoundingClientRect();
            echoCard.style.left = (widgetRect.left + 100) + 'px';
            echoCard.style.top = (widgetRect.top + 100) + 'px';
            
            document.body.appendChild(echoCard);
            this.spawnedCards.push({ id: echoCard.id, element: echoCard });
            
            this.addMessage(`🔄 Creating Echo for ${app.name} on ${this.currentDevice}...`, 'agent');
            this.speak(`Creating Echo for ${app.name}`);
        }
    }

    updateWeatherAndTraffic() {
        const weather = ['72°F', '68°F', '75°F', '70°F'];
        const traffic = ['Light', 'Moderate', 'Heavy', 'Clear'];
        
        this.elements.weatherInfo.textContent = weather[Math.floor(Math.random() * weather.length)];
        this.elements.trafficInfo.textContent = traffic[Math.floor(Math.random() * traffic.length)];
    }

    startStatusUpdates() {
        setInterval(() => {
            this.elements.timeStatus.textContent = new Date().toLocaleTimeString();
        }, 1000);

        setInterval(() => {
            this.updateWeatherAndTraffic();
        }, 300000);
    }

    async greetUser() {
        await new Promise(resolve => setTimeout(resolve, 1000));
        this.speak("Hello! I'm Agent Lee, your floating AI assistant. I can open apps, create Echo cards, and help with tasks!");
        this.updateStatus("Ready and listening");
    }

    updateStatus(status) {
        this.elements.status.textContent = status;
    }

    closeWidget() {
        if (confirm('Close Agent Lee?')) {
            this.elements.widget.style.display = 'none';
        }
    }

    // Feature handlers
    handleApps() {
        this.elements.input.value = "Show me available applications";
        this.sendMessage();
    }

    handleWeb() {
        this.elements.input.value = "Search the web for ";
        this.elements.input.focus();
    }

    handleEmail() {
        this.elements.input.value = "Open my email";
        this.sendMessage();
    }

    handleNews() {
        this.elements.input.value = "Show me the latest news";
        this.sendMessage();
    }
}

// Initialize Agent Lee Widget when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.agentLee = new AgentLeeWidget();
});
