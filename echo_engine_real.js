/**
 * Agent Lee™ REAL Echo Engine
 * Actually mirrors applications using desktop capture and WebRTC
 */

class AgentLeeRealEchoEngine {
    constructor() {
        this.isMinimized = false;
        this.isListening = false;
        this.echoMirrors = [];
        this.currentDevice = 'computer';
        this.apiUrl = 'http://localhost:8000';
        this.conversationHistory = [];
        this.mediaStream = null;
        
        // Real application data with executable paths
        this.apps = {
            computer: [
                { id: 'chrome', name: 'Chrome', icon: '🌐', executable: 'chrome.exe', windowTitle: 'Chrome' },
                { id: 'notepad', name: 'Notepad', icon: '📝', executable: 'notepad.exe', windowTitle: 'Notepad' },
                { id: 'calculator', name: 'Calculator', icon: '🧮', executable: 'calc.exe', windowTitle: 'Calculator' },
                { id: 'paint', name: 'Paint', icon: '🎨', executable: 'mspaint.exe', windowTitle: 'Paint' },
                { id: 'word', name: 'Word', icon: '📄', executable: 'winword.exe', windowTitle: 'Word' },
                { id: 'excel', name: 'Excel', icon: '📊', executable: 'excel.exe', windowTitle: 'Excel' },
                { id: 'vscode', name: 'VS Code', icon: '💻', executable: 'code.exe', windowTitle: 'Visual Studio Code' },
                { id: 'discord', name: 'Discord', icon: '💬', executable: 'discord.exe', windowTitle: 'Discord' }
            ],
            phone: [
                { id: 'whatsapp', name: 'WhatsApp', icon: '💬' },
                { id: 'instagram', name: 'Instagram', icon: '📷' },
                { id: 'youtube', name: 'YouTube', icon: '📺' },
                { id: 'gmail', name: 'Gmail', icon: '📧' },
                { id: 'maps', name: 'Maps', icon: '🗺️' },
                { id: 'camera', name: 'Camera', icon: '📸' }
            ],
            tablet: [
                { id: 'netflix', name: 'Netflix', icon: '🎬' },
                { id: 'kindle', name: 'Kindle', icon: '📚' },
                { id: 'procreate', name: 'Procreate', icon: '🎨' },
                { id: 'notes', name: 'Notes', icon: '📝' }
            ],
            laptop: [
                { id: 'firefox', name: 'Firefox', icon: '🦊' },
                { id: 'photoshop', name: 'Photoshop', icon: '🎨' },
                { id: 'teams', name: 'Teams', icon: '👥' },
                { id: 'outlook', name: 'Outlook', icon: '📧' }
            ]
        };

        this.initialize();
    }

    initialize() {
        this.initializeElements();
        this.initializeEventListeners();
        this.initializeDragFunctionality();
        this.loadApplications();
        this.startStatusUpdates();
        this.greetUser();
    }

    initializeElements() {
        this.elements = {
            card: document.getElementById('agentCard'),
            minimizedBubble: document.getElementById('minimizedBubble'),
            cardContent: document.getElementById('cardContent'),
            header: document.getElementById('cardHeader'),
            face: document.getElementById('agentFace'),
            status: document.getElementById('agentStatus'),
            chat: document.getElementById('chatArea'),
            input: document.getElementById('messageInput'),
            sendBtn: document.getElementById('sendBtn'),
            voiceBtn: document.getElementById('voiceBtn'),
            minimizeBtn: document.getElementById('minimizeBtn'),
            closeBtn: document.getElementById('closeBtn'),
            appGrid: document.getElementById('appGrid'),
            connectionStatus: document.getElementById('connectionStatus'),
            timeStatus: document.getElementById('timeStatus'),
            listeningIndicator: document.getElementById('listeningIndicator')
        };
    }

    initializeEventListeners() {
        // Card controls
        this.elements.minimizeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleMinimize();
        });
        
        this.elements.closeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.closeWidget();
        });

        // Chat controls
        this.elements.sendBtn.addEventListener('click', () => this.sendMessage());
        this.elements.input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.sendMessage();
        });
        this.elements.voiceBtn.addEventListener('click', () => this.toggleVoice());

        // Device tabs
        document.querySelectorAll('.device-tab').forEach(tab => {
            tab.addEventListener('click', () => this.switchDevice(tab.dataset.device));
        });

        // Restore from minimized
        this.elements.minimizedBubble.addEventListener('dblclick', () => this.toggleMinimize());
    }

    initializeDragFunctionality() {
        let dragTarget = null;
        let startX = 0;
        let startY = 0;
        let startLeft = 0;
        let startTop = 0;

        const startDrag = (element, handle, e) => {
            dragTarget = element;
            startX = e.clientX;
            startY = e.clientY;
            const rect = element.getBoundingClientRect();
            startLeft = rect.left;
            startTop = rect.top;
            element.style.cursor = 'grabbing';
            e.preventDefault();
        };

        // Main card dragging
        this.elements.header.addEventListener('mousedown', (e) => {
            startDrag(this.elements.card, this.elements.header, e);
        });

        // Minimized bubble dragging
        this.elements.minimizedBubble.addEventListener('mousedown', (e) => {
            startDrag(this.elements.card, this.elements.minimizedBubble, e);
        });

        document.addEventListener('mousemove', (e) => {
            if (dragTarget) {
                const deltaX = e.clientX - startX;
                const deltaY = e.clientY - startY;
                const newLeft = startLeft + deltaX;
                const newTop = startTop + deltaY;

                // Keep within screen bounds
                const maxLeft = window.innerWidth - dragTarget.offsetWidth;
                const maxTop = window.innerHeight - dragTarget.offsetHeight;

                dragTarget.style.left = Math.max(0, Math.min(newLeft, maxLeft)) + 'px';
                dragTarget.style.top = Math.max(0, Math.min(newTop, maxTop)) + 'px';
                dragTarget.style.right = 'auto';
                dragTarget.style.bottom = 'auto';
            }
        });

        document.addEventListener('mouseup', () => {
            if (dragTarget) {
                dragTarget.style.cursor = this.isMinimized ? 'grab' : 'move';
                dragTarget = null;
            }
        });
    }

    toggleMinimize() {
        this.isMinimized = !this.isMinimized;
        this.elements.card.classList.toggle('minimized', this.isMinimized);
        
        if (this.isMinimized) {
            this.updateStatus('Minimized');
            this.elements.card.style.cursor = 'grab';
        } else {
            this.updateStatus('Echo Engine Ready');
            this.elements.card.style.cursor = 'move';
        }
    }

    async createEchoMirror(app) {
        const mirrorId = 'mirror_' + Date.now();
        const mirror = this.createEchoMirrorElement(mirrorId, app);
        
        // Position new mirror offset from main card
        const cardRect = this.elements.card.getBoundingClientRect();
        mirror.style.left = (cardRect.left + 80) + 'px';
        mirror.style.top = (cardRect.top + 80) + 'px';
        
        document.body.appendChild(mirror);
        this.echoMirrors.push({ id: mirrorId, element: mirror, app: app });
        
        // Start actual mirroring
        if (this.currentDevice === 'computer') {
            await this.startDesktopCapture(mirrorId, app);
        } else {
            await this.startRemoteMirroring(mirrorId, app);
        }
        
        this.addMessage(`🔄 Echo mirror created for ${app.name}!`, 'agent');
        this.speak(`Echo mirror created for ${app.name}`);
        
        return mirrorId;
    }

    createEchoMirrorElement(id, app) {
        const mirror = document.createElement('div');
        mirror.className = 'echo-mirror';
        mirror.id = id;
        
        mirror.innerHTML = `
            <div class="echo-header">
                <div class="echo-title">🔄 Echo: ${app.name}</div>
                <div class="header-controls">
                    <button class="control-btn close-btn" onclick="agentLee.closeEchoMirror('${id}')">×</button>
                </div>
            </div>
            <div class="echo-content" id="content_${id}">
                <div class="echo-placeholder">
                    🔄 Starting Echo mirror for ${app.name}...<br>
                    Capturing application window...
                </div>
            </div>
        `;

        // Make mirror draggable
        const header = mirror.querySelector('.echo-header');
        this.makeElementDraggable(mirror, header);

        return mirror;
    }

    async startDesktopCapture(mirrorId, app) {
        try {
            this.updateStatus('Starting desktop capture...');
            
            // First, open the application
            await this.openApplication(app.id);
            
            // Wait a moment for the app to open
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Request desktop capture permission
            if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
                const stream = await navigator.mediaDevices.getDisplayMedia({
                    video: {
                        mediaSource: 'window',
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    },
                    audio: false
                });

                // Create video element to display the captured stream
                const contentDiv = document.getElementById(`content_${mirrorId}`);
                contentDiv.innerHTML = `
                    <video class="echo-video" autoplay muted></video>
                    <div style="position: absolute; top: 5px; left: 5px; color: #10b981; font-size: 10px; background: rgba(0,0,0,0.7); padding: 2px 6px; border-radius: 3px;">
                        🟢 Live Mirror
                    </div>
                `;
                
                const video = contentDiv.querySelector('.echo-video');
                video.srcObject = stream;
                
                // Store stream reference
                const mirror = this.echoMirrors.find(m => m.id === mirrorId);
                if (mirror) {
                    mirror.stream = stream;
                }
                
                this.updateStatus('Echo mirror active');
                this.addMessage(`✅ Desktop capture started for ${app.name}!`, 'agent');
                
            } else {
                throw new Error('Desktop capture not supported');
            }
            
        } catch (error) {
            console.error('Desktop capture failed:', error);
            this.addMessage(`❌ Desktop capture failed for ${app.name}: ${error.message}`, 'agent');
            
            // Show fallback content
            const contentDiv = document.getElementById(`content_${mirrorId}`);
            contentDiv.innerHTML = `
                <div class="echo-placeholder">
                    ❌ Desktop capture failed<br>
                    ${error.message}<br><br>
                    <small>Note: Desktop capture requires user permission and HTTPS</small>
                </div>
            `;
        }
    }

    async startRemoteMirroring(mirrorId, app) {
        // Simulate remote mirroring for non-computer devices
        const contentDiv = document.getElementById(`content_${mirrorId}`);
        contentDiv.innerHTML = `
            <div class="echo-placeholder">
                🔄 Connecting to ${this.currentDevice}...<br>
                Establishing WebRTC connection...<br><br>
                <div style="color: #3b82f6;">Simulating ${app.name} mirror</div>
            </div>
        `;
        
        // Simulate connection process
        setTimeout(() => {
            contentDiv.innerHTML = `
                <div class="echo-placeholder">
                    📱 ${app.name} Mirror Active<br>
                    Device: ${this.currentDevice}<br><br>
                    <div style="color: #10b981;">🟢 Connected via WebRTC</div><br>
                    <small>This would show the live interface of ${app.name} running on your ${this.currentDevice}</small>
                </div>
            `;
        }, 2000);
        
        this.addMessage(`🔄 Remote mirror established for ${app.name} on ${this.currentDevice}`, 'agent');
    }

    makeElementDraggable(element, handle) {
        let isDragging = false;
        let startX = 0;
        let startY = 0;
        let startLeft = 0;
        let startTop = 0;

        handle.addEventListener('mousedown', (e) => {
            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;
            const rect = element.getBoundingClientRect();
            startLeft = rect.left;
            startTop = rect.top;
            handle.style.cursor = 'grabbing';
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                const deltaX = e.clientX - startX;
                const deltaY = e.clientY - startY;
                element.style.left = (startLeft + deltaX) + 'px';
                element.style.top = (startTop + deltaY) + 'px';
            }
        });

        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                handle.style.cursor = 'grab';
            }
        });
    }

    closeEchoMirror(mirrorId) {
        const mirrorIndex = this.echoMirrors.findIndex(mirror => mirror.id === mirrorId);
        if (mirrorIndex !== -1) {
            const mirror = this.echoMirrors[mirrorIndex];
            
            // Stop any active streams
            if (mirror.stream) {
                mirror.stream.getTracks().forEach(track => track.stop());
            }
            
            mirror.element.remove();
            this.echoMirrors.splice(mirrorIndex, 1);
            
            this.addMessage(`🔄 Echo mirror closed for ${mirror.app.name}`, 'agent');
        }
    }

    async sendMessage() {
        const message = this.elements.input.value.trim();
        if (!message) return;

        this.addMessage(message, 'user');
        this.elements.input.value = '';

        try {
            const response = await this.sendToBackend(message);
            this.addMessage(response.message, 'agent');
            
            if (response.speak) {
                this.speak(response.message);
            }

            if (response.action) {
                await this.handleAction(response.action);
            }
        } catch (error) {
            this.addMessage("I'm having trouble connecting. Please try again.", 'agent');
            console.error('Error:', error);
        }
    }

    async sendToBackend(message) {
        try {
            const response = await fetch(`${this.apiUrl}/api/chat`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ message, history: this.conversationHistory })
            });

            if (response.ok) {
                return await response.json();
            }
        } catch (error) {
            console.error('Backend error:', error);
        }

        // Fallback response
        return {
            message: this.generateResponse(message),
            speak: true,
            action: this.detectAction(message)
        };
    }

    generateResponse(message) {
        const lower = message.toLowerCase();
        
        if (lower.includes('hello') || lower.includes('hi')) {
            return "Hello! I'm Agent Lee with Echo Engine. I can mirror applications in real-time floating windows!";
        } else if (lower.includes('mirror') || lower.includes('echo')) {
            return "I can create Echo mirrors of applications! Click any app below to mirror it in a floating window.";
        } else if (lower.includes('open') || lower.includes('launch')) {
            return "I can open applications and mirror them! Click on any app in the Echo Engine below.";
        } else if (lower.includes('desktop') || lower.includes('capture')) {
            return "I use desktop capture to create real-time mirrors of applications. This requires screen sharing permission.";
        } else {
            return "I'm Agent Lee with Echo Engine! I can mirror applications across devices using desktop capture and WebRTC. What would you like me to mirror?";
        }
    }

    detectAction(message) {
        const lower = message.toLowerCase();
        
        if (lower.includes('mirror chrome') || lower.includes('echo chrome')) {
            return { type: 'mirror_app', app: 'chrome' };
        } else if (lower.includes('mirror notepad') || lower.includes('echo notepad')) {
            return { type: 'mirror_app', app: 'notepad' };
        } else if (lower.includes('open chrome')) {
            return { type: 'open_app', app: 'chrome' };
        }
        
        return null;
    }

    async handleAction(action) {
        switch (action.type) {
            case 'open_app':
                await this.openApplication(action.app);
                break;
            case 'mirror_app':
                const app = this.apps.computer.find(a => a.id === action.app);
                if (app) {
                    await this.createEchoMirror(app);
                }
                break;
        }
    }

    async openApplication(appName) {
        try {
            const response = await fetch(`${this.apiUrl}/api/open_app`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ app: appName })
            });

            const result = await response.json();
            
            if (result.status === 'success') {
                this.addMessage(`✅ ${appName} opened successfully!`, 'agent');
                this.speak(`${appName} is now open`);
            } else {
                this.addMessage(`❌ Could not open ${appName}: ${result.message}`, 'agent');
            }
        } catch (error) {
            this.addMessage(`❌ Error opening ${appName}: ${error.message}`, 'agent');
        }
    }

    toggleVoice() {
        if (this.isListening) {
            this.stopListening();
        } else {
            this.startListening();
        }
    }

    startListening() {
        this.isListening = true;
        this.elements.voiceBtn.classList.add('active');
        this.elements.listeningIndicator.classList.add('active');
        this.updateStatus('Listening...');

        if ('webkitSpeechRecognition' in window) {
            const recognition = new webkitSpeechRecognition();
            recognition.continuous = false;
            recognition.interimResults = false;
            recognition.lang = 'en-US';

            recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                this.elements.input.value = transcript;
                this.sendMessage();
            };

            recognition.onerror = () => {
                this.stopListening();
                this.addMessage("Sorry, I couldn't hear you clearly.", 'agent');
            };

            recognition.onend = () => {
                this.stopListening();
            };

            recognition.start();
        } else {
            this.stopListening();
            this.addMessage("Voice recognition not supported in this browser.", 'agent');
        }
    }

    stopListening() {
        this.isListening = false;
        this.elements.voiceBtn.classList.remove('active');
        this.elements.listeningIndicator.classList.remove('active');
        this.updateStatus('Echo Engine Ready');
    }

    async speak(text) {
        try {
            await fetch(`${this.apiUrl}/api/speak`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ text })
            });
        } catch (error) {
            // Fallback to browser speech
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(text);
                utterance.rate = 0.9;
                speechSynthesis.speak(utterance);
            }
        }
    }

    addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        messageDiv.textContent = text;
        
        this.elements.chat.appendChild(messageDiv);
        this.elements.chat.scrollTop = this.elements.chat.scrollHeight;

        this.conversationHistory.push({ sender, text, timestamp: Date.now() });
        
        if (this.conversationHistory.length > 20) {
            this.conversationHistory = this.conversationHistory.slice(-20);
        }
    }

    loadApplications() {
        this.updateAppGrid();
    }

    switchDevice(deviceType) {
        this.currentDevice = deviceType;
        
        document.querySelectorAll('.device-tab').forEach(tab => {
            tab.classList.toggle('active', tab.dataset.device === deviceType);
        });

        this.updateAppGrid();
    }

    updateAppGrid() {
        const apps = this.apps[this.currentDevice] || [];
        this.elements.appGrid.innerHTML = '';

        apps.forEach(app => {
            const appTile = document.createElement('div');
            appTile.className = 'app-tile';
            appTile.innerHTML = `
                <div class="app-icon">${app.icon}</div>
                <div class="app-name">${app.name}</div>
            `;
            
            appTile.addEventListener('click', () => this.handleAppClick(app));
            this.elements.appGrid.appendChild(appTile);
        });
    }

    async handleAppClick(app) {
        if (this.currentDevice === 'computer') {
            // Create Echo mirror for computer applications
            await this.createEchoMirror(app);
        } else {
            // Create remote Echo mirror for other devices
            await this.createEchoMirror(app);
        }
    }

    startStatusUpdates() {
        // Update time every second
        setInterval(() => {
            this.elements.timeStatus.textContent = new Date().toLocaleTimeString();
        }, 1000);
    }

    async greetUser() {
        await new Promise(resolve => setTimeout(resolve, 1000));
        this.speak("Hello! I'm Agent Lee with Echo Engine. I can mirror applications in real-time floating windows!");
        this.updateStatus("Echo Engine Ready");
    }

    updateStatus(status) {
        this.elements.status.textContent = status;
    }

    closeWidget() {
        if (confirm('Close Agent Lee Echo Engine?')) {
            // Stop all active streams
            this.echoMirrors.forEach(mirror => {
                if (mirror.stream) {
                    mirror.stream.getTracks().forEach(track => track.stop());
                }
            });
            this.elements.card.style.display = 'none';
        }
    }
}

// Initialize Agent Lee Echo Engine when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.agentLee = new AgentLeeRealEchoEngine();
    console.log('🔄 Agent Lee Real Echo Engine initialized!');
});
