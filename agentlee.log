2025-05-31 08:20:44,601 [INFO] agentlee.llm: Personality Engine initialized
2025-05-31 08:20:44,601 [INFO] agentlee.llm: Gemini API initialized successfully
2025-05-31 08:20:46,690 [INFO] agentlee.controller: LLM Service initialized: {'gemini_available': True, 'ollama_available': True, 'available_models': ['gemini-1.5-flash', 'ollama-llama2', 'fallback'], 'primary_model': 'gemini-1.5-flash', 'personality_status': {'current_scene': 'default', 'emotional_state': 'neutral', 'memory_size': 0, 'available_scenes': ['default', 'funny', 'serious', 'excited', 'chill'], 'humor_level': 0.7}}
2025-05-31 08:20:46,690 [INFO] agentlee.controller: System Controller initialized
2025-05-31 08:20:46,828 [INFO] agentlee.controller: Agent Lee� System Controller v2.0 starting up...
2025-05-31 08:20:46,911 [INFO] agentlee.controller: Database tables created/verified
2025-05-31 08:20:46,911 [INFO] agentlee.controller: Background health monitoring started
2025-05-31 08:20:46,911 [INFO] agentlee.controller: Configuration: {'debug': False, 'host': '127.0.0.1', 'port': 8000, 'allowed_origins': ['http://localhost', 'http://127.0.0.1'], 'database_type': 'sqlite', 'gemini_configured': True, 'ollama_url': 'http://localhost:11434', 'ollama_model': 'llama2', 'log_level': 'info', 'tts_engine': 'pyttsx3', 'chrome_available': False, 'firefox_available': False, 'platform': 'Windows'}
2025-05-31 08:21:09,718 [INFO] agentlee.controller: LLM request processed: model=gemini-1.5-flash, confidence=0.95, processing_time=1.40s
2025-05-31 08:21:09,797 [INFO] comtypes.client._code_cache: Imported existing <module 'comtypes.gen' from 'D:\\Agentleefreshidea\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-31 08:21:09,797 [INFO] comtypes.client._code_cache: Using writeable comtypes cache directory: 'D:\Agentleefreshidea\.venv\Lib\site-packages\comtypes\gen'
2025-05-31 08:22:45,570 [INFO] agentlee.controller: LLM request processed: model=gemini-1.5-flash, confidence=0.95, processing_time=2.08s
2025-05-31 08:28:47,107 [INFO] agentlee.llm: Personality Engine initialized
2025-05-31 08:28:47,107 [INFO] agentlee.llm: Gemini API initialized successfully
2025-05-31 08:28:49,238 [INFO] agentlee.controller: LLM Service initialized: {'gemini_available': True, 'ollama_available': True, 'available_models': ['gemini-1.5-flash', 'ollama-llama2', 'fallback'], 'primary_model': 'gemini-1.5-flash', 'personality_status': {'current_scene': 'default', 'emotional_state': 'neutral', 'memory_size': 0, 'available_scenes': ['default', 'funny', 'serious', 'excited', 'chill'], 'humor_level': 0.7}}
2025-05-31 08:28:49,238 [INFO] agentlee.controller: System Controller initialized
2025-05-31 08:28:49,343 [INFO] agentlee.controller: Agent Lee� System Controller v2.0 starting up...
2025-05-31 08:28:49,349 [INFO] agentlee.controller: Database tables created/verified
2025-05-31 08:28:49,349 [INFO] agentlee.controller: Background health monitoring started
2025-05-31 08:28:49,349 [INFO] agentlee.controller: Configuration: {'debug': False, 'host': '127.0.0.1', 'port': 8000, 'allowed_origins': ['http://localhost', 'http://127.0.0.1'], 'database_type': 'sqlite', 'gemini_configured': True, 'ollama_url': 'http://localhost:11434', 'ollama_model': 'llama2', 'log_level': 'info', 'tts_engine': 'pyttsx3', 'chrome_available': False, 'firefox_available': False, 'platform': 'Windows'}
2025-05-31 08:28:49,455 [INFO] agentlee.controller: Agent Lee� System Controller shutting down...
2025-05-31 08:29:23,897 [INFO] agentlee.llm: Personality Engine initialized
2025-05-31 08:29:23,898 [INFO] agentlee.llm: Gemini API initialized successfully
2025-05-31 08:29:25,938 [INFO] agentlee.controller: LLM Service initialized: {'gemini_available': True, 'ollama_available': True, 'available_models': ['gemini-1.5-flash', 'ollama-llama2', 'fallback'], 'primary_model': 'gemini-1.5-flash', 'personality_status': {'current_scene': 'default', 'emotional_state': 'neutral', 'memory_size': 0, 'available_scenes': ['default', 'funny', 'serious', 'excited', 'chill'], 'humor_level': 0.7}}
2025-05-31 08:29:25,939 [INFO] agentlee.controller: System Controller initialized
2025-05-31 08:29:25,999 [INFO] agentlee.controller: Agent Lee� System Controller v2.0 starting up...
2025-05-31 08:29:26,001 [INFO] agentlee.controller: Database tables created/verified
2025-05-31 08:29:26,001 [INFO] agentlee.controller: Background health monitoring started
2025-05-31 08:29:26,001 [INFO] agentlee.controller: Configuration: {'debug': False, 'host': '127.0.0.1', 'port': 8000, 'allowed_origins': ['http://localhost', 'http://127.0.0.1'], 'database_type': 'sqlite', 'gemini_configured': True, 'ollama_url': 'http://localhost:11434', 'ollama_model': 'llama2', 'log_level': 'info', 'tts_engine': 'pyttsx3', 'chrome_available': False, 'firefox_available': False, 'platform': 'Windows'}
2025-05-31 08:33:53,276 [INFO] agentlee.llm: Personality Engine initialized
2025-05-31 08:33:53,276 [INFO] agentlee.llm: Gemini API initialized successfully
2025-05-31 08:33:55,338 [INFO] agentlee.controller: LLM Service initialized: {'gemini_available': True, 'ollama_available': True, 'available_models': ['gemini-1.5-flash', 'ollama-llama2', 'fallback'], 'primary_model': 'gemini-1.5-flash', 'personality_status': {'current_scene': 'default', 'emotional_state': 'neutral', 'memory_size': 0, 'available_scenes': ['default', 'funny', 'serious', 'excited', 'chill'], 'humor_level': 0.7}}
2025-05-31 08:33:55,338 [INFO] agentlee.controller: System Controller initialized
2025-05-31 08:33:55,420 [INFO] agentlee.controller: Agent Lee� System Controller v2.0 starting up...
2025-05-31 08:33:55,422 [INFO] agentlee.controller: Database tables created/verified
2025-05-31 08:33:55,422 [INFO] agentlee.controller: Background health monitoring started
2025-05-31 08:33:55,422 [INFO] agentlee.controller: Configuration: {'debug': False, 'host': '127.0.0.1', 'port': 8000, 'allowed_origins': ['http://localhost', 'http://127.0.0.1'], 'database_type': 'sqlite', 'gemini_configured': True, 'ollama_url': 'http://localhost:11434', 'ollama_model': 'llama2', 'log_level': 'info', 'tts_engine': 'pyttsx3', 'chrome_available': False, 'firefox_available': False, 'platform': 'Windows'}
2025-05-31 08:33:55,529 [INFO] agentlee.controller: Agent Lee� System Controller shutting down...
2025-05-31 08:34:39,636 [INFO] agentlee.llm: Personality Engine initialized
2025-05-31 08:34:39,636 [INFO] agentlee.llm: Gemini API initialized successfully
2025-05-31 08:34:41,686 [INFO] agentlee.controller: LLM Service initialized: {'gemini_available': True, 'ollama_available': True, 'available_models': ['gemini-1.5-flash', 'ollama-llama2', 'fallback'], 'primary_model': 'gemini-1.5-flash', 'personality_status': {'current_scene': 'default', 'emotional_state': 'neutral', 'memory_size': 0, 'available_scenes': ['default', 'funny', 'serious', 'excited', 'chill'], 'humor_level': 0.7}}
2025-05-31 08:34:41,686 [INFO] agentlee.controller: System Controller initialized
2025-05-31 08:34:41,760 [INFO] agentlee.controller: Agent Lee� System Controller v2.0 starting up...
2025-05-31 08:34:41,763 [INFO] agentlee.controller: Database tables created/verified
2025-05-31 08:34:41,764 [INFO] agentlee.controller: Background health monitoring started
2025-05-31 08:34:41,764 [INFO] agentlee.controller: Configuration: {'debug': False, 'host': '127.0.0.1', 'port': 8000, 'allowed_origins': ['http://localhost', 'http://127.0.0.1'], 'database_type': 'sqlite', 'gemini_configured': True, 'ollama_url': 'http://localhost:11434', 'ollama_model': 'llama2', 'log_level': 'info', 'tts_engine': 'pyttsx3', 'chrome_available': False, 'firefox_available': False, 'platform': 'Windows'}
2025-05-31 08:39:07,558 [INFO] agentlee.llm: Personality Engine initialized
2025-05-31 08:39:07,558 [INFO] agentlee.llm: Gemini API initialized successfully
2025-05-31 08:39:09,610 [INFO] agentlee.controller: LLM Service initialized: {'gemini_available': True, 'ollama_available': True, 'available_models': ['gemini-1.5-flash', 'ollama-llama2', 'fallback'], 'primary_model': 'gemini-1.5-flash', 'personality_status': {'current_scene': 'default', 'emotional_state': 'neutral', 'memory_size': 0, 'available_scenes': ['default', 'funny', 'serious', 'excited', 'chill'], 'humor_level': 0.7}}
2025-05-31 08:39:09,610 [INFO] agentlee.controller: System Controller initialized
2025-05-31 08:39:09,703 [INFO] agentlee.controller: Agent Lee� System Controller v2.0 starting up...
2025-05-31 08:39:09,705 [INFO] agentlee.controller: Database tables created/verified
2025-05-31 08:39:09,706 [INFO] agentlee.controller: Background health monitoring started
2025-05-31 08:39:09,706 [INFO] agentlee.controller: Configuration: {'debug': False, 'host': '127.0.0.1', 'port': 8000, 'allowed_origins': ['http://localhost', 'http://127.0.0.1'], 'database_type': 'sqlite', 'gemini_configured': True, 'ollama_url': 'http://localhost:11434', 'ollama_model': 'llama2', 'log_level': 'info', 'tts_engine': 'pyttsx3', 'chrome_available': False, 'firefox_available': False, 'platform': 'Windows'}
2025-05-31 08:39:09,813 [INFO] agentlee.controller: Agent Lee� System Controller shutting down...
2025-05-31 08:39:44,888 [INFO] agentlee.llm: Personality Engine initialized
2025-05-31 08:39:44,888 [INFO] agentlee.llm: Gemini API initialized successfully
2025-05-31 08:39:46,933 [INFO] agentlee.controller: LLM Service initialized: {'gemini_available': True, 'ollama_available': True, 'available_models': ['gemini-1.5-flash', 'ollama-llama2', 'fallback'], 'primary_model': 'gemini-1.5-flash', 'personality_status': {'current_scene': 'default', 'emotional_state': 'neutral', 'memory_size': 0, 'available_scenes': ['default', 'funny', 'serious', 'excited', 'chill'], 'humor_level': 0.7}}
2025-05-31 08:39:46,934 [INFO] agentlee.controller: System Controller initialized
2025-05-31 08:39:46,995 [INFO] agentlee.controller: Agent Lee� System Controller v2.0 starting up...
2025-05-31 08:39:46,997 [INFO] agentlee.controller: Database tables created/verified
2025-05-31 08:39:46,997 [INFO] agentlee.controller: Background health monitoring started
2025-05-31 08:39:46,997 [INFO] agentlee.controller: Configuration: {'debug': False, 'host': '127.0.0.1', 'port': 8000, 'allowed_origins': ['http://localhost', 'http://127.0.0.1'], 'database_type': 'sqlite', 'gemini_configured': True, 'ollama_url': 'http://localhost:11434', 'ollama_model': 'llama2', 'log_level': 'info', 'tts_engine': 'pyttsx3', 'chrome_available': False, 'firefox_available': False, 'platform': 'Windows'}
2025-05-31 08:47:35,359 [INFO] agentlee.llm: Personality Engine initialized
2025-05-31 08:47:35,359 [INFO] agentlee.llm: Gemini API initialized successfully
2025-05-31 08:47:37,406 [INFO] agentlee.controller: LLM Service initialized: {'gemini_available': True, 'ollama_available': True, 'available_models': ['gemini-1.5-flash', 'ollama-llama2', 'fallback'], 'primary_model': 'gemini-1.5-flash', 'personality_status': {'current_scene': 'default', 'emotional_state': 'neutral', 'memory_size': 0, 'available_scenes': ['default', 'funny', 'serious', 'excited', 'chill'], 'humor_level': 0.7}}
2025-05-31 08:47:37,406 [INFO] agentlee.controller: System Controller initialized
2025-05-31 08:47:37,487 [INFO] agentlee.controller: Agent Lee� System Controller v2.0 starting up...
2025-05-31 08:47:37,489 [INFO] agentlee.controller: Database tables created/verified
2025-05-31 08:47:37,489 [INFO] agentlee.controller: Background health monitoring started
2025-05-31 08:47:37,489 [INFO] agentlee.controller: Configuration: {'debug': False, 'host': '127.0.0.1', 'port': 8000, 'allowed_origins': ['http://localhost', 'http://127.0.0.1'], 'database_type': 'sqlite', 'gemini_configured': True, 'ollama_url': 'http://localhost:11434', 'ollama_model': 'llama2', 'log_level': 'info', 'tts_engine': 'pyttsx3', 'chrome_available': False, 'firefox_available': False, 'platform': 'Windows'}
2025-05-31 08:47:37,597 [INFO] agentlee.controller: Agent Lee� System Controller shutting down...
2025-05-31 08:47:43,675 [INFO] comtypes.client._code_cache: Imported existing <module 'comtypes.gen' from 'D:\\Agentleefreshidea\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-31 08:47:43,789 [INFO] comtypes.client._code_cache: Using writeable comtypes cache directory: 'D:\Agentleefreshidea\.venv\Lib\site-packages\comtypes\gen'
2025-05-31 08:47:46,108 [ERROR] agentlee.controller: TTS error: run loop already started
2025-05-31 08:48:35,598 [INFO] agentlee.llm: Personality Engine initialized
2025-05-31 08:48:35,599 [INFO] agentlee.llm: Gemini API initialized successfully
2025-05-31 08:48:37,635 [INFO] agentlee.controller: LLM Service initialized: {'gemini_available': True, 'ollama_available': True, 'available_models': ['gemini-1.5-flash', 'ollama-llama2', 'fallback'], 'primary_model': 'gemini-1.5-flash', 'personality_status': {'current_scene': 'default', 'emotional_state': 'neutral', 'memory_size': 0, 'available_scenes': ['default', 'funny', 'serious', 'excited', 'chill'], 'humor_level': 0.7}}
2025-05-31 08:48:37,635 [INFO] agentlee.controller: System Controller initialized
2025-05-31 08:48:37,697 [INFO] agentlee.controller: Agent Lee� System Controller v2.0 starting up...
2025-05-31 08:48:37,699 [INFO] agentlee.controller: Database tables created/verified
2025-05-31 08:48:37,699 [INFO] agentlee.controller: Background health monitoring started
2025-05-31 08:48:37,699 [INFO] agentlee.controller: Configuration: {'debug': False, 'host': '127.0.0.1', 'port': 8000, 'allowed_origins': ['http://localhost', 'http://127.0.0.1'], 'database_type': 'sqlite', 'gemini_configured': True, 'ollama_url': 'http://localhost:11434', 'ollama_model': 'llama2', 'log_level': 'info', 'tts_engine': 'pyttsx3', 'chrome_available': False, 'firefox_available': False, 'platform': 'Windows'}
2025-05-31 08:48:44,141 [INFO] comtypes.client._code_cache: Imported existing <module 'comtypes.gen' from 'D:\\Agentleefreshidea\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-31 08:48:44,142 [INFO] comtypes.client._code_cache: Using writeable comtypes cache directory: 'D:\Agentleefreshidea\.venv\Lib\site-packages\comtypes\gen'
2025-05-31 08:48:46,602 [ERROR] agentlee.controller: TTS error: run loop already started
2025-05-31 11:23:20,605 [INFO] agentlee.llm: Personality Engine initialized
2025-05-31 11:23:20,606 [INFO] agentlee.llm: Gemini API initialized successfully
2025-05-31 11:23:22,680 [INFO] agentlee.controller: LLM Service initialized: {'gemini_available': True, 'ollama_available': True, 'available_models': ['gemini-1.5-flash', 'ollama-llama2', 'fallback'], 'primary_model': 'gemini-1.5-flash', 'personality_status': {'current_scene': 'default', 'emotional_state': 'neutral', 'memory_size': 0, 'available_scenes': ['default', 'funny', 'serious', 'excited', 'chill'], 'humor_level': 0.7}}
2025-05-31 11:23:22,681 [INFO] agentlee.controller: System Controller initialized
2025-05-31 11:23:22,813 [INFO] agentlee.controller: Agent Lee� System Controller v2.0 starting up...
2025-05-31 11:23:22,836 [INFO] agentlee.controller: Database tables created/verified
2025-05-31 11:23:22,836 [INFO] agentlee.controller: Background health monitoring started
2025-05-31 11:23:22,836 [INFO] agentlee.controller: Configuration: {'debug': False, 'host': '127.0.0.1', 'port': 8000, 'allowed_origins': ['http://localhost:3000', 'http://localhost:8000', 'http://127.0.0.1:8000'], 'database_type': 'sqlite', 'gemini_configured': True, 'ollama_url': 'http://localhost:11434', 'ollama_model': 'llama2', 'log_level': 'INFO', 'tts_engine': 'pyttsx3', 'chrome_available': False, 'firefox_available': False, 'platform': 'Windows'}
2025-05-31 11:24:57,914 [INFO] agentlee.controller: Agent Lee� System Controller shutting down...
2025-05-31 11:32:05,668 [INFO] agentlee.llm: Personality Engine initialized
2025-05-31 11:32:05,669 [INFO] agentlee.llm: Gemini API initialized successfully
2025-05-31 11:32:07,709 [INFO] agentlee.controller: LLM Service initialized: {'gemini_available': True, 'ollama_available': True, 'available_models': ['gemini-1.5-flash', 'ollama-llama2', 'fallback'], 'primary_model': 'gemini-1.5-flash', 'personality_status': {'current_scene': 'default', 'emotional_state': 'neutral', 'memory_size': 0, 'available_scenes': ['default', 'funny', 'serious', 'excited', 'chill'], 'humor_level': 0.7}}
2025-05-31 11:32:07,710 [INFO] agentlee.controller: System Controller initialized
2025-05-31 11:32:07,786 [INFO] agentlee.controller: Agent Lee� System Controller v2.0 starting up...
2025-05-31 11:32:07,787 [INFO] agentlee.controller: Database tables created/verified
2025-05-31 11:32:07,925 [INFO] agentlee.mods: Loading mod cluster: core_system
2025-05-31 11:32:07,930 [INFO] agentlee.mods.permissions: System Permissions Manager initialized
2025-05-31 11:32:07,956 [INFO] agentlee.mods: Loading mod cluster: app_control
2025-05-31 11:32:07,959 [INFO] agentlee.mods.app_controller: Enhanced App Controller initialized
2025-05-31 11:32:07,966 [INFO] agentlee.mods: Loading mod cluster: analytics
2025-05-31 11:32:07,977 [INFO] agentlee.mods: Loading mod cluster: memory_monitoring
2025-05-31 11:32:07,984 [INFO] agentlee.mods: Loading mod cluster: task_management
2025-05-31 11:32:07,991 [INFO] agentlee.mods: Loading mod cluster: ai_frontend
2025-05-31 11:32:07,993 [INFO] agentlee.mods: Loading mod cluster: system_integration
2025-05-31 11:32:07,994 [INFO] agentlee.mods: Loading mod cluster: ai_voice
2025-05-31 11:32:08,007 [INFO] agentlee.controller: Agent Lee mods loaded: {'core_system': True, 'app_control': True, 'analytics': True, 'memory_monitoring': True, 'task_management': True, 'ai_frontend': True, 'system_integration': True, 'ai_voice': True}
2025-05-31 11:32:08,007 [INFO] agentlee.controller: Background health monitoring started
2025-05-31 11:32:08,007 [INFO] agentlee.controller: Configuration: {'debug': False, 'host': '127.0.0.1', 'port': 8000, 'allowed_origins': ['http://localhost:3000', 'http://localhost:8000', 'http://127.0.0.1:8000'], 'database_type': 'sqlite', 'gemini_configured': True, 'ollama_url': 'http://localhost:11434', 'ollama_model': 'llama2', 'log_level': 'INFO', 'tts_engine': 'pyttsx3', 'chrome_available': False, 'firefox_available': False, 'platform': 'Windows'}
2025-05-31 12:05:37,214 [INFO] agentlee.controller: Agent Lee� System Controller shutting down...
2025-05-31 12:39:14,070 [WARNING] agentlee.controller: Configuration: GEMINI_API_KEY not configured - LLM features may be limited
2025-05-31 12:39:14,075 [INFO] agentlee.controller: LLM Service initialized: {'primary_provider': None, 'fallback_providers': ['openai', 'anthropic', 'ollama'], 'personality_mode': 'default', 'available_models': ['openai (fallback)', 'anthropic (fallback)', 'ollama (fallback)']}
2025-05-31 12:39:14,075 [INFO] agentlee.system: System Controller initialized
2025-05-31 12:39:14,075 [INFO] agentlee.controller: System Controller initialized
2025-05-31 12:39:14,202 [INFO] agentlee.controller: Agent Lee� System Controller v2.0 starting up...
2025-05-31 12:39:14,268 [INFO] agentlee.controller: Database tables created/verified
2025-05-31 12:39:14,278 [INFO] agentlee.mods: Loading mod cluster: core_system
2025-05-31 12:39:14,281 [INFO] agentlee.mods.permissions: System Permissions Manager initialized
2025-05-31 12:39:14,301 [INFO] agentlee.mods: Loading mod cluster: app_control
2025-05-31 12:39:14,304 [INFO] agentlee.mods.app_controller: Enhanced App Controller initialized
2025-05-31 12:39:14,306 [INFO] agentlee.mods: Loading mod cluster: analytics
2025-05-31 12:39:14,308 [INFO] agentlee.mods: Loading mod cluster: memory_monitoring
2025-05-31 12:39:14,310 [INFO] agentlee.mods: Loading mod cluster: task_management
2025-05-31 12:39:14,311 [INFO] agentlee.mods: Loading mod cluster: ai_frontend
2025-05-31 12:39:14,313 [INFO] agentlee.mods: Loading mod cluster: system_integration
2025-05-31 12:39:14,315 [INFO] agentlee.mods: Loading mod cluster: ai_voice
2025-05-31 12:39:14,318 [INFO] agentlee.controller: Agent Lee mods loaded: {'core_system': True, 'app_control': True, 'analytics': True, 'memory_monitoring': True, 'task_management': True, 'ai_frontend': True, 'system_integration': True, 'ai_voice': True}
2025-05-31 12:39:14,318 [INFO] agentlee.controller: Background health monitoring started
2025-05-31 12:39:14,319 [INFO] agentlee.controller: Configuration: {'host': '127.0.0.1', 'port': 8000, 'debug': False, 'database': 'sqlite:///./agentlee.db', 'gemini_configured': False, 'ollama_url': 'http://localhost:11434', 'log_level': 'info'}
2025-05-31 12:55:20,256 [INFO] agentlee.controller: LLM Service initialized: {'primary_provider': 'gemini', 'fallback_providers': ['openai', 'anthropic', 'ollama'], 'personality_mode': 'default', 'available_models': ['gemini (primary)', 'openai (fallback)', 'anthropic (fallback)', 'ollama (fallback)']}
2025-05-31 12:55:20,257 [INFO] agentlee.system: System Controller initialized
2025-05-31 12:55:20,257 [INFO] agentlee.controller: System Controller initialized
2025-05-31 12:55:20,352 [INFO] agentlee.controller: Agent Lee� System Controller v2.0 starting up...
2025-05-31 12:55:20,362 [INFO] agentlee.controller: Database tables created/verified
2025-05-31 12:55:20,374 [INFO] agentlee.mods: Loading mod cluster: core_system
2025-05-31 12:55:20,375 [INFO] agentlee.mods.permissions: System Permissions Manager initialized
2025-05-31 12:55:20,384 [INFO] agentlee.mods: Loading mod cluster: app_control
2025-05-31 12:55:20,385 [INFO] agentlee.mods.app_controller: Enhanced App Controller initialized
2025-05-31 12:55:20,386 [INFO] agentlee.mods: Loading mod cluster: analytics
2025-05-31 12:55:20,387 [INFO] agentlee.mods: Loading mod cluster: memory_monitoring
2025-05-31 12:55:20,389 [INFO] agentlee.mods: Loading mod cluster: task_management
2025-05-31 12:55:20,390 [INFO] agentlee.mods: Loading mod cluster: ai_frontend
2025-05-31 12:55:20,392 [INFO] agentlee.mods: Loading mod cluster: system_integration
2025-05-31 12:55:20,393 [INFO] agentlee.mods: Loading mod cluster: ai_voice
2025-05-31 12:55:20,396 [INFO] agentlee.controller: Agent Lee mods loaded: {'core_system': True, 'app_control': True, 'analytics': True, 'memory_monitoring': True, 'task_management': True, 'ai_frontend': True, 'system_integration': True, 'ai_voice': True}
2025-05-31 12:55:20,396 [INFO] agentlee.controller: Background health monitoring started
2025-05-31 12:55:20,396 [INFO] agentlee.controller: Configuration: {'host': '127.0.0.1', 'port': 8000, 'debug': False, 'database': 'sqlite:///./agentlee.db', 'gemini_configured': True, 'ollama_url': 'http://localhost:11434', 'log_level': 'INFO'}
2025-05-31 13:15:12,397 [INFO] agentlee.controller: LLM Service initialized: {'primary_provider': 'gemini', 'fallback_providers': ['openai', 'anthropic', 'ollama'], 'personality_mode': 'default', 'available_models': ['gemini (primary)', 'openai (fallback)', 'anthropic (fallback)', 'ollama (fallback)']}
2025-05-31 13:15:12,397 [INFO] agentlee.system: System Controller initialized
2025-05-31 13:15:12,397 [INFO] agentlee.controller: System Controller initialized
2025-05-31 13:15:12,458 [INFO] agentlee.controller: Agent Lee� System Controller v2.0 starting up...
2025-05-31 13:15:12,460 [INFO] agentlee.controller: Database tables created/verified
2025-05-31 13:15:12,464 [INFO] agentlee.mods: Loading mod cluster: core_system
2025-05-31 13:15:12,465 [INFO] agentlee.mods.permissions: System Permissions Manager initialized
2025-05-31 13:15:12,475 [INFO] agentlee.mods: Loading mod cluster: app_control
2025-05-31 13:15:12,475 [INFO] agentlee.mods.app_controller: Enhanced App Controller initialized
2025-05-31 13:15:12,477 [INFO] agentlee.mods: Loading mod cluster: analytics
2025-05-31 13:15:12,478 [INFO] agentlee.mods: Loading mod cluster: memory_monitoring
2025-05-31 13:15:12,479 [INFO] agentlee.mods: Loading mod cluster: task_management
2025-05-31 13:15:12,481 [INFO] agentlee.mods: Loading mod cluster: ai_frontend
2025-05-31 13:15:12,483 [INFO] agentlee.mods: Loading mod cluster: system_integration
2025-05-31 13:15:12,484 [INFO] agentlee.mods: Loading mod cluster: ai_voice
2025-05-31 13:15:12,487 [INFO] agentlee.controller: Agent Lee mods loaded: {'core_system': True, 'app_control': True, 'analytics': True, 'memory_monitoring': True, 'task_management': True, 'ai_frontend': True, 'system_integration': True, 'ai_voice': True}
2025-05-31 13:15:12,487 [INFO] agentlee.controller: Background health monitoring started
2025-05-31 13:15:12,487 [INFO] agentlee.controller: Configuration: {'host': '127.0.0.1', 'port': 8000, 'debug': False, 'database': 'sqlite:///./agentlee.db', 'gemini_configured': True, 'ollama_url': 'http://localhost:11434', 'log_level': 'info'}
2025-05-31 13:15:12,599 [INFO] agentlee.controller: Agent Lee� System Controller shutting down...
2025-05-31 13:20:30,752 [INFO] agentlee.llm: Gemini API initialized successfully
2025-05-31 13:20:30,752 [INFO] agentlee.llm: OpenAI available as fallback
2025-05-31 13:20:30,753 [INFO] agentlee.llm: Anthropic available as fallback
2025-05-31 13:20:30,753 [INFO] agentlee.llm: Ollama available as fallback
2025-05-31 13:20:30,753 [INFO] agentlee.controller: LLM Service initialized: {'primary_provider': 'gemini', 'fallback_providers': ['openai', 'anthropic', 'ollama'], 'personality_mode': 'default', 'available_models': ['gemini (primary)', 'openai (fallback)', 'anthropic (fallback)', 'ollama (fallback)']}
2025-05-31 13:20:30,753 [INFO] agentlee.system: System Controller initialized
2025-05-31 13:20:30,753 [INFO] agentlee.controller: System Controller initialized
2025-05-31 13:20:30,814 [INFO] agentlee.controller: Agent Lee� System Controller v2.0 starting up...
2025-05-31 13:20:30,816 [INFO] agentlee.controller: Database tables created/verified
2025-05-31 13:20:30,816 [INFO] agentlee.mods: Loading Agent Lee mod clusters...
2025-05-31 13:20:30,816 [INFO] agentlee.mods: Loading mod cluster: core_system
2025-05-31 13:20:30,817 [INFO] agentlee.mods.permissions: System Permissions Manager initialized
2025-05-31 13:20:30,827 [INFO] agentlee.mods: Cluster 'core_system' loaded successfully
2025-05-31 13:20:30,827 [INFO] agentlee.mods: Loading mod cluster: app_control
2025-05-31 13:20:30,828 [INFO] agentlee.mods.app_controller: Enhanced App Controller initialized
2025-05-31 13:20:30,828 [INFO] agentlee.mods: Cluster 'app_control' loaded successfully
2025-05-31 13:20:30,828 [INFO] agentlee.mods: Loading mod cluster: analytics
2025-05-31 13:20:30,829 [INFO] agentlee.mods: Cluster 'analytics' loaded successfully
2025-05-31 13:20:30,829 [INFO] agentlee.mods: Loading mod cluster: memory_monitoring
2025-05-31 13:20:30,829 [INFO] agentlee.mods: Cluster 'memory_monitoring' loaded successfully
2025-05-31 13:20:30,829 [INFO] agentlee.mods: Loading mod cluster: task_management
2025-05-31 13:20:30,830 [INFO] agentlee.mods: Cluster 'task_management' loaded successfully
2025-05-31 13:20:30,830 [INFO] agentlee.mods: Loading mod cluster: ai_frontend
2025-05-31 13:20:30,830 [INFO] agentlee.mods: Cluster 'ai_frontend' loaded successfully
2025-05-31 13:20:30,830 [INFO] agentlee.mods: Loading mod cluster: system_integration
2025-05-31 13:20:30,830 [INFO] agentlee.mods: Cluster 'system_integration' loaded successfully
2025-05-31 13:20:30,830 [INFO] agentlee.mods: Loading mod cluster: ai_voice
2025-05-31 13:20:30,831 [INFO] agentlee.mods: Cluster 'ai_voice' loaded successfully
2025-05-31 13:20:30,831 [INFO] agentlee.mods: Loaded 8/8 mod clusters successfully
2025-05-31 13:20:30,831 [INFO] agentlee.controller: Agent Lee mods loaded: {'core_system': True, 'app_control': True, 'analytics': True, 'memory_monitoring': True, 'task_management': True, 'ai_frontend': True, 'system_integration': True, 'ai_voice': True}
2025-05-31 13:20:30,831 [INFO] agentlee.controller: Background health monitoring started
2025-05-31 13:20:30,831 [INFO] agentlee.controller: Configuration: {'host': '127.0.0.1', 'port': 8000, 'debug': False, 'database': 'sqlite:///./agentlee.db', 'gemini_configured': True, 'ollama_url': 'http://localhost:11434', 'log_level': 'INFO'}
2025-05-31 13:21:59,385 [ERROR] asyncio: Exception in callback _ProactorBasePipeTransport._call_connection_lost()
handle: <Handle _ProactorBasePipeTransport._call_connection_lost()>
Traceback (most recent call last):
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python313\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-05-31 13:23:14,086 [INFO] agentlee.llm: Gemini API initialized successfully
2025-05-31 13:23:14,086 [INFO] agentlee.llm: OpenAI available as fallback
2025-05-31 13:23:14,086 [INFO] agentlee.llm: Anthropic available as fallback
2025-05-31 13:23:14,086 [INFO] agentlee.llm: Ollama available as fallback
2025-05-31 13:23:14,086 [INFO] agentlee.controller: LLM Service initialized: {'primary_provider': 'gemini', 'fallback_providers': ['openai', 'anthropic', 'ollama'], 'personality_mode': 'default', 'available_models': ['gemini (primary)', 'openai (fallback)', 'anthropic (fallback)', 'ollama (fallback)']}
2025-05-31 13:23:14,087 [INFO] agentlee.system: System Controller initialized
2025-05-31 13:23:14,087 [INFO] agentlee.controller: System Controller initialized
2025-05-31 13:23:14,164 [INFO] agentlee.controller: Agent Lee� System Controller v2.0 starting up...
2025-05-31 13:23:14,166 [INFO] agentlee.controller: Database tables created/verified
2025-05-31 13:23:14,166 [INFO] agentlee.mods: Loading Agent Lee mod clusters...
2025-05-31 13:23:14,166 [INFO] agentlee.mods: Loading mod cluster: core_system
2025-05-31 13:23:14,167 [INFO] agentlee.mods.permissions: System Permissions Manager initialized
2025-05-31 13:23:14,179 [INFO] agentlee.mods: Cluster 'core_system' loaded successfully
2025-05-31 13:23:14,179 [INFO] agentlee.mods: Loading mod cluster: app_control
2025-05-31 13:23:14,180 [INFO] agentlee.mods.app_controller: Enhanced App Controller initialized
2025-05-31 13:23:14,180 [INFO] agentlee.mods: Cluster 'app_control' loaded successfully
2025-05-31 13:23:14,180 [INFO] agentlee.mods: Loading mod cluster: analytics
2025-05-31 13:23:14,180 [INFO] agentlee.mods: Cluster 'analytics' loaded successfully
2025-05-31 13:23:14,181 [INFO] agentlee.mods: Loading mod cluster: memory_monitoring
2025-05-31 13:23:14,181 [INFO] agentlee.mods: Cluster 'memory_monitoring' loaded successfully
2025-05-31 13:23:14,181 [INFO] agentlee.mods: Loading mod cluster: task_management
2025-05-31 13:23:14,181 [INFO] agentlee.mods: Cluster 'task_management' loaded successfully
2025-05-31 13:23:14,181 [INFO] agentlee.mods: Loading mod cluster: ai_frontend
2025-05-31 13:23:14,181 [INFO] agentlee.mods: Cluster 'ai_frontend' loaded successfully
2025-05-31 13:23:14,182 [INFO] agentlee.mods: Loading mod cluster: system_integration
2025-05-31 13:23:14,182 [INFO] agentlee.mods: Cluster 'system_integration' loaded successfully
2025-05-31 13:23:14,182 [INFO] agentlee.mods: Loading mod cluster: ai_voice
2025-05-31 13:23:14,182 [INFO] agentlee.mods: Cluster 'ai_voice' loaded successfully
2025-05-31 13:23:14,182 [INFO] agentlee.mods: Loaded 8/8 mod clusters successfully
2025-05-31 13:23:14,182 [INFO] agentlee.controller: Agent Lee mods loaded: {'core_system': True, 'app_control': True, 'analytics': True, 'memory_monitoring': True, 'task_management': True, 'ai_frontend': True, 'system_integration': True, 'ai_voice': True}
2025-05-31 13:23:14,182 [INFO] agentlee.controller: Background health monitoring started
2025-05-31 13:23:14,183 [INFO] agentlee.controller: Configuration: {'host': '127.0.0.1', 'port': 8000, 'debug': False, 'database': 'sqlite:///./agentlee.db', 'gemini_configured': True, 'ollama_url': 'http://localhost:11434', 'log_level': 'INFO'}
2025-05-31 13:23:34,302 [INFO] comtypes.client._code_cache: Imported existing <module 'comtypes.gen' from 'D:\\Agentleefreshidea\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-31 13:23:34,303 [INFO] comtypes.client._code_cache: Using writeable comtypes cache directory: 'D:\Agentleefreshidea\.venv\Lib\site-packages\comtypes\gen'
2025-05-31 13:23:37,204 [ERROR] agentlee.controller: TTS error: run loop already started
2025-05-31 13:24:06,446 [ERROR] agentlee.controller: TTS error: run loop already started
2025-05-31 15:00:34,390 [INFO] agentlee.llm: Gemini API initialized successfully
2025-05-31 15:00:34,391 [INFO] agentlee.llm: OpenAI available as fallback
2025-05-31 15:00:34,392 [INFO] agentlee.llm: Anthropic available as fallback
2025-05-31 15:00:34,392 [INFO] agentlee.llm: Ollama available as fallback
2025-05-31 15:00:34,392 [INFO] agentlee.controller: LLM Service initialized: {'primary_provider': 'gemini', 'fallback_providers': ['openai', 'anthropic', 'ollama'], 'personality_mode': 'default', 'available_models': ['gemini (primary)', 'openai (fallback)', 'anthropic (fallback)', 'ollama (fallback)']}
2025-05-31 15:00:34,392 [INFO] agentlee.system: System Controller initialized
2025-05-31 15:00:34,392 [INFO] agentlee.controller: System Controller initialized
2025-05-31 15:00:34,472 [INFO] agentlee.controller: Agent Lee� System Controller v2.0 starting up...
2025-05-31 15:00:34,485 [INFO] agentlee.controller: Database tables created/verified
2025-05-31 15:00:34,485 [INFO] agentlee.mods: Loading Agent Lee mod clusters...
2025-05-31 15:00:34,486 [INFO] agentlee.mods: Loading mod cluster: core_system
2025-05-31 15:00:34,487 [INFO] agentlee.mods.permissions: System Permissions Manager initialized
2025-05-31 15:00:34,507 [INFO] agentlee.mods: Cluster 'core_system' loaded successfully
2025-05-31 15:00:34,508 [INFO] agentlee.mods: Loading mod cluster: app_control
2025-05-31 15:00:34,508 [INFO] agentlee.mods.app_controller: Enhanced App Controller initialized
2025-05-31 15:00:34,509 [INFO] agentlee.mods: Cluster 'app_control' loaded successfully
2025-05-31 15:00:34,509 [INFO] agentlee.mods: Loading mod cluster: analytics
2025-05-31 15:00:34,509 [INFO] agentlee.mods: Cluster 'analytics' loaded successfully
2025-05-31 15:00:34,509 [INFO] agentlee.mods: Loading mod cluster: memory_monitoring
2025-05-31 15:00:34,509 [INFO] agentlee.mods: Cluster 'memory_monitoring' loaded successfully
2025-05-31 15:00:34,509 [INFO] agentlee.mods: Loading mod cluster: task_management
2025-05-31 15:00:34,510 [INFO] agentlee.mods: Cluster 'task_management' loaded successfully
2025-05-31 15:00:34,510 [INFO] agentlee.mods: Loading mod cluster: ai_frontend
2025-05-31 15:00:34,510 [INFO] agentlee.mods: Cluster 'ai_frontend' loaded successfully
2025-05-31 15:00:34,510 [INFO] agentlee.mods: Loading mod cluster: system_integration
2025-05-31 15:00:34,510 [INFO] agentlee.mods: Cluster 'system_integration' loaded successfully
2025-05-31 15:00:34,510 [INFO] agentlee.mods: Loading mod cluster: ai_voice
2025-05-31 15:00:34,511 [INFO] agentlee.mods: Cluster 'ai_voice' loaded successfully
2025-05-31 15:00:34,511 [INFO] agentlee.mods: Loaded 8/8 mod clusters successfully
2025-05-31 15:00:34,511 [INFO] agentlee.controller: Agent Lee mods loaded: {'core_system': True, 'app_control': True, 'analytics': True, 'memory_monitoring': True, 'task_management': True, 'ai_frontend': True, 'system_integration': True, 'ai_voice': True}
2025-05-31 15:00:34,511 [INFO] agentlee.controller: Background health monitoring started
2025-05-31 15:00:34,511 [INFO] agentlee.controller: Configuration: {'host': '127.0.0.1', 'port': 8000, 'debug': False, 'database': 'sqlite:///./agentlee.db', 'gemini_configured': True, 'ollama_url': 'http://localhost:11434', 'log_level': 'INFO'}
2025-05-31 15:04:02,777 [INFO] agentlee.controller: Agent Lee� System Controller shutting down...
2025-05-31 15:04:10,692 [INFO] agentlee.llm: Gemini API initialized successfully
2025-05-31 15:04:10,692 [INFO] agentlee.llm: OpenAI available as fallback
2025-05-31 15:04:10,692 [INFO] agentlee.llm: Anthropic available as fallback
2025-05-31 15:04:10,692 [INFO] agentlee.llm: Ollama available as fallback
2025-05-31 15:04:10,693 [INFO] agentlee.controller: LLM Service initialized: {'primary_provider': 'gemini', 'fallback_providers': ['openai', 'anthropic', 'ollama'], 'personality_mode': 'default', 'available_models': ['gemini (primary)', 'openai (fallback)', 'anthropic (fallback)', 'ollama (fallback)']}
2025-05-31 15:04:10,693 [INFO] agentlee.system: System Controller initialized
2025-05-31 15:04:10,693 [INFO] agentlee.controller: System Controller initialized
2025-05-31 15:04:10,774 [INFO] agentlee.controller: Agent Lee� System Controller v2.0 starting up...
2025-05-31 15:04:10,777 [INFO] agentlee.controller: Database tables created/verified
2025-05-31 15:04:10,777 [INFO] agentlee.mods: Loading Agent Lee mod clusters...
2025-05-31 15:04:10,777 [INFO] agentlee.mods: Loading mod cluster: core_system
2025-05-31 15:04:10,781 [INFO] agentlee.mods.permissions: System Permissions Manager initialized
2025-05-31 15:04:10,781 [INFO] agentlee.mods.permissions: Permission 'file_access' already granted
2025-05-31 15:04:10,781 [INFO] agentlee.mods.permissions: Permission 'app_control' already granted
2025-05-31 15:04:10,781 [INFO] agentlee.mods.permissions: Permission 'system_info' already granted
2025-05-31 15:04:10,781 [INFO] agentlee.mods.permissions: Permission 'web_access' already granted
2025-05-31 15:04:10,782 [INFO] agentlee.mods: Cluster 'core_system' loaded successfully
2025-05-31 15:04:10,782 [INFO] agentlee.mods: Loading mod cluster: app_control
2025-05-31 15:04:10,784 [INFO] agentlee.mods.app_controller: Enhanced App Controller initialized
2025-05-31 15:04:10,784 [INFO] agentlee.mods: Cluster 'app_control' loaded successfully
2025-05-31 15:04:10,785 [INFO] agentlee.mods: Loading mod cluster: analytics
2025-05-31 15:04:10,785 [INFO] agentlee.mods: Cluster 'analytics' loaded successfully
2025-05-31 15:04:10,785 [INFO] agentlee.mods: Loading mod cluster: memory_monitoring
2025-05-31 15:04:10,785 [INFO] agentlee.mods: Cluster 'memory_monitoring' loaded successfully
2025-05-31 15:04:10,786 [INFO] agentlee.mods: Loading mod cluster: task_management
2025-05-31 15:04:10,786 [INFO] agentlee.mods: Cluster 'task_management' loaded successfully
2025-05-31 15:04:10,786 [INFO] agentlee.mods: Loading mod cluster: ai_frontend
2025-05-31 15:04:10,786 [INFO] agentlee.mods: Cluster 'ai_frontend' loaded successfully
2025-05-31 15:04:10,786 [INFO] agentlee.mods: Loading mod cluster: system_integration
2025-05-31 15:04:10,786 [INFO] agentlee.mods: Cluster 'system_integration' loaded successfully
2025-05-31 15:04:10,787 [INFO] agentlee.mods: Loading mod cluster: ai_voice
2025-05-31 15:04:10,787 [INFO] agentlee.mods: Cluster 'ai_voice' loaded successfully
2025-05-31 15:04:10,787 [INFO] agentlee.mods: Loaded 8/8 mod clusters successfully
2025-05-31 15:04:10,787 [INFO] agentlee.controller: Agent Lee mods loaded: {'core_system': True, 'app_control': True, 'analytics': True, 'memory_monitoring': True, 'task_management': True, 'ai_frontend': True, 'system_integration': True, 'ai_voice': True}
2025-05-31 15:04:10,787 [INFO] agentlee.controller: Background health monitoring started
2025-05-31 15:04:10,787 [INFO] agentlee.controller: Configuration: {'host': '127.0.0.1', 'port': 8000, 'debug': False, 'database': 'sqlite:///./agentlee.db', 'gemini_configured': True, 'ollama_url': 'http://localhost:11434', 'log_level': 'INFO'}
2025-05-31 15:04:44,135 [INFO] agentlee.llm: Gemini API initialized successfully
2025-05-31 15:04:44,135 [INFO] agentlee.llm: OpenAI available as fallback
2025-05-31 15:04:44,135 [INFO] agentlee.llm: Anthropic available as fallback
2025-05-31 15:04:44,136 [INFO] agentlee.llm: Ollama available as fallback
2025-05-31 15:04:44,136 [INFO] agentlee.controller: LLM Service initialized: {'primary_provider': 'gemini', 'fallback_providers': ['openai', 'anthropic', 'ollama'], 'personality_mode': 'default', 'available_models': ['gemini (primary)', 'openai (fallback)', 'anthropic (fallback)', 'ollama (fallback)']}
2025-05-31 15:04:44,136 [INFO] agentlee.system: System Controller initialized
2025-05-31 15:04:44,136 [INFO] agentlee.controller: System Controller initialized
2025-05-31 15:04:44,197 [INFO] agentlee.controller: Agent Lee� System Controller v2.0 starting up...
2025-05-31 15:04:44,198 [INFO] agentlee.controller: Database tables created/verified
2025-05-31 15:04:44,198 [INFO] agentlee.mods: Loading Agent Lee mod clusters...
2025-05-31 15:04:44,199 [INFO] agentlee.mods: Loading mod cluster: core_system
2025-05-31 15:04:44,199 [INFO] agentlee.mods.permissions: System Permissions Manager initialized
2025-05-31 15:04:44,199 [INFO] agentlee.mods.permissions: Permission 'file_access' already granted
2025-05-31 15:04:44,199 [INFO] agentlee.mods.permissions: Permission 'app_control' already granted
2025-05-31 15:04:44,199 [INFO] agentlee.mods.permissions: Permission 'system_info' already granted
2025-05-31 15:04:44,199 [INFO] agentlee.mods.permissions: Permission 'web_access' already granted
2025-05-31 15:04:44,200 [INFO] agentlee.mods: Cluster 'core_system' loaded successfully
2025-05-31 15:04:44,200 [INFO] agentlee.mods: Loading mod cluster: app_control
2025-05-31 15:04:44,200 [INFO] agentlee.mods.app_controller: Enhanced App Controller initialized
2025-05-31 15:04:44,200 [INFO] agentlee.mods: Cluster 'app_control' loaded successfully
2025-05-31 15:04:44,201 [INFO] agentlee.mods: Loading mod cluster: analytics
2025-05-31 15:04:44,201 [INFO] agentlee.mods: Cluster 'analytics' loaded successfully
2025-05-31 15:04:44,201 [INFO] agentlee.mods: Loading mod cluster: memory_monitoring
2025-05-31 15:04:44,201 [INFO] agentlee.mods: Cluster 'memory_monitoring' loaded successfully
2025-05-31 15:04:44,201 [INFO] agentlee.mods: Loading mod cluster: task_management
2025-05-31 15:04:44,201 [INFO] agentlee.mods: Cluster 'task_management' loaded successfully
2025-05-31 15:04:44,201 [INFO] agentlee.mods: Loading mod cluster: ai_frontend
2025-05-31 15:04:44,202 [INFO] agentlee.mods: Cluster 'ai_frontend' loaded successfully
2025-05-31 15:04:44,202 [INFO] agentlee.mods: Loading mod cluster: system_integration
2025-05-31 15:04:44,202 [INFO] agentlee.mods: Cluster 'system_integration' loaded successfully
2025-05-31 15:04:44,202 [INFO] agentlee.mods: Loading mod cluster: ai_voice
2025-05-31 15:04:44,202 [INFO] agentlee.mods: Cluster 'ai_voice' loaded successfully
2025-05-31 15:04:44,203 [INFO] agentlee.mods: Loaded 8/8 mod clusters successfully
2025-05-31 15:04:44,203 [INFO] agentlee.controller: Agent Lee mods loaded: {'core_system': True, 'app_control': True, 'analytics': True, 'memory_monitoring': True, 'task_management': True, 'ai_frontend': True, 'system_integration': True, 'ai_voice': True}
2025-05-31 15:04:44,203 [INFO] agentlee.controller: Background health monitoring started
2025-05-31 15:04:44,203 [INFO] agentlee.controller: Configuration: {'host': '127.0.0.1', 'port': 8000, 'debug': False, 'database': 'sqlite:///./agentlee.db', 'gemini_configured': True, 'ollama_url': 'http://localhost:11434', 'log_level': 'INFO'}
2025-05-31 15:04:50,404 [ERROR] agentlee.controller: Get personality status error: 'State' object has no attribute 'llm_service'
2025-05-31 15:04:57,210 [INFO] comtypes.client._code_cache: Imported existing <module 'comtypes.gen' from 'D:\\Agentleefreshidea\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-31 15:04:57,210 [INFO] comtypes.client._code_cache: Using writeable comtypes cache directory: 'D:\Agentleefreshidea\.venv\Lib\site-packages\comtypes\gen'
2025-05-31 15:04:59,651 [ERROR] agentlee.controller: TTS error: run loop already started
2025-05-31 15:05:17,988 [ERROR] agentlee.controller: LLM processing error: LLMService.generate_response() got an unexpected keyword argument 'prompt'
2025-05-31 15:07:27,686 [ERROR] agentlee.controller: Open messaging error: 'SystemController' object has no attribute 'open_messaging_app'
2025-05-31 15:35:37,779 [INFO] agentlee.llm: Gemini API initialized successfully
2025-05-31 15:35:37,779 [INFO] agentlee.llm: OpenAI available as fallback
2025-05-31 15:35:37,779 [INFO] agentlee.llm: Anthropic available as fallback
2025-05-31 15:35:37,779 [INFO] agentlee.llm: Ollama available as fallback
2025-05-31 15:35:37,779 [INFO] agentlee.controller: LLM Service initialized: {'primary_provider': 'gemini', 'fallback_providers': ['openai', 'anthropic', 'ollama'], 'personality_mode': 'default', 'available_models': ['gemini (primary)', 'openai (fallback)', 'anthropic (fallback)', 'ollama (fallback)']}
2025-05-31 15:35:37,780 [INFO] agentlee.system: System Controller initialized
2025-05-31 15:35:37,780 [INFO] agentlee.controller: System Controller initialized
2025-05-31 15:35:37,849 [INFO] agentlee.controller: Agent Lee� System Controller v2.0 starting up...
2025-05-31 15:35:37,853 [INFO] agentlee.controller: Database tables created/verified
2025-05-31 15:35:37,853 [INFO] agentlee.mods: Loading Agent Lee mod clusters...
2025-05-31 15:35:37,853 [INFO] agentlee.mods: Loading mod cluster: core_system
2025-05-31 15:35:37,854 [INFO] agentlee.mods.permissions: System Permissions Manager initialized
2025-05-31 15:35:37,854 [INFO] agentlee.mods.permissions: Permission 'file_access' already granted
2025-05-31 15:35:37,854 [INFO] agentlee.mods.permissions: Permission 'app_control' already granted
2025-05-31 15:35:37,855 [INFO] agentlee.mods.permissions: Permission 'system_info' already granted
2025-05-31 15:35:37,855 [INFO] agentlee.mods.permissions: Permission 'web_access' already granted
2025-05-31 15:35:37,855 [INFO] agentlee.mods: Cluster 'core_system' loaded successfully
2025-05-31 15:35:37,855 [INFO] agentlee.mods: Loading mod cluster: app_control
2025-05-31 15:35:37,856 [INFO] agentlee.mods.app_controller: Enhanced App Controller initialized
2025-05-31 15:35:37,856 [INFO] agentlee.mods: Cluster 'app_control' loaded successfully
2025-05-31 15:35:37,856 [INFO] agentlee.mods: Loading mod cluster: analytics
2025-05-31 15:35:37,856 [INFO] agentlee.mods: Cluster 'analytics' loaded successfully
2025-05-31 15:35:37,856 [INFO] agentlee.mods: Loading mod cluster: memory_monitoring
2025-05-31 15:35:37,856 [INFO] agentlee.mods: Cluster 'memory_monitoring' loaded successfully
2025-05-31 15:35:37,857 [INFO] agentlee.mods: Loading mod cluster: task_management
2025-05-31 15:35:37,857 [INFO] agentlee.mods: Cluster 'task_management' loaded successfully
2025-05-31 15:35:37,857 [INFO] agentlee.mods: Loading mod cluster: ai_frontend
2025-05-31 15:35:37,857 [INFO] agentlee.mods: Cluster 'ai_frontend' loaded successfully
2025-05-31 15:35:37,858 [INFO] agentlee.mods: Loading mod cluster: system_integration
2025-05-31 15:35:37,858 [INFO] agentlee.mods: Cluster 'system_integration' loaded successfully
2025-05-31 15:35:37,858 [INFO] agentlee.mods: Loading mod cluster: ai_voice
2025-05-31 15:35:37,858 [INFO] agentlee.mods: Cluster 'ai_voice' loaded successfully
2025-05-31 15:35:37,858 [INFO] agentlee.mods: Loaded 8/8 mod clusters successfully
2025-05-31 15:35:37,858 [INFO] agentlee.controller: Agent Lee mods loaded: {'core_system': True, 'app_control': True, 'analytics': True, 'memory_monitoring': True, 'task_management': True, 'ai_frontend': True, 'system_integration': True, 'ai_voice': True}
2025-05-31 15:35:37,859 [INFO] agentlee.controller: Background health monitoring started
2025-05-31 15:35:37,859 [INFO] agentlee.controller: Configuration: {'host': '127.0.0.1', 'port': 8000, 'debug': False, 'database': 'sqlite:///./agentlee.db', 'gemini_configured': True, 'ollama_url': 'http://localhost:11434', 'log_level': 'INFO'}
2025-05-31 15:35:40,361 [ERROR] agentlee.controller: Get personality status error: 'State' object has no attribute 'llm_service'
2025-05-31 15:35:47,183 [INFO] comtypes.client._code_cache: Imported existing <module 'comtypes.gen' from 'D:\\Agentleefreshidea\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-31 15:35:47,184 [INFO] comtypes.client._code_cache: Using writeable comtypes cache directory: 'D:\Agentleefreshidea\.venv\Lib\site-packages\comtypes\gen'
2025-05-31 15:35:49,618 [ERROR] agentlee.controller: TTS error: run loop already started
2025-05-31 15:37:54,745 [ERROR] agentlee.controller: Get personality status error: 'State' object has no attribute 'llm_service'
2025-05-31 15:38:04,019 [ERROR] agentlee.controller: TTS error: run loop already started
2025-05-31 15:38:44,460 [ERROR] agentlee.controller: LLM processing error: LLMService.generate_response() got an unexpected keyword argument 'prompt'
2025-05-31 15:47:17,610 [INFO] agentlee.llm: Gemini API initialized successfully
2025-05-31 15:47:17,610 [INFO] agentlee.llm: OpenAI available as fallback
2025-05-31 15:47:17,610 [INFO] agentlee.llm: Anthropic available as fallback
2025-05-31 15:47:17,610 [INFO] agentlee.llm: Ollama available as fallback
2025-05-31 15:47:17,611 [INFO] agentlee.controller: LLM Service initialized: {'primary_provider': 'gemini', 'fallback_providers': ['openai', 'anthropic', 'ollama'], 'personality_mode': 'default', 'available_models': ['gemini (primary)', 'openai (fallback)', 'anthropic (fallback)', 'ollama (fallback)']}
2025-05-31 15:47:17,611 [INFO] agentlee.system: System Controller initialized
2025-05-31 15:47:17,611 [INFO] agentlee.controller: System Controller initialized
2025-05-31 15:47:17,679 [INFO] agentlee.controller: Agent Lee� System Controller v2.0 starting up...
2025-05-31 15:47:17,682 [INFO] agentlee.controller: Database tables created/verified
2025-05-31 15:47:17,682 [INFO] agentlee.mods: Loading Agent Lee mod clusters...
2025-05-31 15:47:17,682 [INFO] agentlee.mods: Loading mod cluster: core_system
2025-05-31 15:47:17,683 [INFO] agentlee.mods.permissions: System Permissions Manager initialized
2025-05-31 15:47:17,683 [INFO] agentlee.mods.permissions: Permission 'file_access' already granted
2025-05-31 15:47:17,683 [INFO] agentlee.mods.permissions: Permission 'app_control' already granted
2025-05-31 15:47:17,683 [INFO] agentlee.mods.permissions: Permission 'system_info' already granted
2025-05-31 15:47:17,684 [INFO] agentlee.mods.permissions: Permission 'web_access' already granted
2025-05-31 15:47:17,684 [INFO] agentlee.mods: Cluster 'core_system' loaded successfully
2025-05-31 15:47:17,684 [INFO] agentlee.mods: Loading mod cluster: app_control
2025-05-31 15:47:17,685 [INFO] agentlee.mods.app_controller: Enhanced App Controller initialized
2025-05-31 15:47:17,685 [INFO] agentlee.mods: Cluster 'app_control' loaded successfully
2025-05-31 15:47:17,685 [INFO] agentlee.mods: Loading mod cluster: analytics
2025-05-31 15:47:17,685 [INFO] agentlee.mods: Cluster 'analytics' loaded successfully
2025-05-31 15:47:17,685 [INFO] agentlee.mods: Loading mod cluster: memory_monitoring
2025-05-31 15:47:17,685 [INFO] agentlee.mods: Cluster 'memory_monitoring' loaded successfully
2025-05-31 15:47:17,686 [INFO] agentlee.mods: Loading mod cluster: task_management
2025-05-31 15:47:17,686 [INFO] agentlee.mods: Cluster 'task_management' loaded successfully
2025-05-31 15:47:17,686 [INFO] agentlee.mods: Loading mod cluster: ai_frontend
2025-05-31 15:47:17,686 [INFO] agentlee.mods: Cluster 'ai_frontend' loaded successfully
2025-05-31 15:47:17,686 [INFO] agentlee.mods: Loading mod cluster: system_integration
2025-05-31 15:47:17,686 [INFO] agentlee.mods: Cluster 'system_integration' loaded successfully
2025-05-31 15:47:17,687 [INFO] agentlee.mods: Loading mod cluster: ai_voice
2025-05-31 15:47:17,687 [INFO] agentlee.mods: Cluster 'ai_voice' loaded successfully
2025-05-31 15:47:17,687 [INFO] agentlee.mods: Loaded 8/8 mod clusters successfully
2025-05-31 15:47:17,687 [INFO] agentlee.controller: Agent Lee mods loaded: {'core_system': True, 'app_control': True, 'analytics': True, 'memory_monitoring': True, 'task_management': True, 'ai_frontend': True, 'system_integration': True, 'ai_voice': True}
2025-05-31 15:47:17,687 [INFO] agentlee.controller: Background health monitoring started
2025-05-31 15:47:17,687 [INFO] agentlee.controller: Configuration: {'host': '127.0.0.1', 'port': 8000, 'debug': False, 'database': 'sqlite:///./agentlee.db', 'gemini_configured': True, 'ollama_url': 'http://localhost:11434', 'log_level': 'INFO'}
2025-05-31 15:47:20,380 [ERROR] agentlee.controller: Get personality status error: 'State' object has no attribute 'llm_service'
2025-05-31 15:47:27,217 [INFO] comtypes.client._code_cache: Imported existing <module 'comtypes.gen' from 'D:\\Agentleefreshidea\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-31 15:47:27,218 [INFO] comtypes.client._code_cache: Using writeable comtypes cache directory: 'D:\Agentleefreshidea\.venv\Lib\site-packages\comtypes\gen'
2025-05-31 15:47:29,666 [ERROR] agentlee.controller: TTS error: run loop already started
2025-05-31 15:49:57,498 [ERROR] agentlee.controller: Get personality status error: 'State' object has no attribute 'llm_service'
2025-05-31 15:50:06,750 [ERROR] agentlee.controller: TTS error: run loop already started
2025-05-31 17:10:49,926 [ERROR] agentlee.controller: TTS error: run loop already started
2025-05-31 17:12:02,463 [ERROR] agentlee.controller: Get personality status error: 'State' object has no attribute 'llm_service'
2025-05-31 17:12:11,717 [ERROR] agentlee.controller: TTS error: run loop already started
2025-05-31 17:15:54,228 [INFO] agentlee.system: Opened telegram messaging app
2025-05-31 17:16:03,836 [INFO] agentlee.system: Opened email client
2025-05-31 17:31:53,319 [ERROR] agentlee.controller: Get personality status error: 'State' object has no attribute 'llm_service'
2025-05-31 17:32:02,551 [ERROR] agentlee.controller: TTS error: run loop already started
2025-05-31 17:59:28,875 [ERROR] agentlee.controller: Get personality status error: 'State' object has no attribute 'llm_service'
2025-05-31 17:59:38,162 [ERROR] agentlee.controller: TTS error: run loop already started
2025-05-31 18:00:04,479 [ERROR] agentlee.controller: Personality change error: 'State' object has no attribute 'llm_service'
2025-05-31 18:00:06,848 [ERROR] agentlee.llm: Gemini error: 403 Requests to this API generativelanguage.googleapis.com method google.ai.generativelanguage.v1beta.GenerativeService.GenerateContent are blocked. [reason: "API_KEY_SERVICE_BLOCKED"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
metadata {
  key: "methodName"
  value: "google.ai.generativelanguage.v1beta.GenerativeService.GenerateContent"
}
metadata {
  key: "consumer"
  value: "projects/249742914141"
}
metadata {
  key: "apiName"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "Requests to this API generativelanguage.googleapis.com method google.ai.generativelanguage.v1beta.GenerativeService.GenerateContent are blocked."
]
2025-05-31 18:00:06,849 [ERROR] agentlee.llm: LLM generation error: 403 Requests to this API generativelanguage.googleapis.com method google.ai.generativelanguage.v1beta.GenerativeService.GenerateContent are blocked. [reason: "API_KEY_SERVICE_BLOCKED"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
metadata {
  key: "methodName"
  value: "google.ai.generativelanguage.v1beta.GenerativeService.GenerateContent"
}
metadata {
  key: "consumer"
  value: "projects/249742914141"
}
metadata {
  key: "apiName"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "Requests to this API generativelanguage.googleapis.com method google.ai.generativelanguage.v1beta.GenerativeService.GenerateContent are blocked."
]
2025-05-31 18:00:06,850 [ERROR] agentlee.controller: LLM processing error: unsupported format string passed to NoneType.__format__
2025-05-31 18:13:10,617 [ERROR] agentlee.controller: TTS error: run loop already started
2025-05-31 20:16:21,558 [INFO] agentlee.llm: Gemini API initialized successfully
2025-05-31 20:16:21,558 [INFO] agentlee.llm: OpenAI available as fallback
2025-05-31 20:16:21,559 [INFO] agentlee.llm: Anthropic available as fallback
2025-05-31 20:16:21,559 [INFO] agentlee.llm: Ollama available as fallback
2025-05-31 20:16:21,559 [INFO] agentlee.controller: LLM Service initialized: {'primary_provider': 'gemini', 'fallback_providers': ['openai', 'anthropic', 'ollama'], 'personality_mode': 'default', 'available_models': ['gemini (primary)', 'openai (fallback)', 'anthropic (fallback)', 'ollama (fallback)']}
2025-05-31 20:16:21,559 [INFO] agentlee.system: System Controller initialized
2025-05-31 20:16:21,560 [INFO] agentlee.controller: System Controller initialized
2025-05-31 20:16:21,651 [INFO] agentlee.controller: Agent Lee� System Controller v2.0 starting up...
2025-05-31 20:16:21,653 [INFO] agentlee.controller: Database tables created/verified
2025-05-31 20:16:21,653 [INFO] agentlee.mods: Loading Agent Lee mod clusters...
2025-05-31 20:16:21,654 [INFO] agentlee.mods: Loading mod cluster: core_system
2025-05-31 20:16:21,654 [INFO] agentlee.mods.permissions: System Permissions Manager initialized
2025-05-31 20:16:21,655 [INFO] agentlee.mods.permissions: Permission 'file_access' already granted
2025-05-31 20:16:21,655 [INFO] agentlee.mods.permissions: Permission 'app_control' already granted
2025-05-31 20:16:21,655 [INFO] agentlee.mods.permissions: Permission 'system_info' already granted
2025-05-31 20:16:21,655 [INFO] agentlee.mods.permissions: Permission 'web_access' already granted
2025-05-31 20:16:21,656 [INFO] agentlee.mods: Cluster 'core_system' loaded successfully
2025-05-31 20:16:21,656 [INFO] agentlee.mods: Loading mod cluster: app_control
2025-05-31 20:16:21,656 [INFO] agentlee.mods.app_controller: Enhanced App Controller initialized
2025-05-31 20:16:21,657 [INFO] agentlee.mods: Cluster 'app_control' loaded successfully
2025-05-31 20:16:21,657 [INFO] agentlee.mods: Loading mod cluster: analytics
2025-05-31 20:16:21,657 [INFO] agentlee.mods: Cluster 'analytics' loaded successfully
2025-05-31 20:16:21,657 [INFO] agentlee.mods: Loading mod cluster: memory_monitoring
2025-05-31 20:16:21,657 [INFO] agentlee.mods: Cluster 'memory_monitoring' loaded successfully
2025-05-31 20:16:21,658 [INFO] agentlee.mods: Loading mod cluster: task_management
2025-05-31 20:16:21,658 [INFO] agentlee.mods: Cluster 'task_management' loaded successfully
2025-05-31 20:16:21,658 [INFO] agentlee.mods: Loading mod cluster: ai_frontend
2025-05-31 20:16:21,658 [INFO] agentlee.mods: Cluster 'ai_frontend' loaded successfully
2025-05-31 20:16:21,658 [INFO] agentlee.mods: Loading mod cluster: system_integration
2025-05-31 20:16:21,658 [INFO] agentlee.mods: Cluster 'system_integration' loaded successfully
2025-05-31 20:16:21,658 [INFO] agentlee.mods: Loading mod cluster: ai_voice
2025-05-31 20:16:21,659 [INFO] agentlee.mods: Cluster 'ai_voice' loaded successfully
2025-05-31 20:16:21,659 [INFO] agentlee.mods: Loaded 8/8 mod clusters successfully
2025-05-31 20:16:21,659 [INFO] agentlee.controller: Agent Lee mods loaded: {'core_system': True, 'app_control': True, 'analytics': True, 'memory_monitoring': True, 'task_management': True, 'ai_frontend': True, 'system_integration': True, 'ai_voice': True}
2025-05-31 20:16:21,659 [INFO] agentlee.controller: Background health monitoring started
2025-05-31 20:16:21,659 [INFO] agentlee.controller: Configuration: {'host': '127.0.0.1', 'port': 8000, 'debug': False, 'database': 'sqlite:///./agentlee.db', 'gemini_configured': True, 'ollama_url': 'http://localhost:11434', 'log_level': 'INFO'}
2025-05-31 20:16:32,914 [ERROR] agentlee.controller: Get personality status error: 'State' object has no attribute 'llm_service'
2025-05-31 20:16:39,746 [INFO] comtypes.client._code_cache: Imported existing <module 'comtypes.gen' from 'D:\\Agentleefreshidea\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-31 20:16:39,747 [INFO] comtypes.client._code_cache: Using writeable comtypes cache directory: 'D:\Agentleefreshidea\.venv\Lib\site-packages\comtypes\gen'
2025-05-31 20:16:42,188 [ERROR] agentlee.controller: TTS error: run loop already started
2025-05-31 20:17:55,439 [ERROR] agentlee.controller: Get personality status error: 'State' object has no attribute 'llm_service'
2025-05-31 20:18:03,552 [ERROR] agentlee.controller: Get memory error: 'State' object has no attribute 'llm_service'
2025-05-31 20:18:04,710 [ERROR] agentlee.controller: TTS error: run loop already started
2025-05-31 20:20:29,928 [ERROR] agentlee.controller: Web search error: 'SystemController' object has no attribute 'web_search'
2025-05-31 20:21:08,419 [INFO] agentlee.system: Opened telegram messaging app
2025-05-31 20:21:15,428 [INFO] agentlee.system: Opened email client
2025-05-31 20:30:53,781 [ERROR] agentlee.controller: TTS error: run loop already started
2025-05-31 20:31:34,360 [ERROR] agentlee.controller: Web search error: 'SystemController' object has no attribute 'web_search'
2025-05-31 20:32:00,578 [ERROR] agentlee.controller: TTS error: run loop already started
2025-05-31 20:36:12,502 [ERROR] agentlee.controller: Get personality status error: 'State' object has no attribute 'llm_service'
2025-05-31 20:36:21,772 [ERROR] agentlee.controller: TTS error: run loop already started
