/**
 * Agent Lee™ Echo Engine Client
 * Handles user clicks on app icons in the Echo Launcher and coordinates the entire Echo process
 */

class EchoEngineClient {
    constructor() {
        this.isElectron = typeof window.electronAPI !== 'undefined';
        this.activeEchoes = new Map();
        this.echoCounter = 0;
        this.initialized = false;
    }

    async initialize() {
        console.log('🔄 Initializing Echo Engine Client...');
        
        try {
            // Set up Echo Engine event handlers
            this.setupEchoEventHandlers();
            
            // Initialize Electron bridge if available
            if (this.isElectron) {
                await this.initializeElectronBridge();
            }
            
            this.initialized = true;
            console.log('✅ Echo Engine Client initialized successfully');
            
        } catch (error) {
            console.error('❌ Echo Engine Client initialization failed:', error);
            throw error;
        }
    }

    async initializeElectronBridge() {
        if (this.isElectron && window.electronAPI) {
            // Set up IPC handlers for Echo Engine
            window.electronAPI.onAppLaunched((echoData) => {
                this.handleAppLaunched(echoData);
            });

            window.electronAPI.onEchoStreamReady((echoId, streamData) => {
                this.handleEchoStreamReady(echoId, streamData);
            });

            window.electronAPI.onEchoError((echoId, error) => {
                this.handleEchoError(echoId, error);
            });
        }
    }

    setupEchoEventHandlers() {
        // Listen for app launch requests from ApplicationScannerClient
        document.addEventListener('appLaunchRequested', (event) => {
            this.handleAppLaunchRequest(event.detail);
        });

        // Listen for Echo GUI creation events
        document.addEventListener('echoGUICreated', (event) => {
            this.handleEchoGUICreated(event.detail);
        });

        // Listen for Echo GUI close events
        document.addEventListener('echoGUIClosed', (event) => {
            this.handleEchoGUIClosed(event.detail);
        });
    }

    /**
     * Handle app launch request from the Echo Launcher
     * @param {Object} request - Launch request details
     * @param {Object} request.app - Application details
     * @param {string} request.deviceType - Device type (computer, phone, tablet, laptop)
     */
    async handleAppLaunchRequest(request) {
        const { app, deviceType } = request;
        
        console.log(`🚀 Echo launch requested: ${app.name} on ${deviceType}`);

        try {
            // Check permissions first
            const hasPermission = await this.checkEchoPermission(app, deviceType);
            if (!hasPermission) {
                console.log('❌ Echo permission denied');
                return;
            }

            // Create Echo session
            const echoId = await this.createEchoSession(app, deviceType);
            
            // Launch the application
            await this.launchApplication(echoId, app, deviceType);
            
            // Create Echo GUI
            await this.createEchoGUI(echoId, app, deviceType);
            
            console.log(`✅ Echo session created: ${echoId}`);
            
        } catch (error) {
            console.error('❌ Failed to create Echo session:', error);
            this.showEchoError(app.name, error.message);
        }
    }

    async checkEchoPermission(app, deviceType) {
        if (!window.PermissionManagerClient) {
            console.warn('⚠️ Permission Manager not available, proceeding without permission check');
            return true;
        }

        try {
            return await window.PermissionManagerClient.requestEchoPermission(app, deviceType);
        } catch (error) {
            console.error('❌ Permission check failed:', error);
            return false;
        }
    }

    async createEchoSession(app, deviceType) {
        const echoId = `echo_${++this.echoCounter}_${Date.now()}`;
        
        const echoSession = {
            id: echoId,
            app: app,
            deviceType: deviceType,
            status: 'initializing',
            createdAt: Date.now(),
            guiId: null,
            streamId: null,
            processId: null
        };

        this.activeEchoes.set(echoId, echoSession);
        
        console.log(`🔄 Echo session created: ${echoId}`);
        return echoId;
    }

    async launchApplication(echoId, app, deviceType) {
        const echoSession = this.activeEchoes.get(echoId);
        if (!echoSession) {
            throw new Error(`Echo session not found: ${echoId}`);
        }

        console.log(`🚀 Launching ${app.name} on ${deviceType}...`);
        
        try {
            echoSession.status = 'launching';

            if (deviceType === 'computer' && this.isElectron) {
                // Launch local application via Electron
                const launchResult = await window.electronAPI.launchOsAppAndGetPid({
                    path: app.path,
                    name: app.name,
                    id: app.id,
                    echoId: echoId
                });

                echoSession.processId = launchResult.pid;
                echoSession.windowId = launchResult.windowId;
                
            } else {
                // Launch remote application via DeviceSyncAgent
                if (window.DeviceSyncAgent) {
                    const launchResult = await window.DeviceSyncAgent.launchRemoteApp(deviceType, app, echoId);
                    echoSession.remoteSessionId = launchResult.sessionId;
                } else {
                    throw new Error('DeviceSyncAgent not available for remote app launch');
                }
            }

            echoSession.status = 'launched';
            console.log(`✅ ${app.name} launched successfully`);
            
        } catch (error) {
            echoSession.status = 'launch_failed';
            console.error(`❌ Failed to launch ${app.name}:`, error);
            throw error;
        }
    }

    async createEchoGUI(echoId, app, deviceType) {
        const echoSession = this.activeEchoes.get(echoId);
        if (!echoSession) {
            throw new Error(`Echo session not found: ${echoId}`);
        }

        console.log(`🎨 Creating Echo GUI for ${app.name}...`);

        try {
            echoSession.status = 'creating_gui';

            if (!window.GUIManager) {
                throw new Error('GUIManager not available');
            }

            // Create floating GUI window
            const guiId = await window.GUIManager.createGUI({
                appName: app.name,
                appId: app.id,
                deviceType: deviceType,
                echoId: echoId,
                windowOptions: {
                    width: 800,
                    height: 600,
                    resizable: true,
                    frame: false,
                    transparent: true,
                    alwaysOnTop: false
                }
            });

            echoSession.guiId = guiId;
            echoSession.status = 'gui_created';
            
            console.log(`✅ Echo GUI created: ${guiId}`);
            
            // Start mirroring process
            await this.startEchoMirroring(echoId);
            
        } catch (error) {
            echoSession.status = 'gui_failed';
            console.error(`❌ Failed to create Echo GUI:`, error);
            throw error;
        }
    }

    async startEchoMirroring(echoId) {
        const echoSession = this.activeEchoes.get(echoId);
        if (!echoSession) {
            throw new Error(`Echo session not found: ${echoId}`);
        }

        console.log(`🔄 Starting Echo mirroring for ${echoSession.app.name}...`);

        try {
            echoSession.status = 'starting_mirror';

            if (echoSession.deviceType === 'computer' && this.isElectron) {
                // Start local desktop capture
                await this.startLocalMirroring(echoId);
            } else {
                // Start remote mirroring via WebRTC
                await this.startRemoteMirroring(echoId);
            }

            echoSession.status = 'mirroring_active';
            console.log(`✅ Echo mirroring started for ${echoSession.app.name}`);
            
        } catch (error) {
            echoSession.status = 'mirror_failed';
            console.error(`❌ Failed to start Echo mirroring:`, error);
            throw error;
        }
    }

    async startLocalMirroring(echoId) {
        const echoSession = this.activeEchoes.get(echoId);
        
        if (this.isElectron && window.electronAPI) {
            // Request Electron to start desktop capture for the launched app
            const mirrorResult = await window.electronAPI.createEchoGuiAndMirror({
                appId: echoSession.app.id,
                appName: echoSession.app.name,
                osWindowIdToMirror: echoSession.windowId,
                electronEchoWindowId: echoSession.guiId,
                echoId: echoId
            });

            echoSession.streamId = mirrorResult.streamId;
        } else {
            throw new Error('Electron API not available for local mirroring');
        }
    }

    async startRemoteMirroring(echoId) {
        const echoSession = this.activeEchoes.get(echoId);
        
        if (window.DeviceSyncAgent) {
            // Request remote device to start mirroring via WebRTC
            const mirrorResult = await window.DeviceSyncAgent.startRemoteMirroring(
                echoSession.deviceType,
                echoSession.remoteSessionId,
                echoId
            );

            echoSession.streamId = mirrorResult.streamId;
        } else {
            throw new Error('DeviceSyncAgent not available for remote mirroring');
        }
    }

    handleAppLaunched(echoData) {
        console.log('🚀 App launched:', echoData);
        
        const echoSession = this.activeEchoes.get(echoData.echoId);
        if (echoSession) {
            echoSession.processId = echoData.pid;
            echoSession.windowId = echoData.windowId;
            echoSession.status = 'launched';
        }
    }

    handleEchoStreamReady(echoId, streamData) {
        console.log('📺 Echo stream ready:', echoId, streamData);
        
        const echoSession = this.activeEchoes.get(echoId);
        if (echoSession) {
            echoSession.streamId = streamData.streamId;
            echoSession.status = 'streaming';
            
            // Notify GUI to start displaying the stream
            this.notifyGUIStreamReady(echoSession.guiId, streamData);
        }
    }

    handleEchoError(echoId, error) {
        console.error('❌ Echo error:', echoId, error);
        
        const echoSession = this.activeEchoes.get(echoId);
        if (echoSession) {
            echoSession.status = 'error';
            echoSession.error = error;
            
            this.showEchoError(echoSession.app.name, error.message);
        }
    }

    handleEchoGUICreated(guiConfig) {
        console.log('🎨 Echo GUI created:', guiConfig);
        
        // Find the Echo session for this GUI
        for (const [echoId, session] of this.activeEchoes) {
            if (session.guiId === guiConfig.id) {
                session.status = 'gui_ready';
                break;
            }
        }
    }

    handleEchoGUIClosed(guiData) {
        console.log('🎨 Echo GUI closed:', guiData);
        
        // Find and clean up the Echo session
        for (const [echoId, session] of this.activeEchoes) {
            if (session.guiId === guiData.guiId) {
                this.cleanupEchoSession(echoId);
                break;
            }
        }
    }

    async cleanupEchoSession(echoId) {
        const echoSession = this.activeEchoes.get(echoId);
        if (!echoSession) return;

        console.log(`🧹 Cleaning up Echo session: ${echoId}`);

        try {
            // Stop mirroring
            if (echoSession.streamId) {
                if (this.isElectron && window.electronAPI) {
                    await window.electronAPI.stopEchoMirroring(echoSession.streamId);
                } else if (window.DeviceSyncAgent) {
                    await window.DeviceSyncAgent.stopRemoteMirroring(echoSession.streamId);
                }
            }

            // Close application if needed
            if (echoSession.processId && this.isElectron) {
                // Optionally close the application
                // await window.electronAPI.closeApplication(echoSession.processId);
            }

            // Remove from active echoes
            this.activeEchoes.delete(echoId);
            
            console.log(`✅ Echo session cleaned up: ${echoId}`);
            
        } catch (error) {
            console.error(`❌ Failed to cleanup Echo session ${echoId}:`, error);
        }
    }

    notifyGUIStreamReady(guiId, streamData) {
        const event = new CustomEvent('echoStreamReady', {
            detail: {
                guiId: guiId,
                streamData: streamData
            }
        });
        document.dispatchEvent(event);
    }

    showEchoError(appName, errorMessage) {
        // Show user-friendly error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'echo-error-notification';
        errorDiv.innerHTML = `
            <div class="error-content">
                <div class="error-icon">❌</div>
                <div class="error-text">
                    <div class="error-title">Echo Failed</div>
                    <div class="error-message">Could not create Echo for ${appName}: ${errorMessage}</div>
                </div>
                <button class="error-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        // Add styles if not already added
        if (!document.getElementById('echo-error-styles')) {
            const styles = document.createElement('style');
            styles.id = 'echo-error-styles';
            styles.textContent = `
                .echo-error-notification {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: linear-gradient(145deg, #dc2626, #b91c1c);
                    color: white;
                    border-radius: 12px;
                    padding: 15px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
                    z-index: 10000;
                    max-width: 300px;
                    animation: slideIn 0.3s ease;
                }

                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }

                .error-content {
                    display: flex;
                    align-items: flex-start;
                    gap: 10px;
                }

                .error-icon {
                    font-size: 20px;
                }

                .error-text {
                    flex: 1;
                }

                .error-title {
                    font-weight: bold;
                    margin-bottom: 5px;
                }

                .error-message {
                    font-size: 12px;
                    opacity: 0.9;
                }

                .error-close {
                    background: none;
                    border: none;
                    color: white;
                    font-size: 16px;
                    cursor: pointer;
                    padding: 0;
                    width: 20px;
                    height: 20px;
                }
            `;
            document.head.appendChild(styles);
        }

        document.body.appendChild(errorDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentElement) {
                errorDiv.remove();
            }
        }, 5000);
    }

    getActiveEchoes() {
        return Array.from(this.activeEchoes.values());
    }

    getEchoSession(echoId) {
        return this.activeEchoes.get(echoId);
    }

    async stopEcho(echoId) {
        await this.cleanupEchoSession(echoId);
    }

    async stopAllEchoes() {
        const echoIds = Array.from(this.activeEchoes.keys());
        
        for (const echoId of echoIds) {
            await this.cleanupEchoSession(echoId);
        }
    }
}

// Create global instance
window.EchoEngineClient = new EchoEngineClient();
