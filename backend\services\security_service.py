"""
Agent Lee™ Security Service
Production-ready security implementation with JWT authentication,
rate limiting, and comprehensive security measures
"""

import os
import time
import hashlib
import secrets
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
import json

# Security imports
from passlib.context import CryptContext
from jose import JW<PERSON><PERSON>r, jwt
import bcrypt

# FastAPI security
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status, Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

logger = logging.getLogger("agentlee.security")

@dataclass
class SecurityConfig:
    """Security configuration"""
    SECRET_KEY: str = os.getenv("SECRET_KEY", secrets.token_urlsafe(32))
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    MAX_LOGIN_ATTEMPTS: int = 5
    LOCKOUT_DURATION_MINUTES: int = 15
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_WINDOW_MINUTES: int = 1
    PASSWORD_MIN_LENGTH: int = 8
    REQUIRE_SPECIAL_CHARS: bool = True
    SESSION_TIMEOUT_MINUTES: int = 60

@dataclass
class User:
    """User model"""
    id: str
    username: str
    email: str
    hashed_password: str
    is_active: bool = True
    is_admin: bool = False
    created_at: datetime = None
    last_login: datetime = None
    failed_login_attempts: int = 0
    locked_until: Optional[datetime] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now(timezone.utc)

@dataclass
class SecurityEvent:
    """Security event for logging"""
    event_type: str
    user_id: Optional[str]
    ip_address: str
    user_agent: str
    timestamp: datetime
    details: Dict[str, Any]
    severity: str = "info"  # info, warning, error, critical

class SecurityService:
    """Comprehensive security service for Agent Lee"""
    
    def __init__(self):
        self.config = SecurityConfig()
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        self.security = HTTPBearer()
        
        # In-memory stores (in production, use Redis/database)
        self.users: Dict[str, User] = {}
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.rate_limit_store: Dict[str, List[float]] = {}
        self.security_events: List[SecurityEvent] = []
        self.blacklisted_tokens: set = set()
        
        # Initialize admin user
        self._create_admin_user()
        
        logger.info("Security Service initialized")
    
    def _create_admin_user(self):
        """Create default admin user"""
        admin_password = os.getenv("ADMIN_PASSWORD", "AgentLee2024!")
        admin_user = User(
            id="admin",
            username="admin",
            email="<EMAIL>",
            hashed_password=self.get_password_hash(admin_password),
            is_admin=True
        )
        self.users["admin"] = admin_user
        logger.info("Default admin user created")
    
    def get_password_hash(self, password: str) -> str:
        """Hash a password"""
        return self.pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash"""
        return self.pwd_context.verify(plain_password, hashed_password)
    
    def validate_password_strength(self, password: str) -> Dict[str, Any]:
        """Validate password strength"""
        issues = []
        
        if len(password) < self.config.PASSWORD_MIN_LENGTH:
            issues.append(f"Password must be at least {self.config.PASSWORD_MIN_LENGTH} characters")
        
        if self.config.REQUIRE_SPECIAL_CHARS:
            if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
                issues.append("Password must contain at least one special character")
            
            if not any(c.isupper() for c in password):
                issues.append("Password must contain at least one uppercase letter")
            
            if not any(c.islower() for c in password):
                issues.append("Password must contain at least one lowercase letter")
            
            if not any(c.isdigit() for c in password):
                issues.append("Password must contain at least one digit")
        
        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "strength": self._calculate_password_strength(password)
        }
    
    def _calculate_password_strength(self, password: str) -> str:
        """Calculate password strength"""
        score = 0
        
        if len(password) >= 8:
            score += 1
        if len(password) >= 12:
            score += 1
        if any(c.isupper() for c in password):
            score += 1
        if any(c.islower() for c in password):
            score += 1
        if any(c.isdigit() for c in password):
            score += 1
        if any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            score += 1
        
        if score <= 2:
            return "weak"
        elif score <= 4:
            return "medium"
        else:
            return "strong"
    
    def create_access_token(self, data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """Create JWT access token"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.now(timezone.utc) + expires_delta
        else:
            expire = datetime.now(timezone.utc) + timedelta(minutes=self.config.ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire, "type": "access"})
        
        encoded_jwt = jwt.encode(to_encode, self.config.SECRET_KEY, algorithm=self.config.ALGORITHM)
        return encoded_jwt
    
    def create_refresh_token(self, data: Dict[str, Any]) -> str:
        """Create JWT refresh token"""
        to_encode = data.copy()
        expire = datetime.now(timezone.utc) + timedelta(days=self.config.REFRESH_TOKEN_EXPIRE_DAYS)
        to_encode.update({"exp": expire, "type": "refresh"})
        
        encoded_jwt = jwt.encode(to_encode, self.config.SECRET_KEY, algorithm=self.config.ALGORITHM)
        return encoded_jwt
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """Verify and decode JWT token"""
        try:
            if token in self.blacklisted_tokens:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token has been revoked"
                )
            
            payload = jwt.decode(token, self.config.SECRET_KEY, algorithms=[self.config.ALGORITHM])
            return payload
            
        except JWTError as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials"
            )
    
    def authenticate_user(self, username: str, password: str, ip_address: str, user_agent: str) -> Optional[User]:
        """Authenticate user with comprehensive security checks"""
        user = self.users.get(username)
        
        if not user:
            self._log_security_event("login_failed", None, ip_address, user_agent, 
                                    {"reason": "user_not_found", "username": username}, "warning")
            return None
        
        # Check if user is locked
        if user.locked_until and datetime.now(timezone.utc) < user.locked_until:
            self._log_security_event("login_blocked", user.id, ip_address, user_agent,
                                    {"reason": "account_locked"}, "warning")
            return None
        
        # Verify password
        if not self.verify_password(password, user.hashed_password):
            user.failed_login_attempts += 1
            
            # Lock account if too many failed attempts
            if user.failed_login_attempts >= self.config.MAX_LOGIN_ATTEMPTS:
                user.locked_until = datetime.now(timezone.utc) + timedelta(
                    minutes=self.config.LOCKOUT_DURATION_MINUTES
                )
                self._log_security_event("account_locked", user.id, ip_address, user_agent,
                                       {"failed_attempts": user.failed_login_attempts}, "error")
            
            self._log_security_event("login_failed", user.id, ip_address, user_agent,
                                   {"reason": "invalid_password", "attempts": user.failed_login_attempts}, "warning")
            return None
        
        # Check if user is active
        if not user.is_active:
            self._log_security_event("login_blocked", user.id, ip_address, user_agent,
                                   {"reason": "account_disabled"}, "warning")
            return None
        
        # Successful login
        user.failed_login_attempts = 0
        user.locked_until = None
        user.last_login = datetime.now(timezone.utc)
        
        self._log_security_event("login_success", user.id, ip_address, user_agent, {}, "info")
        
        return user
    
    def check_rate_limit(self, identifier: str, request: Request) -> bool:
        """Check rate limiting for requests"""
        current_time = time.time()
        window_start = current_time - (self.config.RATE_LIMIT_WINDOW_MINUTES * 60)
        
        # Clean old entries
        if identifier in self.rate_limit_store:
            self.rate_limit_store[identifier] = [
                timestamp for timestamp in self.rate_limit_store[identifier]
                if timestamp > window_start
            ]
        else:
            self.rate_limit_store[identifier] = []
        
        # Check if limit exceeded
        if len(self.rate_limit_store[identifier]) >= self.config.RATE_LIMIT_REQUESTS:
            self._log_security_event("rate_limit_exceeded", None, 
                                    request.client.host if request.client else "unknown",
                                    request.headers.get("user-agent", "unknown"),
                                    {"identifier": identifier}, "warning")
            return False
        
        # Add current request
        self.rate_limit_store[identifier].append(current_time)
        return True
    
    def create_session(self, user: User, ip_address: str, user_agent: str) -> str:
        """Create user session"""
        session_id = secrets.token_urlsafe(32)
        
        session_data = {
            "user_id": user.id,
            "username": user.username,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "created_at": datetime.now(timezone.utc),
            "last_activity": datetime.now(timezone.utc),
            "is_admin": user.is_admin
        }
        
        self.active_sessions[session_id] = session_data
        
        self._log_security_event("session_created", user.id, ip_address, user_agent,
                               {"session_id": session_id}, "info")
        
        return session_id
    
    def validate_session(self, session_id: str, ip_address: str) -> Optional[Dict[str, Any]]:
        """Validate user session"""
        session = self.active_sessions.get(session_id)
        
        if not session:
            return None
        
        # Check session timeout
        last_activity = session["last_activity"]
        if isinstance(last_activity, str):
            last_activity = datetime.fromisoformat(last_activity)
        
        timeout_threshold = datetime.now(timezone.utc) - timedelta(
            minutes=self.config.SESSION_TIMEOUT_MINUTES
        )
        
        if last_activity < timeout_threshold:
            self.invalidate_session(session_id)
            return None
        
        # Update last activity
        session["last_activity"] = datetime.now(timezone.utc)
        
        return session
    
    def invalidate_session(self, session_id: str):
        """Invalidate user session"""
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            self._log_security_event("session_invalidated", session.get("user_id"),
                                   session.get("ip_address", "unknown"),
                                   session.get("user_agent", "unknown"),
                                   {"session_id": session_id}, "info")
            del self.active_sessions[session_id]
    
    def revoke_token(self, token: str):
        """Revoke JWT token"""
        self.blacklisted_tokens.add(token)
        
        try:
            payload = jwt.decode(token, self.config.SECRET_KEY, algorithms=[self.config.ALGORITHM])
            user_id = payload.get("sub")
            self._log_security_event("token_revoked", user_id, "system", "system",
                                   {"token_type": payload.get("type", "unknown")}, "info")
        except JWTError:
            pass
    
    def get_current_user(self, credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())) -> User:
        """Get current authenticated user from token"""
        token = credentials.credentials
        payload = self.verify_token(token)
        
        username = payload.get("sub")
        if username is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials"
            )
        
        user = self.users.get(username)
        if user is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found"
            )
        
        return user
    
    def require_admin(self, current_user: User = Depends(get_current_user)) -> User:
        """Require admin privileges"""
        if not current_user.is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin privileges required"
            )
        return current_user
    
    def _log_security_event(self, event_type: str, user_id: Optional[str], 
                           ip_address: str, user_agent: str, 
                           details: Dict[str, Any], severity: str = "info"):
        """Log security event"""
        event = SecurityEvent(
            event_type=event_type,
            user_id=user_id,
            ip_address=ip_address,
            user_agent=user_agent,
            timestamp=datetime.now(timezone.utc),
            details=details,
            severity=severity
        )
        
        self.security_events.append(event)
        
        # Log to system logger
        log_message = f"Security Event: {event_type} - User: {user_id} - IP: {ip_address}"
        if severity == "info":
            logger.info(log_message)
        elif severity == "warning":
            logger.warning(log_message)
        elif severity == "error":
            logger.error(log_message)
        elif severity == "critical":
            logger.critical(log_message)
        
        # Keep only last 1000 events in memory
        if len(self.security_events) > 1000:
            self.security_events = self.security_events[-1000:]
    
    def get_security_events(self, limit: int = 100, severity: Optional[str] = None) -> List[SecurityEvent]:
        """Get recent security events"""
        events = self.security_events
        
        if severity:
            events = [event for event in events if event.severity == severity]
        
        return events[-limit:]
    
    def get_security_metrics(self) -> Dict[str, Any]:
        """Get security metrics"""
        current_time = datetime.now(timezone.utc)
        last_hour = current_time - timedelta(hours=1)
        last_day = current_time - timedelta(days=1)
        
        recent_events = [event for event in self.security_events if event.timestamp > last_hour]
        daily_events = [event for event in self.security_events if event.timestamp > last_day]
        
        return {
            "active_sessions": len(self.active_sessions),
            "total_users": len(self.users),
            "blacklisted_tokens": len(self.blacklisted_tokens),
            "events_last_hour": len(recent_events),
            "events_last_day": len(daily_events),
            "failed_logins_last_hour": len([e for e in recent_events if e.event_type == "login_failed"]),
            "rate_limit_violations_last_hour": len([e for e in recent_events if e.event_type == "rate_limit_exceeded"]),
            "locked_accounts": len([u for u in self.users.values() if u.locked_until and u.locked_until > current_time])
        }

# Global security service instance
security_service = SecurityService()
