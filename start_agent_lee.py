#!/usr/bin/env python3
"""
Agent Lee™ - Real AI Assistant <PERSON><PERSON>
Start the complete Agent Lee system with all functionality
"""

import os
import sys
import time
import subprocess
import webbrowser
from pathlib import Path

def find_available_port(start_port=8000, max_attempts=10):
    """Find an available port starting from start_port"""
    import socket
    
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    
    return None

def main():
    """Start Agent Lee Real AI Assistant"""
    print("🧠 AGENT LEE™ - REAL AI ASSISTANT")
    print("=" * 50)
    print("🤖 Starting the complete Agent Lee system...")
    print("✨ Features: Voice Control, App Opening, Conversations")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print(f"❌ Python 3.8+ required, found {sys.version_info[0]}.{sys.version_info[1]}")
        return False
    
    print(f"✅ Python {sys.version_info[0]}.{sys.version_info[1]}.{sys.version_info[2]}")
    
    # Check if backend exists
    backend_file = Path("backend/agentlee_controller_production.py")
    if not backend_file.exists():
        print("❌ Agent Lee backend not found")
        print("💡 Make sure you're in the Agent Lee directory")
        return False
    
    print("✅ Agent Lee backend found")
    
    # Check if Agent Lee interface exists
    interface_files = ["frontend/agentleecard.html", "agentleecard.html", "index.html", "echo_engine.html", "agent_lee_real.html", "index_production.html"]
    interface_found = False

    for interface_file in interface_files:
        if Path(interface_file).exists():
            print(f"✅ Agent Lee interface found: {interface_file}")
            interface_found = True
            break

    if not interface_found:
        print("❌ No Agent Lee interface found")
        return False
    
    # Find available port
    port = find_available_port(8000)
    if not port:
        print("❌ No available ports found")
        return False
    
    print(f"✅ Using port {port}")
    
    # Start the backend
    print("🚀 Starting Agent Lee backend...")
    
    try:
        cmd = [
            sys.executable, "-m", "uvicorn",
            "agentlee_controller_production:app",
            "--host", "localhost",
            "--port", str(port),
            "--log-level", "info"
        ]
        
        print(f"🔧 Command: {' '.join(cmd)}")
        print("🔧 Working directory: backend/")
        
        # Start the process
        process = subprocess.Popen(
            cmd,
            cwd="backend",
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        print("✅ Backend process started")
        print("⏳ Waiting for Agent Lee to initialize...")
        
        # Wait for startup
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is not None:
            print("❌ Backend process terminated")
            output = process.stdout.read()
            print(f"Error output: {output}")
            return False
        
        print("✅ Agent Lee is ready!")
        
        # Display success info
        print("\n" + "=" * 50)
        print("🧠 AGENT LEE™ - FULLY OPERATIONAL")
        print("=" * 50)
        print(f"🤖 Agent Lee Interface: http://localhost:{port}/app")
        print(f"🔧 API Endpoints: http://localhost:{port}")
        print(f"🏥 Health Check: http://localhost:{port}/api/health")
        print("=" * 50)
        print("🎯 FEATURES AVAILABLE:")
        print("  🗣️  Voice Control - Click microphone button")
        print("  💬 Chat Interface - Type messages to Agent Lee")
        print("  📱 App Control - Say 'open Chrome' or 'start Calculator'")
        print("  🌐 Web Search - Ask Agent Lee to search for anything")
        print("  🎤 Text-to-Speech - Agent Lee speaks responses")
        print("  🖱️  Draggable Interface - Drag the card around")
        print("=" * 50)
        print("💡 Try saying:")
        print("  • 'Hello Agent Lee'")
        print("  • 'Open Chrome'")
        print("  • 'Search for Python tutorials'")
        print("  • 'What can you do?'")
        print("=" * 50)
        print("💡 Press Ctrl+C to shutdown")
        print("=" * 50)
        
        # Try to open browser
        try:
            webbrowser.open(f"http://localhost:{port}/app")
            print("🌐 Browser opened - Agent Lee is ready to chat!")
        except:
            print("⚠️ Could not open browser automatically")
            print(f"🌐 Please open: http://localhost:{port}/app")
        
        # Monitor the process
        try:
            while True:
                # Check if process is still running
                if process.poll() is not None:
                    print("❌ Backend process terminated")
                    break
                
                # Read and display important output
                try:
                    line = process.stdout.readline()
                    if line:
                        # Show important messages
                        if any(keyword in line.lower() for keyword in ['error', 'warning', 'started', 'application startup complete']):
                            print(f"Agent Lee: {line.strip()}")
                except:
                    pass
                
                time.sleep(0.1)
                
        except KeyboardInterrupt:
            print("\n🛑 Shutdown signal received")
            
        # Cleanup
        print("🛑 Shutting down Agent Lee...")
        process.terminate()
        
        try:
            process.wait(timeout=5)
            print("✅ Agent Lee shutdown complete")
        except subprocess.TimeoutExpired:
            print("⚠️ Force killing process...")
            process.kill()
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to start Agent Lee: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 Thank you for using Agent Lee!")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Interrupted")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
