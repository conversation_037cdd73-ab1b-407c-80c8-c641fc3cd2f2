<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Lee™</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: transparent;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
        }

        /* Agent <PERSON> Card - Pure Floating Widget */
        .agent-card {
            position: fixed;
            top: 100px;
            right: 100px;
            width: 320px;
            height: 580px;
            background: linear-gradient(145deg, #1e293b 0%, #0f172a 100%);
            border-radius: 20px;
            box-shadow: 0 25px 80px rgba(0,0,0,0.8), 0 0 0 2px rgba(59, 130, 246, 0.3);
            border: 1px solid rgba(59, 130, 246, 0.2);
            z-index: 999999;
            cursor: move;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* Minimized State */
        .agent-card.minimized {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            cursor: grab;
        }

        .agent-card.minimized .card-content {
            display: none;
        }

        .agent-card.minimized .minimized-bubble {
            display: flex;
        }

        /* Minimized Bubble */
        .minimized-bubble {
            display: none;
            width: 100%;
            height: 100%;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            border-radius: 50%;
            position: relative;
        }

        .bubble-face {
            font-size: 28px;
            animation: blink 3s infinite;
        }

        .bubble-status {
            position: absolute;
            bottom: -3px;
            right: -3px;
            width: 16px;
            height: 16px;
            background: #10b981;
            border-radius: 50%;
            border: 2px solid #0f172a;
        }

        @keyframes blink {
            0%, 90%, 100% { transform: scale(1); }
            95% { transform: scale(0.9); }
        }

        /* Card Header */
        .card-header {
            background: linear-gradient(135deg, #1e40af 0%, #3730a3 50%, #374151 100%);
            padding: 12px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: grab;
            border-bottom: 1px solid rgba(59, 130, 246, 0.3);
        }

        .card-header:active {
            cursor: grabbing;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .agent-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.4), 0 0 0 2px rgba(59, 130, 246, 0.3);
            border: 2px solid rgba(255,255,255,0.2);
        }

        .agent-face {
            font-size: 18px;
            animation: blink 3s infinite;
        }

        .agent-info {
            color: white;
        }

        .agent-name {
            font-size: 13px;
            font-weight: bold;
            margin-bottom: 1px;
        }

        .agent-status {
            font-size: 9px;
            color: rgba(255,255,255,0.8);
        }

        /* Header Controls */
        .header-controls {
            display: flex;
            gap: 5px;
        }

        .control-btn {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            font-size: 9px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .spawn-btn { background: #10b981; }
        .minimize-btn { background: #fbbf24; }
        .close-btn { background: #ef4444; }

        .control-btn:hover {
            transform: scale(1.3);
        }

        /* Card Content */
        .card-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 12px;
            gap: 10px;
            overflow-y: auto;
        }

        /* Info Cards */
        .info-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .info-card {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 6px;
            padding: 8px;
            text-align: center;
            color: white;
            font-size: 10px;
        }

        .info-icon {
            font-size: 14px;
            margin-bottom: 3px;
        }

        .info-value {
            font-size: 11px;
            color: #3b82f6;
            font-weight: bold;
        }

        /* Chat Area */
        .chat-area {
            flex: 1;
            background: rgba(30, 41, 59, 0.6);
            border-radius: 8px;
            padding: 8px;
            overflow-y: auto;
            border: 1px solid rgba(59, 130, 246, 0.2);
            min-height: 120px;
            max-height: 160px;
        }

        .message {
            margin-bottom: 6px;
            padding: 5px 7px;
            border-radius: 5px;
            font-size: 10px;
            line-height: 1.3;
        }

        .agent-message {
            background: rgba(59, 130, 246, 0.2);
            color: #e2e8f0;
            border-left: 2px solid #3b82f6;
        }

        .user-message {
            background: rgba(16, 185, 129, 0.2);
            color: #e2e8f0;
            border-left: 2px solid #10b981;
            text-align: right;
        }

        /* Input Area */
        .input-area {
            display: flex;
            gap: 6px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 5px;
            padding: 6px;
            color: white;
            font-size: 10px;
            outline: none;
        }

        .message-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .input-btn {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            font-size: 12px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .voice-btn {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
        }

        .send-btn {
            background: linear-gradient(135deg, #3b82f6, #1e40af);
        }

        .input-btn:hover {
            transform: scale(1.1);
        }

        .input-btn.active {
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* Feature Buttons */
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 6px;
        }

        .feature-btn {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 6px;
            padding: 6px 3px;
            color: white;
            font-size: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 3px;
            text-align: center;
        }

        .feature-btn:hover {
            background: rgba(59, 130, 246, 0.3);
            transform: translateY(-1px);
        }

        .feature-icon {
            font-size: 12px;
        }

        /* Echo Engine */
        .echo-section {
            background: rgba(30, 41, 59, 0.6);
            border-radius: 6px;
            padding: 8px;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .echo-title {
            color: white;
            font-size: 10px;
            font-weight: bold;
            margin-bottom: 6px;
            text-align: center;
        }

        .device-tabs {
            display: flex;
            gap: 3px;
            margin-bottom: 6px;
            justify-content: center;
        }

        .device-tab {
            background: rgba(59, 130, 246, 0.2);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 3px;
            padding: 3px 6px;
            color: white;
            font-size: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .device-tab.active {
            background: rgba(59, 130, 246, 0.5);
        }

        .app-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 4px;
        }

        .app-tile {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 4px;
            padding: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;
            text-align: center;
        }

        .app-tile:hover {
            background: rgba(59, 130, 246, 0.3);
            transform: translateY(-1px);
        }

        .app-icon {
            font-size: 10px;
        }

        .app-name {
            font-size: 7px;
            color: white;
            font-weight: 500;
        }

        /* Status Bar */
        .status-bar {
            background: rgba(15, 23, 42, 0.8);
            padding: 4px 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 8px;
            color: rgba(255, 255, 255, 0.7);
            border-top: 1px solid rgba(59, 130, 246, 0.2);
        }

        .listening-indicator {
            display: none;
            color: #ef4444;
            animation: pulse 1s infinite;
        }

        .listening-indicator.active {
            display: inline;
        }

        /* Spawned Echo Cards */
        .echo-card {
            position: fixed;
            width: 350px;
            height: 250px;
            background: linear-gradient(145deg, #1e293b 0%, #0f172a 100%);
            border-radius: 12px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.8), 0 0 0 2px rgba(59, 130, 246, 0.3);
            border: 1px solid rgba(59, 130, 246, 0.2);
            z-index: 999998;
            cursor: move;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .echo-header {
            background: linear-gradient(135deg, #1e40af 0%, #3730a3 50%, #374151 100%);
            padding: 8px 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: grab;
            border-bottom: 1px solid rgba(59, 130, 246, 0.3);
        }

        .echo-title {
            color: white;
            font-size: 11px;
            font-weight: bold;
        }

        .echo-content {
            flex: 1;
            padding: 12px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <!-- Agent Lee Floating Card -->
    <div class="agent-card" id="agentCard">
        <!-- Minimized Bubble -->
        <div class="minimized-bubble" id="minimizedBubble">
            <div class="bubble-face">🤖</div>
            <div class="bubble-status"></div>
        </div>

        <!-- Full Card Content -->
        <div class="card-content" id="cardContent">
            <!-- Header -->
            <div class="card-header" id="cardHeader">
                <div class="header-left">
                    <div class="agent-avatar">
                        <div class="agent-face" id="agentFace">🤖</div>
                    </div>
                    <div class="agent-info">
                        <div class="agent-name">Agent Lee™</div>
                        <div class="agent-status" id="agentStatus">Ready</div>
                    </div>
                </div>
                
                <div class="header-controls">
                    <button class="control-btn spawn-btn" id="spawnBtn" title="Spawn Echo">+</button>
                    <button class="control-btn minimize-btn" id="minimizeBtn" title="Minimize">−</button>
                    <button class="control-btn close-btn" id="closeBtn" title="Close">×</button>
                </div>
            </div>

            <!-- Info Cards -->
            <div class="info-row">
                <div class="info-card">
                    <div class="info-icon">🌤️</div>
                    <div class="info-value" id="weatherInfo">72°F</div>
                </div>
                <div class="info-card">
                    <div class="info-icon">🚗</div>
                    <div class="info-value" id="trafficInfo">Light</div>
                </div>
            </div>

            <!-- Chat Area -->
            <div class="chat-area" id="chatArea">
                <div class="message agent-message">
                    Hello! I'm Agent Lee. I can open apps, create Echoes, and help with tasks. What would you like me to do?
                </div>
            </div>

            <!-- Input Area -->
            <div class="input-area">
                <input type="text" class="message-input" id="messageInput" placeholder="Type or speak to Agent Lee...">
                <button class="input-btn voice-btn" id="voiceBtn" title="Voice">🎤</button>
                <button class="input-btn send-btn" id="sendBtn" title="Send">➤</button>
            </div>

            <!-- Feature Buttons -->
            <div class="feature-grid">
                <button class="feature-btn" id="appsBtn">
                    <span class="feature-icon">📱</span>
                    <span>Apps</span>
                </button>
                <button class="feature-btn" id="webBtn">
                    <span class="feature-icon">🌐</span>
                    <span>Web</span>
                </button>
                <button class="feature-btn" id="emailBtn">
                    <span class="feature-icon">📧</span>
                    <span>Email</span>
                </button>
                <button class="feature-btn" id="newsBtn">
                    <span class="feature-icon">📰</span>
                    <span>News</span>
                </button>
            </div>

            <!-- Echo Engine -->
            <div class="echo-section">
                <div class="echo-title">🔄 Echo Engine</div>
                
                <div class="device-tabs">
                    <div class="device-tab active" data-device="computer">💻</div>
                    <div class="device-tab" data-device="phone">📱</div>
                    <div class="device-tab" data-device="tablet">📱</div>
                    <div class="device-tab" data-device="laptop">💻</div>
                </div>
                
                <div class="app-grid" id="appGrid">
                    <!-- Apps populated by JavaScript -->
                </div>
            </div>

            <!-- Status Bar -->
            <div class="status-bar">
                <span id="connectionStatus">🟢 Ready</span>
                <span class="listening-indicator" id="listeningIndicator">🎤</span>
                <span id="timeStatus"></span>
            </div>
        </div>
    </div>

    <script src="agent_lee_real_widget.js"></script>
</body>
</html>
