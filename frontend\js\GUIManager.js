/**
 * Agent Lee™ GUI Manager
 * Responsible for spawning new, styled, floating "Echo Cards" when an app echo is initiated
 */

class GUIManager {
    constructor() {
        this.activeGUIs = new Map();
        this.guiCounter = 0;
        this.isElectron = typeof window.electronAPI !== 'undefined';
        this.initialized = false;
    }

    async initialize() {
        console.log('🎨 Initializing GUI Manager...');
        
        try {
            // Set up GUI management
            this.setupGUIEventHandlers();
            
            // Initialize Electron bridge if available
            if (this.isElectron) {
                await this.initializeElectronBridge();
            }
            
            this.initialized = true;
            console.log('✅ GUI Manager initialized successfully');
            
        } catch (error) {
            console.error('❌ GUI Manager initialization failed:', error);
            throw error;
        }
    }

    async initializeElectronBridge() {
        if (this.isElectron && window.electronAPI) {
            // Set up IPC handlers for GUI management
            window.electronAPI.onGUICreated((guiData) => {
                this.handleElectronGUICreated(guiData);
            });

            window.electronAPI.onGUIClosed((guiId) => {
                this.handleElectronGUIClosed(guiId);
            });
        }
    }

    setupGUIEventHandlers() {
        // Listen for GUI creation requests
        document.addEventListener('createEchoGUI', (event) => {
            this.createGUI(event.detail);
        });

        // Listen for GUI close requests
        document.addEventListener('closeEchoGUI', (event) => {
            this.closeGUI(event.detail.guiId);
        });
    }

    /**
     * Create a new floating GUI card for an Echo
     * @param {Object} options - GUI creation options
     * @param {string} options.appName - Name of the application
     * @param {string} options.appId - Unique identifier for the app
     * @param {string} options.deviceType - Type of device (computer, phone, tablet, laptop)
     * @param {Object} options.windowOptions - Window configuration options
     */
    async createGUI(options) {
        try {
            const guiId = `echo_gui_${++this.guiCounter}`;
            
            console.log(`🎨 Creating Echo GUI for ${options.appName}...`);

            const guiConfig = {
                id: guiId,
                title: `Echo: ${options.appName}`,
                appName: options.appName,
                appId: options.appId,
                deviceType: options.deviceType,
                windowOptions: {
                    width: 800,
                    height: 600,
                    resizable: true,
                    frame: false,
                    transparent: true,
                    alwaysOnTop: false,
                    ...options.windowOptions
                }
            };

            if (this.isElectron) {
                // Create Electron window
                const electronGUI = await this.createElectronGUI(guiConfig);
                this.activeGUIs.set(guiId, electronGUI);
            } else {
                // Create web-based floating window
                const webGUI = await this.createWebGUI(guiConfig);
                this.activeGUIs.set(guiId, webGUI);
            }

            console.log(`✅ Echo GUI created: ${guiId}`);
            
            // Notify other components
            this.notifyGUICreated(guiConfig);
            
            return guiId;

        } catch (error) {
            console.error('❌ Failed to create GUI:', error);
            throw error;
        }
    }

    async createElectronGUI(config) {
        if (!this.isElectron) {
            throw new Error('Electron not available');
        }

        try {
            // Request Electron to create a new window
            const electronWindowId = await window.electronAPI.createEchoWindow({
                title: config.title,
                width: config.windowOptions.width,
                height: config.windowOptions.height,
                resizable: config.windowOptions.resizable,
                frame: config.windowOptions.frame,
                transparent: config.windowOptions.transparent,
                alwaysOnTop: config.windowOptions.alwaysOnTop,
                appId: config.appId,
                appName: config.appName,
                deviceType: config.deviceType
            });

            return {
                id: config.id,
                electronWindowId: electronWindowId,
                type: 'electron',
                config: config
            };

        } catch (error) {
            console.error('❌ Failed to create Electron GUI:', error);
            throw error;
        }
    }

    async createWebGUI(config) {
        try {
            // Create floating div-based window for web
            const guiElement = this.createFloatingWindow(config);
            document.body.appendChild(guiElement);

            // Initialize drag functionality
            this.initializeWebGUIDrag(guiElement);

            // Initialize resize functionality
            this.initializeWebGUIResize(guiElement);

            return {
                id: config.id,
                element: guiElement,
                type: 'web',
                config: config
            };

        } catch (error) {
            console.error('❌ Failed to create Web GUI:', error);
            throw error;
        }
    }

    createFloatingWindow(config) {
        const windowElement = document.createElement('div');
        windowElement.className = 'echo-gui-window';
        windowElement.id = config.id;
        
        windowElement.innerHTML = `
            <div class="echo-gui-header" data-gui-id="${config.id}">
                <div class="echo-gui-title">
                    <span class="echo-gui-icon">${this.getDeviceIcon(config.deviceType)}</span>
                    <span class="echo-gui-name">${config.title}</span>
                </div>
                <div class="echo-gui-controls">
                    <button class="echo-gui-btn minimize-btn" data-action="minimize" data-gui-id="${config.id}">−</button>
                    <button class="echo-gui-btn maximize-btn" data-action="maximize" data-gui-id="${config.id}">□</button>
                    <button class="echo-gui-btn close-btn" data-action="close" data-gui-id="${config.id}">×</button>
                </div>
            </div>
            <div class="echo-gui-content" id="${config.id}_content">
                <div class="echo-loading">
                    <div class="echo-spinner"></div>
                    <div>Connecting to ${config.appName}...</div>
                </div>
            </div>
            <div class="echo-gui-resize-handle"></div>
        `;

        // Apply styles
        this.applyGUIStyles(windowElement, config);

        // Add event listeners
        this.setupGUIEventListeners(windowElement, config);

        return windowElement;
    }

    applyGUIStyles(element, config) {
        const styles = `
            position: fixed;
            top: 100px;
            left: 100px;
            width: ${config.windowOptions.width}px;
            height: ${config.windowOptions.height}px;
            background: linear-gradient(145deg, #1e293b 0%, #0f172a 100%);
            border-radius: 12px;
            box-shadow: 0 25px 80px rgba(0,0,0,0.8), 0 0 0 2px rgba(59, 130, 246, 0.3);
            border: 1px solid rgba(59, 130, 246, 0.2);
            z-index: 1000;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        `;

        element.style.cssText = styles;

        // Add CSS for GUI components if not already added
        if (!document.getElementById('echo-gui-styles')) {
            this.addGUIStyles();
        }
    }

    addGUIStyles() {
        const styleSheet = document.createElement('style');
        styleSheet.id = 'echo-gui-styles';
        styleSheet.textContent = `
            .echo-gui-header {
                background: linear-gradient(135deg, #1e40af 0%, #3730a3 50%, #374151 100%);
                padding: 12px 15px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                cursor: grab;
                border-bottom: 1px solid rgba(59, 130, 246, 0.3);
            }

            .echo-gui-header:active {
                cursor: grabbing;
            }

            .echo-gui-title {
                display: flex;
                align-items: center;
                gap: 8px;
                color: white;
                font-size: 14px;
                font-weight: 600;
            }

            .echo-gui-icon {
                font-size: 16px;
            }

            .echo-gui-controls {
                display: flex;
                gap: 6px;
            }

            .echo-gui-btn {
                width: 18px;
                height: 18px;
                border-radius: 50%;
                border: none;
                cursor: pointer;
                font-size: 10px;
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.2s ease;
            }

            .minimize-btn { background: #fbbf24; }
            .maximize-btn { background: #10b981; }
            .close-btn { background: #ef4444; }

            .echo-gui-btn:hover {
                transform: scale(1.2);
            }

            .echo-gui-content {
                flex: 1;
                position: relative;
                overflow: hidden;
            }

            .echo-loading {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                text-align: center;
                color: white;
            }

            .echo-spinner {
                width: 30px;
                height: 30px;
                border: 3px solid rgba(255,255,255,0.3);
                border-top: 3px solid #3b82f6;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 10px;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .echo-gui-resize-handle {
                position: absolute;
                bottom: 0;
                right: 0;
                width: 15px;
                height: 15px;
                cursor: se-resize;
                background: linear-gradient(135deg, transparent 0%, rgba(59, 130, 246, 0.3) 100%);
            }
        `;

        document.head.appendChild(styleSheet);
    }

    setupGUIEventListeners(element, config) {
        // Window control buttons
        element.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            const guiId = e.target.dataset.guiId;

            if (action && guiId) {
                this.handleGUIAction(action, guiId);
            }
        });
    }

    handleGUIAction(action, guiId) {
        const gui = this.activeGUIs.get(guiId);
        if (!gui) return;

        switch (action) {
            case 'minimize':
                this.minimizeGUI(guiId);
                break;
            case 'maximize':
                this.maximizeGUI(guiId);
                break;
            case 'close':
                this.closeGUI(guiId);
                break;
        }
    }

    minimizeGUI(guiId) {
        const gui = this.activeGUIs.get(guiId);
        if (!gui) return;

        if (gui.type === 'web') {
            gui.element.style.height = '40px';
            gui.element.style.overflow = 'hidden';
        } else if (gui.type === 'electron') {
            window.electronAPI.minimizeEchoWindow(gui.electronWindowId);
        }
    }

    maximizeGUI(guiId) {
        const gui = this.activeGUIs.get(guiId);
        if (!gui) return;

        if (gui.type === 'web') {
            gui.element.style.height = gui.config.windowOptions.height + 'px';
            gui.element.style.overflow = 'hidden';
        } else if (gui.type === 'electron') {
            window.electronAPI.maximizeEchoWindow(gui.electronWindowId);
        }
    }

    closeGUI(guiId) {
        const gui = this.activeGUIs.get(guiId);
        if (!gui) return;

        if (confirm(`Close ${gui.config.appName} Echo?`)) {
            if (gui.type === 'web') {
                gui.element.remove();
            } else if (gui.type === 'electron') {
                window.electronAPI.closeEchoWindow(gui.electronWindowId);
            }

            this.activeGUIs.delete(guiId);
            this.notifyGUIClosed(guiId);
        }
    }

    initializeWebGUIDrag(element) {
        const header = element.querySelector('.echo-gui-header');
        let isDragging = false;
        let currentX = 0;
        let currentY = 0;
        let initialX = 0;
        let initialY = 0;

        header.addEventListener('mousedown', (e) => {
            initialX = e.clientX - currentX;
            initialY = e.clientY - currentY;
            isDragging = true;
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                e.preventDefault();
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;
                
                element.style.left = currentX + 'px';
                element.style.top = currentY + 'px';
            }
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
        });
    }

    initializeWebGUIResize(element) {
        const resizeHandle = element.querySelector('.echo-gui-resize-handle');
        let isResizing = false;

        resizeHandle.addEventListener('mousedown', (e) => {
            isResizing = true;
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (isResizing) {
                const rect = element.getBoundingClientRect();
                const newWidth = e.clientX - rect.left;
                const newHeight = e.clientY - rect.top;

                if (newWidth > 300) element.style.width = newWidth + 'px';
                if (newHeight > 200) element.style.height = newHeight + 'px';
            }
        });

        document.addEventListener('mouseup', () => {
            isResizing = false;
        });
    }

    getDeviceIcon(deviceType) {
        const icons = {
            computer: '🖥️',
            phone: '📱',
            tablet: '📱',
            laptop: '💻'
        };
        return icons[deviceType] || '🖥️';
    }

    notifyGUICreated(config) {
        const event = new CustomEvent('echoGUICreated', {
            detail: config
        });
        document.dispatchEvent(event);
    }

    notifyGUIClosed(guiId) {
        const event = new CustomEvent('echoGUIClosed', {
            detail: { guiId }
        });
        document.dispatchEvent(event);
    }

    handleElectronGUICreated(guiData) {
        console.log('🎨 Electron GUI created:', guiData);
    }

    handleElectronGUIClosed(guiId) {
        console.log('🎨 Electron GUI closed:', guiId);
        this.activeGUIs.delete(guiId);
    }

    getActiveGUIs() {
        return Array.from(this.activeGUIs.values());
    }

    getGUI(guiId) {
        return this.activeGUIs.get(guiId);
    }
}

// Create global instance
window.GUIManager = new GUIManager();
