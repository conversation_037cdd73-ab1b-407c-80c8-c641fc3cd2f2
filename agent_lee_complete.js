/**
 * Agent Lee™ Complete Implementation
 * Full-featured AI assistant with voice control, app management, and Echo Engine
 */

class AgentLeeComplete {
    constructor() {
        this.isListening = false;
        this.isTyping = false;
        this.conversationHistory = [];
        this.apiUrl = 'http://localhost:8000';
        this.currentDevice = 'computer';
        this.apps = {
            computer: [],
            phone: [],
            tablet: [],
            laptop: []
        };
        
        this.initializeElements();
        this.initializeEventListeners();
        this.initializeDragFunctionality();
        this.startStatusUpdates();
        this.loadApplications();
        this.updateWeatherAndTraffic();
        this.greetUser();
    }

    initializeElements() {
        this.elements = {
            card: document.getElementById('agentCard'),
            header: document.getElementById('cardHeader'),
            face: document.getElementById('agentFace'),
            status: document.getElementById('agentStatus'),
            conversation: document.getElementById('conversationArea'),
            input: document.getElementById('messageInput'),
            sendBtn: document.getElementById('sendBtn'),
            voiceBtn: document.getElementById('voiceBtn'),
            minimizeBtn: document.getElementById('minimizeBtn'),
            closeBtn: document.getElementById('closeBtn'),
            listeningIndicator: document.getElementById('listeningIndicator'),
            connectionStatus: document.getElementById('connectionStatus'),
            timeStatus: document.getElementById('timeStatus'),
            weatherInfo: document.getElementById('weatherInfo'),
            trafficInfo: document.getElementById('trafficInfo'),
            appGrid: document.getElementById('appGrid'),
            voiceStatus: document.getElementById('voiceStatus')
        };
    }

    initializeEventListeners() {
        // Send message
        this.elements.sendBtn.addEventListener('click', () => this.sendMessage());
        this.elements.input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.sendMessage();
        });

        // Voice control
        this.elements.voiceBtn.addEventListener('click', () => this.toggleVoiceInput());

        // Window controls
        this.elements.minimizeBtn.addEventListener('click', () => this.minimizeCard());
        this.elements.closeBtn.addEventListener('click', () => this.closeCard());

        // Feature buttons
        document.getElementById('appsBtn').addEventListener('click', () => this.handleApps());
        document.getElementById('webBtn').addEventListener('click', () => this.handleWeb());
        document.getElementById('emailBtn').addEventListener('click', () => this.handleEmail());
        document.getElementById('weatherBtn').addEventListener('click', () => this.handleWeather());
        document.getElementById('trafficBtn').addEventListener('click', () => this.handleTraffic());
        document.getElementById('newsBtn').addEventListener('click', () => this.handleNews());
        document.getElementById('calendarBtn').addEventListener('click', () => this.handleCalendar());
        document.getElementById('settingsBtn').addEventListener('click', () => this.handleSettings());

        // Device tabs
        document.querySelectorAll('.device-tab').forEach(tab => {
            tab.addEventListener('click', () => this.switchDevice(tab.dataset.device));
        });
    }

    initializeDragFunctionality() {
        let isDragging = false;
        let currentX = 0;
        let currentY = 0;
        let initialX = 0;
        let initialY = 0;

        this.elements.header.addEventListener('mousedown', (e) => {
            initialX = e.clientX - currentX;
            initialY = e.clientY - currentY;
            isDragging = true;
            this.elements.card.style.cursor = 'grabbing';
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                e.preventDefault();
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;
                
                this.elements.card.style.transform = `translate(${currentX}px, ${currentY}px)`;
            }
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
            this.elements.card.style.cursor = 'move';
        });
    }

    async greetUser() {
        await this.sleep(1000);
        this.speak("Hello! I'm Agent Lee, your complete AI assistant. I can help you with applications, weather, traffic, and much more. What would you like me to do?");
        this.updateStatus("Ready and listening");
    }

    async sendMessage() {
        const message = this.elements.input.value.trim();
        if (!message) return;

        // Add user message
        this.addMessage(message, 'user');
        this.elements.input.value = '';

        // Show typing indicator
        this.showTyping(true);

        try {
            // Send to backend
            const response = await this.sendToBackend(message);
            
            // Hide typing indicator
            this.showTyping(false);
            
            // Add agent response
            this.addMessage(response.message, 'agent');
            
            // Speak response
            if (response.speak) {
                this.speak(response.message);
            }

            // Handle actions
            if (response.action) {
                await this.handleAction(response.action);
            }

        } catch (error) {
            this.showTyping(false);
            this.addMessage("I'm sorry, I'm having trouble connecting right now. Please try again.", 'agent');
            console.error('Error sending message:', error);
        }
    }

    async sendToBackend(message) {
        try {
            const response = await fetch(`${this.apiUrl}/api/chat`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    history: this.conversationHistory
                })
            });

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            return await response.json();
        } catch (error) {
            // Fallback response
            return {
                message: this.generateFallbackResponse(message),
                speak: true,
                action: this.detectAction(message)
            };
        }
    }

    generateFallbackResponse(message) {
        const lowerMessage = message.toLowerCase();
        
        if (lowerMessage.includes('weather')) {
            return "I can help you check the weather! The current conditions are shown in the weather card above.";
        } else if (lowerMessage.includes('traffic')) {
            return "I can check traffic conditions for you! The current traffic status is displayed in the traffic card.";
        } else if (lowerMessage.includes('open') || lowerMessage.includes('launch')) {
            return "I can help you open applications! You can see available apps in the Echo Engine section below, or tell me which specific app you'd like to open.";
        } else if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
            return "Hello! I'm Agent Lee, your AI assistant. I can help you with applications, weather, traffic, news, and much more!";
        } else {
            return "I understand you want help with that. I can assist you with applications, weather, traffic, news, and many other tasks. What specifically would you like me to help you with?";
        }
    }

    detectAction(message) {
        const lowerMessage = message.toLowerCase();
        
        if (lowerMessage.includes('open') && (lowerMessage.includes('chrome') || lowerMessage.includes('browser'))) {
            return { type: 'open_app', app: 'chrome' };
        } else if (lowerMessage.includes('weather')) {
            return { type: 'update_weather' };
        } else if (lowerMessage.includes('traffic')) {
            return { type: 'update_traffic' };
        }
        
        return null;
    }

    async handleAction(action) {
        switch (action.type) {
            case 'open_app':
                await this.openApplication(action.app);
                break;
            case 'update_weather':
                await this.updateWeatherAndTraffic();
                break;
            case 'update_traffic':
                await this.updateWeatherAndTraffic();
                break;
        }
    }

    async openApplication(appName) {
        try {
            const response = await fetch(`${this.apiUrl}/api/open_app`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ app: appName })
            });

            const result = await response.json();
            
            if (result.status === 'success') {
                this.addMessage(`✅ ${appName} opened successfully!`, 'agent');
                this.speak(`${appName} is now open`);
            } else {
                this.addMessage(`❌ Could not open ${appName}: ${result.message}`, 'agent');
            }
        } catch (error) {
            this.addMessage(`❌ Error opening ${appName}: ${error.message}`, 'agent');
        }
    }

    toggleVoiceInput() {
        if (this.isListening) {
            this.stopListening();
        } else {
            this.startListening();
        }
    }

    startListening() {
        this.isListening = true;
        this.elements.listeningIndicator.classList.add('active');
        this.elements.voiceBtn.classList.add('active');
        this.elements.voiceStatus.textContent = 'Listening...';

        // Implement speech recognition
        if ('webkitSpeechRecognition' in window) {
            const recognition = new webkitSpeechRecognition();
            recognition.continuous = false;
            recognition.interimResults = false;
            recognition.lang = 'en-US';

            recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                this.elements.input.value = transcript;
                this.sendMessage();
            };

            recognition.onerror = () => {
                this.stopListening();
                this.addMessage("Sorry, I couldn't hear you clearly. Please try again.", 'agent');
            };

            recognition.onend = () => {
                this.stopListening();
            };

            recognition.start();
        } else {
            this.addMessage("Voice recognition is not supported in this browser.", 'agent');
            this.stopListening();
        }
    }

    stopListening() {
        this.isListening = false;
        this.elements.listeningIndicator.classList.remove('active');
        this.elements.voiceBtn.classList.remove('active');
        this.elements.voiceStatus.textContent = 'Voice Ready';
    }

    async speak(text) {
        try {
            await fetch(`${this.apiUrl}/api/speak`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ text: text })
            });
        } catch (error) {
            // Fallback to browser speech synthesis
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(text);
                utterance.rate = 0.9;
                utterance.pitch = 1;
                speechSynthesis.speak(utterance);
            }
        }
    }

    addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        messageDiv.textContent = text;
        
        this.elements.conversation.appendChild(messageDiv);
        this.elements.conversation.scrollTop = this.elements.conversation.scrollHeight;

        // Store in history
        this.conversationHistory.push({ sender, text, timestamp: Date.now() });
        
        // Keep only last 20 messages
        if (this.conversationHistory.length > 20) {
            this.conversationHistory = this.conversationHistory.slice(-20);
        }
    }

    showTyping(show) {
        this.isTyping = show;
        // Could add typing indicator here
    }

    updateStatus(status) {
        this.elements.status.textContent = status;
    }

    async loadApplications() {
        try {
            // Load computer applications
            const response = await fetch(`${this.apiUrl}/api/scan_apps`);
            const data = await response.json();
            
            if (data.status === 'success') {
                this.apps.computer = data.apps;
            } else {
                this.apps.computer = this.getMockApps('computer');
            }
        } catch (error) {
            this.apps.computer = this.getMockApps('computer');
        }

        // Load mock data for other devices
        this.apps.phone = this.getMockApps('phone');
        this.apps.tablet = this.getMockApps('tablet');
        this.apps.laptop = this.getMockApps('laptop');

        this.updateAppGrid();
    }

    getMockApps(deviceType) {
        const mockApps = {
            computer: [
                { id: 'chrome', name: 'Chrome', icon: '🌐' },
                { id: 'notepad', name: 'Notepad', icon: '📝' },
                { id: 'calculator', name: 'Calculator', icon: '🧮' },
                { id: 'paint', name: 'Paint', icon: '🎨' },
                { id: 'word', name: 'Word', icon: '📄' },
                { id: 'excel', name: 'Excel', icon: '📊' }
            ],
            phone: [
                { id: 'whatsapp', name: 'WhatsApp', icon: '💬' },
                { id: 'instagram', name: 'Instagram', icon: '📷' },
                { id: 'youtube', name: 'YouTube', icon: '📺' },
                { id: 'gmail', name: 'Gmail', icon: '📧' },
                { id: 'maps', name: 'Maps', icon: '🗺️' },
                { id: 'camera', name: 'Camera', icon: '📸' }
            ],
            tablet: [
                { id: 'netflix', name: 'Netflix', icon: '🎬' },
                { id: 'kindle', name: 'Kindle', icon: '📚' },
                { id: 'procreate', name: 'Procreate', icon: '🎨' },
                { id: 'notes', name: 'Notes', icon: '📝' }
            ],
            laptop: [
                { id: 'firefox', name: 'Firefox', icon: '🦊' },
                { id: 'photoshop', name: 'Photoshop', icon: '🎨' },
                { id: 'teams', name: 'Teams', icon: '👥' },
                { id: 'outlook', name: 'Outlook', icon: '📧' }
            ]
        };

        return mockApps[deviceType] || [];
    }

    switchDevice(deviceType) {
        this.currentDevice = deviceType;
        
        // Update active tab
        document.querySelectorAll('.device-tab').forEach(tab => {
            tab.classList.toggle('active', tab.dataset.device === deviceType);
        });

        this.updateAppGrid();
    }

    updateAppGrid() {
        const apps = this.apps[this.currentDevice] || [];
        this.elements.appGrid.innerHTML = '';

        apps.forEach(app => {
            const appTile = document.createElement('div');
            appTile.className = 'app-tile';
            appTile.innerHTML = `
                <div class="app-icon">${app.icon}</div>
                <div class="app-name">${app.name}</div>
            `;
            
            appTile.addEventListener('click', () => {
                this.handleAppClick(app);
            });

            this.elements.appGrid.appendChild(appTile);
        });
    }

    handleAppClick(app) {
        if (this.currentDevice === 'computer') {
            this.openApplication(app.id);
        } else {
            this.addMessage(`🔄 Creating Echo for ${app.name} on ${this.currentDevice}...`, 'agent');
            this.speak(`Creating Echo for ${app.name}`);
            
            // Simulate Echo creation
            setTimeout(() => {
                this.addMessage(`✅ Echo created! ${app.name} is now mirrored from your ${this.currentDevice}.`, 'agent');
            }, 2000);
        }
    }

    async updateWeatherAndTraffic() {
        // Mock weather data
        const weatherConditions = ['Sunny 72°F', 'Cloudy 68°F', 'Rainy 65°F', 'Partly Cloudy 70°F'];
        const trafficConditions = ['Light Traffic', 'Moderate Traffic', 'Heavy Traffic', 'Clear Roads'];
        
        this.elements.weatherInfo.textContent = weatherConditions[Math.floor(Math.random() * weatherConditions.length)];
        this.elements.trafficInfo.textContent = trafficConditions[Math.floor(Math.random() * trafficConditions.length)];
    }

    startStatusUpdates() {
        setInterval(() => {
            const now = new Date();
            this.elements.timeStatus.textContent = now.toLocaleTimeString();
        }, 1000);

        // Update weather and traffic every 5 minutes
        setInterval(() => {
            this.updateWeatherAndTraffic();
        }, 300000);
    }

    // Feature button handlers
    handleApps() {
        this.elements.input.value = "Show me available applications";
        this.sendMessage();
    }

    handleWeb() {
        this.elements.input.value = "Search the web for ";
        this.elements.input.focus();
        this.elements.input.setSelectionRange(this.elements.input.value.length, this.elements.input.value.length);
    }

    handleEmail() {
        this.elements.input.value = "Open my email";
        this.sendMessage();
    }

    handleWeather() {
        this.elements.input.value = "What's the weather like?";
        this.sendMessage();
    }

    handleTraffic() {
        this.elements.input.value = "How's the traffic?";
        this.sendMessage();
    }

    handleNews() {
        this.elements.input.value = "Show me the latest news";
        this.sendMessage();
    }

    handleCalendar() {
        this.elements.input.value = "What's on my calendar?";
        this.sendMessage();
    }

    handleSettings() {
        this.elements.input.value = "Open settings";
        this.sendMessage();
    }

    minimizeCard() {
        this.elements.card.style.height = '80px';
        this.elements.card.style.overflow = 'hidden';
    }

    closeCard() {
        if (confirm('Close Agent Lee?')) {
            this.elements.card.style.display = 'none';
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize Agent Lee when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.agentLee = new AgentLeeComplete();
});
