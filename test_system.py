#!/usr/bin/env python3
"""
Agent Lee™ System Test Script
Quick verification that all components work correctly
"""

import sys
import time
import subprocess
from pathlib import Path

def test_python_version():
    """Test Python version"""
    print("🐍 Testing Python version...")
    version = sys.version_info
    if version >= (3, 8):
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - OK")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - Need 3.8+")
        return False

def test_imports():
    """Test essential imports"""
    print("📦 Testing essential imports...")
    
    essential_modules = [
        ('fastapi', 'FastAPI framework'),
        ('uvicorn', 'ASGI server'),
        ('pydantic', 'Data validation'),
        ('psutil', 'System monitoring'),
        ('requests', 'HTTP client'),
        ('dotenv', 'Environment variables')
    ]
    
    optional_modules = [
        ('pyttsx3', 'Text-to-speech'),
        ('google.generativeai', 'Google Gemini AI'),
        ('sqlalchemy', 'Database ORM'),
        ('networkx', 'Graph processing')
    ]
    
    all_good = True
    
    # Test essential modules
    for module, description in essential_modules:
        try:
            __import__(module)
            print(f"✅ {module} - {description}")
        except ImportError:
            print(f"❌ {module} - {description} (REQUIRED)")
            all_good = False
    
    # Test optional modules
    for module, description in optional_modules:
        try:
            __import__(module)
            print(f"✅ {module} - {description}")
        except ImportError:
            print(f"⚠️ {module} - {description} (optional)")
    
    return all_good

def test_file_structure():
    """Test file structure"""
    print("📁 Testing file structure...")
    
    required_files = [
        ("backend/agentlee_controller_production.py", "Production controller"),
        ("index_production.html", "Production frontend"),
        ("start_production.py", "Production startup script"),
        ("backend/requirements_minimal.txt", "Minimal requirements")
    ]
    
    optional_files = [
        ("backend/agentlee_controller_v2.py", "Enhanced controller"),
        ("backend/config.py", "Configuration"),
        ("backend/models.py", "Database models"),
        ("backend/llm_service.py", "LLM service"),
        (".env", "Environment variables")
    ]
    
    all_good = True
    
    # Test required files
    for file_path, description in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path} - {description}")
        else:
            print(f"❌ {file_path} - {description} (REQUIRED)")
            all_good = False
    
    # Test optional files
    for file_path, description in optional_files:
        if Path(file_path).exists():
            print(f"✅ {file_path} - {description}")
        else:
            print(f"⚠️ {file_path} - {description} (optional)")
    
    return all_good

def test_backend_syntax():
    """Test backend syntax"""
    print("🔍 Testing backend syntax...")
    
    controllers = [
        "backend/agentlee_controller_production.py",
        "backend/agentlee_controller_v2.py"
    ]
    
    for controller in controllers:
        if Path(controller).exists():
            try:
                with open(controller, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                compile(content, controller, 'exec')
                print(f"✅ {controller} - Syntax OK")
                return True
                
            except SyntaxError as e:
                print(f"❌ {controller} - Syntax Error: {e}")
                return False
            except Exception as e:
                print(f"⚠️ {controller} - Could not check: {e}")
    
    print("❌ No valid controller found")
    return False

def test_quick_startup():
    """Test quick startup"""
    print("🚀 Testing quick startup...")
    
    try:
        # Try to start the system for a few seconds
        cmd = [sys.executable, "start_simple.py"]
        
        print("   Starting system...")
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        # Wait a few seconds
        time.sleep(5)
        
        # Check if it's still running
        if process.poll() is None:
            print("✅ System started successfully")
            
            # Terminate the test
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            
            return True
        else:
            print("❌ System failed to start")
            # Read any error output
            output = process.stdout.read()
            if output:
                print(f"   Error output: {output[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Startup test failed: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("🧠 AGENT LEE™ SYSTEM TEST")
    print("=" * 50)
    
    tests = [
        ("Python Version", test_python_version),
        ("Essential Imports", test_imports),
        ("File Structure", test_file_structure),
        ("Backend Syntax", test_backend_syntax),
        ("Quick Startup", test_quick_startup)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print("-" * 50)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! System is ready for production.")
        print("\n💡 To start the system, run:")
        print("   python start_production.py")
    else:
        print("⚠️ Some tests failed. Please fix issues before starting.")
        print("\n💡 To install missing dependencies, run:")
        print("   pip install -r backend/requirements_minimal.txt")
    
    print("=" * 50)
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
