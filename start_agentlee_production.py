#!/usr/bin/env python3
"""
Agent Lee™ Production Startup Script
Comprehensive production-ready startup with cognitive architecture initialization
"""

import os
import sys
import time
import asyncio
import logging
import subprocess
import platform
import webbrowser
from pathlib import Path
from datetime import datetime

# Add backend to path
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("agentlee_startup.log")
    ]
)

logger = logging.getLogger("agentlee.startup")

class AgentLeeProductionLauncher:
    """Production launcher for Agent Lee with cognitive architecture"""
    
    def __init__(self):
        self.start_time = time.time()
        self.backend_process = None
        self.frontend_url = "http://localhost:8000/app"
        self.api_url = "http://localhost:8000"
        
        # System requirements
        self.min_python_version = (3, 8)
        self.required_packages = [
            "fastapi", "uvicorn", "pydantic", "sqlalchemy", 
            "google-generativeai", "psutil", "networkx"
        ]
        
        logger.info("🚀 Agent Lee™ Production Launcher initialized")
    
    def check_system_requirements(self):
        """Check system requirements"""
        logger.info("🔍 Checking system requirements...")
        
        # Check Python version
        python_version = sys.version_info[:2]
        if python_version < self.min_python_version:
            logger.error(f"❌ Python {self.min_python_version[0]}.{self.min_python_version[1]}+ required, found {python_version[0]}.{python_version[1]}")
            return False
        
        logger.info(f"✅ Python {python_version[0]}.{python_version[1]} - Compatible")
        
        # Check platform
        system = platform.system()
        logger.info(f"✅ Platform: {system} {platform.release()}")
        
        # Check available memory
        try:
            import psutil
            memory = psutil.virtual_memory()
            memory_gb = memory.total / (1024**3)
            logger.info(f"✅ Available Memory: {memory_gb:.1f} GB")
            
            if memory_gb < 4:
                logger.warning("⚠️ Low memory detected. Agent Lee may run slowly.")
        except ImportError:
            logger.warning("⚠️ Cannot check memory - psutil not available")
        
        return True
    
    def check_dependencies(self):
        """Check required dependencies"""
        logger.info("📦 Checking dependencies...")
        
        missing_packages = []
        
        for package in self.required_packages:
            try:
                __import__(package.replace("-", "_"))
                logger.info(f"✅ {package}")
            except ImportError:
                missing_packages.append(package)
                logger.warning(f"❌ {package} - Missing")
        
        if missing_packages:
            logger.error(f"❌ Missing packages: {', '.join(missing_packages)}")
            logger.info("💡 Install with: pip install -r backend/requirements.txt")
            return False
        
        logger.info("✅ All dependencies satisfied")
        return True
    
    def setup_environment(self):
        """Setup environment variables and configuration"""
        logger.info("⚙️ Setting up environment...")
        
        # Set default environment variables if not present
        env_defaults = {
            "AGENTLEE_ENV": "production",
            "AGENTLEE_LOG_LEVEL": "INFO",
            "AGENTLEE_HOST": "localhost",
            "AGENTLEE_PORT": "8000",
            "AGENTLEE_WORKERS": "1"
        }
        
        for key, value in env_defaults.items():
            if key not in os.environ:
                os.environ[key] = value
                logger.info(f"✅ Set {key}={value}")
        
        # Check for .env file
        env_file = Path(".env")
        if env_file.exists():
            logger.info("✅ Found .env file")
        else:
            logger.warning("⚠️ No .env file found - using defaults")
        
        return True
    
    def initialize_cognitive_architecture(self):
        """Initialize cognitive architecture components"""
        logger.info("🧠 Initializing Cognitive Architecture...")
        
        try:
            # Create cognitive directories
            cognitive_dirs = [
                "frontend/js/cognitive",
                "frontend/js/agents",
                "frontend/js/workers",
                "backend/services",
                "data/cognitive",
                "logs/cognitive"
            ]
            
            for dir_path in cognitive_dirs:
                Path(dir_path).mkdir(parents=True, exist_ok=True)
                logger.info(f"✅ Created directory: {dir_path}")
            
            # Verify cognitive files exist
            cognitive_files = [
                "frontend/js/cognitive/CognitiveArchitecture.js",
                "frontend/js/cognitive/CognitiveHypergraph.js",
                "frontend/js/cognitive/HematopoieticAgentFactory.js",
                "frontend/js/cognitive/CerebrospinalOptimizationEngine.js",
                "backend/services/cognitive_service.py",
                "backend/services/security_service.py",
                "backend/services/performance_monitor.py"
            ]
            
            missing_files = []
            for file_path in cognitive_files:
                if not Path(file_path).exists():
                    missing_files.append(file_path)
            
            if missing_files:
                logger.warning(f"⚠️ Missing cognitive files: {len(missing_files)}")
                for file_path in missing_files:
                    logger.warning(f"   - {file_path}")
            else:
                logger.info("✅ All cognitive architecture files present")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize cognitive architecture: {e}")
            return False
    
    def start_backend(self):
        """Start the FastAPI backend server"""
        logger.info("🔧 Starting Agent Lee backend...")
        
        try:
            # Change to backend directory
            backend_dir = Path("backend")
            
            # Start uvicorn server
            cmd = [
                sys.executable, "-m", "uvicorn",
                "agentlee_controller_v2:app",
                "--host", os.environ.get("AGENTLEE_HOST", "localhost"),
                "--port", os.environ.get("AGENTLEE_PORT", "8000"),
                "--workers", os.environ.get("AGENTLEE_WORKERS", "1"),
                "--log-level", "info",
                "--access-log"
            ]
            
            logger.info(f"🔧 Command: {' '.join(cmd)}")
            
            self.backend_process = subprocess.Popen(
                cmd,
                cwd=backend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            logger.info("✅ Backend server started")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start backend: {e}")
            return False
    
    def wait_for_backend(self, timeout=30):
        """Wait for backend to be ready"""
        logger.info("⏳ Waiting for backend to be ready...")
        
        import requests
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"{self.api_url}/", timeout=5)
                if response.status_code == 200:
                    logger.info("✅ Backend is ready")
                    return True
            except requests.exceptions.RequestException:
                pass
            
            time.sleep(1)
        
        logger.error("❌ Backend failed to start within timeout")
        return False
    
    def open_frontend(self):
        """Open the frontend in the default browser"""
        logger.info("🌐 Opening Agent Lee frontend...")
        
        try:
            webbrowser.open(self.frontend_url)
            logger.info(f"✅ Frontend opened: {self.frontend_url}")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to open frontend: {e}")
            logger.info(f"💡 Manual access: {self.frontend_url}")
            return False
    
    def display_startup_info(self):
        """Display startup information"""
        uptime = time.time() - self.start_time
        
        print("\n" + "="*60)
        print("🧠 AGENT LEE™ COGNITIVE ARCHITECTURE - PRODUCTION READY")
        print("="*60)
        print(f"🚀 Status: OPERATIONAL")
        print(f"⏱️  Startup Time: {uptime:.2f} seconds")
        print(f"🌐 Frontend: {self.frontend_url}")
        print(f"🔧 API: {self.api_url}")
        print(f"📊 Cognitive Dashboard: {self.api_url}/api/cognitive/metrics")
        print(f"🏥 Health Check: {self.api_url}/api/system_status")
        print(f"📈 Performance: {self.api_url}/api/performance/summary")
        print("="*60)
        print("🧠 COGNITIVE COMPONENTS:")
        print("   • Unified Cognitive Hypergraph")
        print("   • Hematopoietic Agent Factory")
        print("   • Cerebrospinal Optimization Engine")
        print("   • Synaptic Mirroring System")
        print("   • Autonomic Healing Matrix")
        print("   • Temporal Context Engine")
        print("   • Exocortical Integration Mesh")
        print("="*60)
        print("💡 Press Ctrl+C to shutdown gracefully")
        print("="*60)
    
    def monitor_backend(self):
        """Monitor backend process"""
        if not self.backend_process:
            return
        
        try:
            while True:
                # Check if process is still running
                if self.backend_process.poll() is not None:
                    logger.error("❌ Backend process terminated unexpectedly")
                    break
                
                # Read output
                line = self.backend_process.stdout.readline()
                if line:
                    # Filter and log important messages
                    if any(keyword in line.lower() for keyword in ['error', 'warning', 'cognitive', 'started']):
                        logger.info(f"Backend: {line.strip()}")
                
                time.sleep(0.1)
                
        except KeyboardInterrupt:
            logger.info("🛑 Shutdown signal received")
            self.shutdown()
    
    def shutdown(self):
        """Graceful shutdown"""
        logger.info("🛑 Shutting down Agent Lee...")
        
        if self.backend_process:
            logger.info("🔧 Terminating backend process...")
            self.backend_process.terminate()
            
            # Wait for graceful shutdown
            try:
                self.backend_process.wait(timeout=10)
                logger.info("✅ Backend shutdown complete")
            except subprocess.TimeoutExpired:
                logger.warning("⚠️ Force killing backend process...")
                self.backend_process.kill()
        
        uptime = time.time() - self.start_time
        logger.info(f"✅ Agent Lee shutdown complete. Uptime: {uptime:.2f} seconds")
    
    def run(self):
        """Main run method"""
        try:
            logger.info("🚀 Starting Agent Lee™ Production System...")
            
            # System checks
            if not self.check_system_requirements():
                return False
            
            if not self.check_dependencies():
                return False
            
            if not self.setup_environment():
                return False
            
            if not self.initialize_cognitive_architecture():
                return False
            
            # Start services
            if not self.start_backend():
                return False
            
            if not self.wait_for_backend():
                return False
            
            # Open frontend
            self.open_frontend()
            
            # Display info
            self.display_startup_info()
            
            # Monitor
            self.monitor_backend()
            
            return True
            
        except KeyboardInterrupt:
            logger.info("🛑 Interrupted by user")
            self.shutdown()
            return True
        except Exception as e:
            logger.error(f"❌ Startup failed: {e}")
            self.shutdown()
            return False

def main():
    """Main entry point"""
    launcher = AgentLeeProductionLauncher()
    success = launcher.run()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
