# Agent Lee™ Cognitive Architecture Requirements v3.0
# Production-ready dependencies for Agent Lee™ Cognitive System

# --- Core Cognitive Framework ---
fastapi==0.111.0
uvicorn[standard]==0.29.0
pydantic==2.8.2
python-multipart==0.0.9
starlette==0.37.2

# --- Cognitive Database & Hypergraph ---
sqlalchemy==2.0.31
alembic==1.13.2
aiosqlite==0.20.0
networkx==3.2.1
redis==5.0.1
hiredis==2.2.3

# --- AI & LLM Cognitive Integration ---
google-generativeai==0.7.2
openai==1.35.13
anthropic==0.31.1
transformers==4.36.2
torch==2.1.2
sentence-transformers==2.2.2
onnxruntime==1.16.3

# --- Browser & Automation ---
playwright==1.44.0
# Required to install supported browsers after pip install
# Run separately: playwright install
selenium==4.22.0
webdriver-manager==4.0.1

# --- Speech & Audio Recognition ---
pyaudio==0.2.13         # For microphone input
SpeechRecognition==3.10.1
vosk==0.3.45             # Offline speech-to-text
sounddevice==0.4.7      # Alternative audio interface

# --- Voice Output / TTS ---
pyttsx3==2.90            # Offline TTS (required for Agent Lee)
# Optional: more advanced TTS like ElevenLabs via API

# --- System Access / Shell Commands ---
psutil==5.9.8            # System info (required for Agent Lee)
pyautogui==0.9.54        # GUI automation
keyboard==0.13.5         # Keyboard control
mouse==0.7.1             # Mouse control
subprocess32; sys_platform == 'linux'

# --- Web Scraping / Search ---
requests==2.31.0
beautifulsoup4==4.12.3
httpx==0.27.0           # Async HTTP client
lxml==5.2.2             # XML/HTML parser
# serpapi==0.1.4          # Optional: Google search API wrapper (requires API key)

# --- Email & Communication (Agent Lee Features) ---
email-validator==2.3.0
yagmail==0.15.293       # Simple email sending
exchangelib==5.2.0      # Microsoft Exchange integration

# --- File & Document Processing ---
python-docx==1.1.2      # Word documents
PyPDF2==3.0.1           # PDF processing
openpyxl==3.1.5         # Excel files
pillow==10.4.0          # Image processing
python-magic==0.4.27    # File type detection

# --- Utility Libraries ---
python-dotenv==1.0.1     # For .env file loading
aiofiles==23.2.1         # For FastAPI file handling
schedule==1.2.2          # Task scheduling
watchdog==4.0.1          # File system monitoring
colorama==0.4.6          # Colored terminal output
rich==13.7.1             # Rich text and beautiful formatting
click==8.1.7             # Command line interface
loguru==0.7.2            # Advanced logging

# --- Security & Encryption ---
cryptography==42.0.8
bcrypt==4.1.3
passlib[bcrypt]==1.7.4

# --- Async & Concurrency ---
asyncio==3.4.3; python_version < "3.7"
concurrent-futures==3.1.1; python_version < "3.2"

# --- Data Processing ---
pandas==2.2.2           # Data analysis
jsonschema==4.23.0      # JSON validation
python-dateutil==2.9.0  # Date utilities
numpy==1.26.4           # Numerical computing

# --- Development & Testing ---
pytest==8.2.2
pytest-asyncio==0.23.7
pytest-mock==3.14.0

# --- Windows-Specific Dependencies ---
pywin32==306; sys_platform == "win32"
winsound; sys_platform == "win32"

# --- macOS-Specific Dependencies ---
# pyobjc-framework-Cocoa==10.3.1; sys_platform == "darwin"

# --- Optional Advanced AI Features ---
# transformers==4.42.3    # Hugging Face transformers
# torch==2.3.1           # PyTorch for ML
# tensorflow==2.16.2     # TensorFlow for ML
