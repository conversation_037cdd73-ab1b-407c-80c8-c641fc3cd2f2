/**
 * Agent Lee™ Hematopoietic Agent Factory
 * Dynamic Agent Creation, Specialization, and Lifecycle Management
 * 
 * Like stem cells in the body, this factory creates, specializes, and manages
 * the lifecycle of software agents based on system needs and load patterns.
 */

class HematopoieticAgentFactory {
    constructor(cognitiveHypergraph) {
        this.cognitiveHypergraph = cognitiveHypergraph;
        this.activeAgents = new Map(); // instance_id -> AgentInstance
        this.agentTemplates = new Map(); // template_id -> AgentTemplate
        this.workerPool = new Map(); // worker_id -> Worker
        this.systemMetrics = new SystemMetrics();
        this.isInitialized = false;
        
        // Factory configuration
        this.config = {
            maxAgents: 125,
            maxWorkers: 200,
            maxServiceWorkers: 40,
            maxSupervisorWorkers: 15,
            spawnThreshold: 0.8, // CPU/memory threshold for spawning
            retireThreshold: 0.3, // Threshold for retiring agents
            heartbeatInterval: 5000, // 5 seconds
            optimizationInterval: 30000 // 30 seconds
        };
        
        this.initializeFactory();
    }

    async initializeFactory() {
        console.log('🧬 Initializing Hematopoietic Agent Factory...');
        
        try {
            // Load agent templates
            await this.loadAgentTemplates();
            
            // Initialize system monitoring
            this.systemMetrics.startMonitoring();
            
            // Start autonomic processes
            this.startAutonomicProcesses();
            
            // Spawn initial agent population
            await this.spawnInitialAgents();
            
            this.isInitialized = true;
            console.log('✅ Hematopoietic Agent Factory initialized successfully');
            
        } catch (error) {
            console.error('❌ Failed to initialize Agent Factory:', error);
            throw error;
        }
    }

    async loadAgentTemplates() {
        console.log('📋 Loading agent templates...');
        
        // Core agent templates (genotypes)
        const coreTemplates = [
            {
                id: 'memory_consolidator',
                type: 'MemoryConsolidatorAgent',
                description: 'Consolidates and organizes memories in the cognitive hypergraph',
                resourceProfile: { cpu: 0.2, memory: 50, priority: 'medium' },
                complexityScore: 0.6,
                dependencies: ['cognitive_hypergraph', 'vector_store'],
                scriptUrl: '/js/agents/MemoryConsolidatorAgent.js'
            },
            {
                id: 'task_dispatcher',
                type: 'TaskDispatcherAgent',
                description: 'Distributes tasks across available agents and workers',
                resourceProfile: { cpu: 0.3, memory: 30, priority: 'high' },
                complexityScore: 0.8,
                dependencies: ['task_queue', 'agent_registry'],
                scriptUrl: '/js/agents/TaskDispatcherAgent.js'
            },
            {
                id: 'echo_session_manager',
                type: 'EchoSessionManagerAgent',
                description: 'Manages Echo Engine sessions and UI mirroring',
                resourceProfile: { cpu: 0.4, memory: 100, priority: 'high' },
                complexityScore: 0.9,
                dependencies: ['desktop_capturer', 'webrtc'],
                scriptUrl: '/js/agents/EchoSessionManagerAgent.js'
            },
            {
                id: 'semantic_recall',
                type: 'SemanticRecallAgent',
                description: 'Performs semantic search and memory retrieval',
                resourceProfile: { cpu: 0.3, memory: 80, priority: 'medium' },
                complexityScore: 0.7,
                dependencies: ['vector_index', 'cognitive_hypergraph'],
                scriptUrl: '/js/agents/SemanticRecallAgent.js'
            },
            {
                id: 'optimization_particle',
                type: 'OptimizationParticleAgent',
                description: 'Explores solution space for system optimization',
                resourceProfile: { cpu: 0.5, memory: 40, priority: 'low' },
                complexityScore: 0.8,
                dependencies: ['performance_metrics', 'system_monitor'],
                scriptUrl: '/js/agents/OptimizationParticleAgent.js'
            },
            {
                id: 'integrity_enforcer',
                type: 'IntegrityEnforcerAgent',
                description: 'Monitors and maintains data integrity',
                resourceProfile: { cpu: 0.2, memory: 30, priority: 'medium' },
                complexityScore: 0.5,
                dependencies: ['cognitive_hypergraph', 'checksum_validator'],
                scriptUrl: '/js/agents/IntegrityEnforcerAgent.js'
            },
            {
                id: 'temporal_context',
                type: 'TemporalContextAgent',
                description: 'Manages temporal context and predictive activation',
                resourceProfile: { cpu: 0.3, memory: 60, priority: 'medium' },
                complexityScore: 0.7,
                dependencies: ['cognitive_hypergraph', 'time_series_analyzer'],
                scriptUrl: '/js/agents/TemporalContextAgent.js'
            },
            {
                id: 'device_sync',
                type: 'DeviceSyncAgent',
                description: 'Synchronizes state across multiple devices',
                resourceProfile: { cpu: 0.4, memory: 70, priority: 'high' },
                complexityScore: 0.8,
                dependencies: ['websocket_client', 'crdt_manager'],
                scriptUrl: '/js/agents/DeviceSyncAgent.js'
            }
        ];

        // Load templates into memory
        coreTemplates.forEach(template => {
            this.agentTemplates.set(template.id, new AgentTemplate(template));
        });

        console.log(`📋 Loaded ${coreTemplates.length} agent templates`);
    }

    /**
     * Spawns a new agent instance from a template
     * @param {string} templateId - Template identifier
     * @param {Object} epigenome - Runtime configuration
     * @returns {string} - Instance ID
     */
    async spawnAgent(templateId, epigenome = {}) {
        const template = this.agentTemplates.get(templateId);
        if (!template) {
            throw new Error(`Agent template not found: ${templateId}`);
        }

        // Check resource availability
        const resourcesAvailable = await this.checkResourceAvailability(template.resourceProfile);
        if (!resourcesAvailable) {
            console.warn(`⚠️ Insufficient resources to spawn ${templateId}`);
            return null;
        }

        const instanceId = this.generateInstanceId();
        const instance = new AgentInstance({
            id: instanceId,
            templateId: templateId,
            template: template,
            epigenome: epigenome,
            spawnedAt: Date.now(),
            status: 'initializing'
        });

        try {
            // Create worker or class instance based on template type
            if (template.requiresWorker) {
                instance.worker = new Worker(template.scriptUrl);
                instance.worker.onmessage = (event) => this.handleWorkerMessage(instanceId, event);
                instance.worker.onerror = (error) => this.handleWorkerError(instanceId, error);
            } else {
                // Load and instantiate agent class
                const AgentClass = await this.loadAgentClass(template.scriptUrl);
                instance.agentInstance = new AgentClass(epigenome, this.cognitiveHypergraph);
            }

            // Apply epigenome configuration
            await this.applyEpigenome(instance, epigenome);

            // Register instance
            this.activeAgents.set(instanceId, instance);
            instance.status = 'active';

            console.log(`🧬 Spawned agent: ${instanceId} (${templateId})`);
            
            // Update cognitive hypergraph
            this.cognitiveHypergraph.createNode('agent_spawn', {
                instanceId: instanceId,
                templateId: templateId,
                epigenome: epigenome,
                timestamp: Date.now()
            });

            return instanceId;

        } catch (error) {
            console.error(`❌ Failed to spawn agent ${templateId}:`, error);
            instance.status = 'failed';
            return null;
        }
    }

    /**
     * Retires an agent instance (apoptosis)
     * @param {string} instanceId - Instance to retire
     * @param {string} reason - Reason for retirement
     */
    async retireAgent(instanceId, reason = 'lifecycle_complete') {
        const instance = this.activeAgents.get(instanceId);
        if (!instance) {
            console.warn(`Agent instance not found: ${instanceId}`);
            return;
        }

        console.log(`💀 Retiring agent: ${instanceId} (${reason})`);

        try {
            // Allow agent to dump memories (exosome release)
            await this.performExosomeRelease(instance);

            // Gracefully shutdown
            if (instance.worker) {
                instance.worker.terminate();
            } else if (instance.agentInstance && instance.agentInstance.shutdown) {
                await instance.agentInstance.shutdown();
            }

            // Remove from active registry
            this.activeAgents.delete(instanceId);
            instance.status = 'retired';

            // Update cognitive hypergraph
            this.cognitiveHypergraph.createNode('agent_retirement', {
                instanceId: instanceId,
                reason: reason,
                lifespan: Date.now() - instance.spawnedAt,
                timestamp: Date.now()
            });

        } catch (error) {
            console.error(`❌ Error retiring agent ${instanceId}:`, error);
        }
    }

    /**
     * Monitors system load and decides on agent lifecycle actions
     */
    async performMitosisAndApoptosis() {
        const metrics = await this.systemMetrics.getCurrentMetrics();
        const activeCount = this.activeAgents.size;

        // Mitosis - spawn new agents if needed
        if (metrics.cpuUsage > this.config.spawnThreshold && activeCount < this.config.maxAgents) {
            const neededAgents = this.analyzeAgentNeeds(metrics);
            
            for (const agentType of neededAgents) {
                await this.spawnAgent(agentType.templateId, agentType.epigenome);
            }
        }

        // Apoptosis - retire underperforming or idle agents
        if (metrics.memoryPressure > 0.8 || activeCount > this.config.maxAgents * 0.9) {
            const candidatesForRetirement = this.identifyRetirementCandidates();
            
            for (const candidate of candidatesForRetirement) {
                await this.retireAgent(candidate.instanceId, 'resource_optimization');
            }
        }
    }

    /**
     * Analyzes system needs and determines which agents to spawn
     */
    analyzeAgentNeeds(metrics) {
        const needs = [];

        // High task queue - need more task dispatchers
        if (metrics.taskQueueLength > 50) {
            needs.push({
                templateId: 'task_dispatcher',
                epigenome: { priority: 'high', maxTasks: 20 }
            });
        }

        // High memory fragmentation - need memory consolidators
        if (metrics.memoryFragmentation > 0.7) {
            needs.push({
                templateId: 'memory_consolidator',
                epigenome: { aggressiveness: 'high' }
            });
        }

        // Active Echo sessions - need session managers
        if (metrics.activeEchoSessions > 0) {
            needs.push({
                templateId: 'echo_session_manager',
                epigenome: { sessionId: metrics.primaryEchoSession }
            });
        }

        return needs;
    }

    /**
     * Identifies agents that are candidates for retirement
     */
    identifyRetirementCandidates() {
        const candidates = [];
        const currentTime = Date.now();

        for (const [instanceId, instance] of this.activeAgents) {
            const age = currentTime - instance.spawnedAt;
            const idleTime = currentTime - instance.lastActivity;

            // Retire if idle for too long
            if (idleTime > 300000) { // 5 minutes
                candidates.push({ instanceId, reason: 'idle_timeout', priority: 1 });
            }

            // Retire if old and low performance
            if (age > 3600000 && instance.performanceScore < 0.3) { // 1 hour old, low performance
                candidates.push({ instanceId, reason: 'poor_performance', priority: 2 });
            }

            // Retire if resource usage is too high
            if (instance.resourceUsage && instance.resourceUsage.memory > 200) { // 200MB
                candidates.push({ instanceId, reason: 'resource_excessive', priority: 3 });
            }
        }

        // Sort by priority (higher priority = retire first)
        return candidates.sort((a, b) => b.priority - a.priority);
    }

    /**
     * Allows agent to release important memories before retirement
     */
    async performExosomeRelease(instance) {
        console.log(`🧬 Performing exosome release for ${instance.id}...`);

        try {
            if (instance.agentInstance && instance.agentInstance.releaseExosomes) {
                const exosomes = await instance.agentInstance.releaseExosomes();
                
                // Store exosomes in cognitive hypergraph
                for (const exosome of exosomes) {
                    this.cognitiveHypergraph.createNode('exosome', {
                        sourceAgent: instance.id,
                        data: exosome,
                        timestamp: Date.now()
                    });
                }
                
                console.log(`🧬 Released ${exosomes.length} exosomes from ${instance.id}`);
            }
        } catch (error) {
            console.error(`❌ Error during exosome release for ${instance.id}:`, error);
        }
    }

    /**
     * Starts autonomic processes for agent lifecycle management
     */
    startAutonomicProcesses() {
        // Heartbeat monitoring
        setInterval(() => this.performHeartbeatCheck(), this.config.heartbeatInterval);
        
        // Mitosis and apoptosis
        setInterval(() => this.performMitosisAndApoptosis(), this.config.optimizationInterval);
        
        // Performance monitoring
        setInterval(() => this.updateAgentPerformanceMetrics(), 15000);
        
        console.log('🔄 Agent factory autonomic processes started');
    }

    async performHeartbeatCheck() {
        const currentTime = Date.now();
        
        for (const [instanceId, instance] of this.activeAgents) {
            const timeSinceHeartbeat = currentTime - (instance.lastHeartbeat || instance.spawnedAt);
            
            if (timeSinceHeartbeat > 30000) { // 30 seconds without heartbeat
                console.warn(`💔 Agent ${instanceId} missed heartbeat, investigating...`);
                
                if (timeSinceHeartbeat > 60000) { // 1 minute - consider dead
                    await this.retireAgent(instanceId, 'heartbeat_failure');
                }
            }
        }
    }

    async updateAgentPerformanceMetrics() {
        for (const [instanceId, instance] of this.activeAgents) {
            if (instance.agentInstance && instance.agentInstance.getPerformanceMetrics) {
                try {
                    const metrics = await instance.agentInstance.getPerformanceMetrics();
                    instance.performanceScore = this.calculatePerformanceScore(metrics);
                    instance.lastActivity = Date.now();
                } catch (error) {
                    console.warn(`⚠️ Failed to get performance metrics for ${instanceId}:`, error);
                }
            }
        }
    }

    calculatePerformanceScore(metrics) {
        // Weighted performance score calculation
        const weights = {
            taskCompletionRate: 0.4,
            responseTime: 0.3,
            resourceEfficiency: 0.2,
            errorRate: 0.1
        };

        let score = 0;
        score += (metrics.taskCompletionRate || 0) * weights.taskCompletionRate;
        score += (1 - (metrics.averageResponseTime || 1000) / 5000) * weights.responseTime; // Normalize to 5s max
        score += (metrics.resourceEfficiency || 0) * weights.resourceEfficiency;
        score += (1 - (metrics.errorRate || 0)) * weights.errorRate;

        return Math.max(0, Math.min(1, score));
    }

    // Utility methods
    generateInstanceId() {
        return `agent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    async checkResourceAvailability(resourceProfile) {
        const metrics = await this.systemMetrics.getCurrentMetrics();
        
        return (
            metrics.cpuUsage + resourceProfile.cpu < 0.9 &&
            metrics.memoryUsage + resourceProfile.memory < 0.8
        );
    }

    async loadAgentClass(scriptUrl) {
        // Dynamic import of agent class
        const module = await import(scriptUrl);
        return module.default || module;
    }

    async applyEpigenome(instance, epigenome) {
        // Apply runtime configuration to agent instance
        if (instance.worker) {
            instance.worker.postMessage({
                type: 'configure',
                epigenome: epigenome
            });
        } else if (instance.agentInstance && instance.agentInstance.configure) {
            await instance.agentInstance.configure(epigenome);
        }
    }

    handleWorkerMessage(instanceId, event) {
        const instance = this.activeAgents.get(instanceId);
        if (!instance) return;

        const { type, data } = event.data;

        switch (type) {
            case 'heartbeat':
                instance.lastHeartbeat = Date.now();
                break;
            case 'performance_metrics':
                instance.performanceScore = this.calculatePerformanceScore(data);
                break;
            case 'task_complete':
                instance.lastActivity = Date.now();
                break;
            default:
                console.log(`📨 Message from ${instanceId}:`, event.data);
        }
    }

    handleWorkerError(instanceId, error) {
        console.error(`❌ Worker error in ${instanceId}:`, error);
        this.retireAgent(instanceId, 'worker_error');
    }
}

/**
 * Agent Template - Genotype for agent creation
 */
class AgentTemplate {
    constructor(config) {
        this.id = config.id;
        this.type = config.type;
        this.description = config.description;
        this.resourceProfile = config.resourceProfile;
        this.complexityScore = config.complexityScore;
        this.dependencies = config.dependencies || [];
        this.scriptUrl = config.scriptUrl;
        this.requiresWorker = config.requiresWorker || false;
    }
}

/**
 * Agent Instance - Active agent with runtime state
 */
class AgentInstance {
    constructor(config) {
        this.id = config.id;
        this.templateId = config.templateId;
        this.template = config.template;
        this.epigenome = config.epigenome;
        this.spawnedAt = config.spawnedAt;
        this.status = config.status;
        this.worker = null;
        this.agentInstance = null;
        this.lastHeartbeat = Date.now();
        this.lastActivity = Date.now();
        this.performanceScore = 1.0;
        this.resourceUsage = { cpu: 0, memory: 0 };
    }
}

/**
 * System Metrics - Monitors system performance
 */
class SystemMetrics {
    constructor() {
        this.metrics = {
            cpuUsage: 0,
            memoryUsage: 0,
            memoryPressure: 0,
            memoryFragmentation: 0,
            taskQueueLength: 0,
            activeEchoSessions: 0,
            primaryEchoSession: null
        };
    }

    startMonitoring() {
        setInterval(() => this.updateMetrics(), 5000);
    }

    async updateMetrics() {
        // Update system metrics
        this.metrics.cpuUsage = await this.getCPUUsage();
        this.metrics.memoryUsage = await this.getMemoryUsage();
        this.metrics.memoryPressure = await this.getMemoryPressure();
        this.metrics.taskQueueLength = await this.getTaskQueueLength();
    }

    async getCurrentMetrics() {
        return { ...this.metrics };
    }

    async getCPUUsage() {
        // Estimate CPU usage based on active agents and tasks
        return Math.random() * 0.8; // Placeholder
    }

    async getMemoryUsage() {
        // Get memory usage from performance API
        if (performance.memory) {
            return performance.memory.usedJSHeapSize / performance.memory.totalJSHeapSize;
        }
        return Math.random() * 0.6; // Placeholder
    }

    async getMemoryPressure() {
        // Calculate memory pressure
        return Math.random() * 0.4; // Placeholder
    }

    async getTaskQueueLength() {
        // Get current task queue length
        return Math.floor(Math.random() * 100); // Placeholder
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { HematopoieticAgentFactory, AgentTemplate, AgentInstance, SystemMetrics };
}
