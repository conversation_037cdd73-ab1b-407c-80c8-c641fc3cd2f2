<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Lee™ Echo Engine</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: transparent;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
        }

        /* Agent <PERSON> Main Card */
        .agent-card {
            position: fixed;
            top: 50px;
            right: 50px;
            width: 300px;
            height: 500px;
            background: linear-gradient(145deg, #1e293b 0%, #0f172a 100%);
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.8), 0 0 0 2px rgba(59, 130, 246, 0.3);
            border: 1px solid rgba(59, 130, 246, 0.2);
            z-index: 999999;
            cursor: move;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* Minimized State */
        .agent-card.minimized {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            cursor: grab;
        }

        .agent-card.minimized .card-content {
            display: none;
        }

        .agent-card.minimized .minimized-bubble {
            display: flex;
        }

        /* Minimized Bubble */
        .minimized-bubble {
            display: none;
            width: 100%;
            height: 100%;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            border-radius: 50%;
            position: relative;
        }

        .bubble-face {
            font-size: 24px;
            animation: blink 3s infinite;
        }

        @keyframes blink {
            0%, 90%, 100% { transform: scale(1); }
            95% { transform: scale(0.9); }
        }

        /* Card Header */
        .card-header {
            background: linear-gradient(135deg, #1e40af 0%, #3730a3 50%, #374151 100%);
            padding: 10px 14px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: grab;
            border-bottom: 1px solid rgba(59, 130, 246, 0.3);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .agent-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.4);
            border: 2px solid rgba(255,255,255,0.2);
        }

        .agent-face {
            font-size: 16px;
            animation: blink 3s infinite;
        }

        .agent-info {
            color: white;
        }

        .agent-name {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 1px;
        }

        .agent-status {
            font-size: 8px;
            color: rgba(255,255,255,0.8);
        }

        /* Header Controls */
        .header-controls {
            display: flex;
            gap: 4px;
        }

        .control-btn {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            font-size: 8px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .minimize-btn { background: #fbbf24; }
        .close-btn { background: #ef4444; }

        .control-btn:hover {
            transform: scale(1.3);
        }

        /* Card Content */
        .card-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 10px;
            gap: 8px;
            overflow-y: auto;
        }

        /* Echo Engine Section */
        .echo-engine {
            background: rgba(30, 41, 59, 0.6);
            border-radius: 6px;
            padding: 8px;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .echo-title {
            color: white;
            font-size: 10px;
            font-weight: bold;
            margin-bottom: 6px;
            text-align: center;
        }

        .device-tabs {
            display: flex;
            gap: 3px;
            margin-bottom: 6px;
            justify-content: center;
        }

        .device-tab {
            background: rgba(59, 130, 246, 0.2);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 3px;
            padding: 3px 6px;
            color: white;
            font-size: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .device-tab.active {
            background: rgba(59, 130, 246, 0.5);
        }

        .app-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 4px;
        }

        .app-tile {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 4px;
            padding: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;
            text-align: center;
        }

        .app-tile:hover {
            background: rgba(59, 130, 246, 0.3);
            transform: translateY(-1px);
        }

        .app-icon {
            font-size: 10px;
        }

        .app-name {
            font-size: 7px;
            color: white;
            font-weight: 500;
        }

        /* Chat Section */
        .chat-section {
            flex: 1;
            background: rgba(30, 41, 59, 0.6);
            border-radius: 6px;
            padding: 6px;
            border: 1px solid rgba(59, 130, 246, 0.2);
            display: flex;
            flex-direction: column;
            min-height: 100px;
        }

        .chat-area {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 6px;
        }

        .message {
            margin-bottom: 4px;
            padding: 4px 6px;
            border-radius: 4px;
            font-size: 9px;
            line-height: 1.3;
        }

        .agent-message {
            background: rgba(59, 130, 246, 0.2);
            color: #e2e8f0;
            border-left: 2px solid #3b82f6;
        }

        .user-message {
            background: rgba(16, 185, 129, 0.2);
            color: #e2e8f0;
            border-left: 2px solid #10b981;
            text-align: right;
        }

        .input-area {
            display: flex;
            gap: 4px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 4px;
            padding: 4px;
            color: white;
            font-size: 9px;
            outline: none;
        }

        .input-btn {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            font-size: 10px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .voice-btn {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
        }

        .send-btn {
            background: linear-gradient(135deg, #3b82f6, #1e40af);
        }

        .input-btn:hover {
            transform: scale(1.1);
        }

        .input-btn.active {
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* Echo Mirror Cards */
        .echo-mirror {
            position: fixed;
            width: 400px;
            height: 300px;
            background: linear-gradient(145deg, #1e293b 0%, #0f172a 100%);
            border-radius: 12px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.8), 0 0 0 2px rgba(59, 130, 246, 0.3);
            border: 1px solid rgba(59, 130, 246, 0.2);
            z-index: 999998;
            cursor: move;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .echo-header {
            background: linear-gradient(135deg, #1e40af 0%, #3730a3 50%, #374151 100%);
            padding: 8px 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: grab;
            border-bottom: 1px solid rgba(59, 130, 246, 0.3);
        }

        .echo-title {
            color: white;
            font-size: 11px;
            font-weight: bold;
        }

        .echo-content {
            flex: 1;
            position: relative;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .echo-video {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .echo-placeholder {
            color: white;
            text-align: center;
            font-size: 11px;
            padding: 20px;
        }

        /* Status Bar */
        .status-bar {
            background: rgba(15, 23, 42, 0.8);
            padding: 4px 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 8px;
            color: rgba(255, 255, 255, 0.7);
            border-top: 1px solid rgba(59, 130, 246, 0.2);
        }

        .listening-indicator {
            display: none;
            color: #ef4444;
            animation: pulse 1s infinite;
        }

        .listening-indicator.active {
            display: inline;
        }
    </style>
</head>
<body>
    <!-- Agent Lee Main Card -->
    <div class="agent-card" id="agentCard">
        <!-- Minimized Bubble -->
        <div class="minimized-bubble" id="minimizedBubble">
            <div class="bubble-face">🤖</div>
        </div>

        <!-- Full Card Content -->
        <div class="card-content" id="cardContent">
            <!-- Header -->
            <div class="card-header" id="cardHeader">
                <div class="header-left">
                    <div class="agent-avatar">
                        <div class="agent-face" id="agentFace">🤖</div>
                    </div>
                    <div class="agent-info">
                        <div class="agent-name">Agent Lee™</div>
                        <div class="agent-status" id="agentStatus">Echo Engine Ready</div>
                    </div>
                </div>
                
                <div class="header-controls">
                    <button class="control-btn minimize-btn" id="minimizeBtn" title="Minimize">−</button>
                    <button class="control-btn close-btn" id="closeBtn" title="Close">×</button>
                </div>
            </div>

            <!-- Chat Section -->
            <div class="chat-section">
                <div class="chat-area" id="chatArea">
                    <div class="message agent-message">
                        Hello! I'm Agent Lee with Echo Engine. Click any app to mirror it in a floating window!
                    </div>
                </div>
                
                <div class="input-area">
                    <input type="text" class="message-input" id="messageInput" placeholder="Type to Agent Lee...">
                    <button class="input-btn voice-btn" id="voiceBtn" title="Voice">🎤</button>
                    <button class="input-btn send-btn" id="sendBtn" title="Send">➤</button>
                </div>
            </div>

            <!-- Echo Engine -->
            <div class="echo-engine">
                <div class="echo-title">🔄 Echo Engine - Mirror Apps</div>
                
                <div class="device-tabs">
                    <div class="device-tab active" data-device="computer">💻</div>
                    <div class="device-tab" data-device="phone">📱</div>
                    <div class="device-tab" data-device="tablet">📱</div>
                    <div class="device-tab" data-device="laptop">💻</div>
                </div>
                
                <div class="app-grid" id="appGrid">
                    <!-- Apps populated by JavaScript -->
                </div>
            </div>

            <!-- Status Bar -->
            <div class="status-bar">
                <span id="connectionStatus">🟢 Echo Ready</span>
                <span class="listening-indicator" id="listeningIndicator">🎤</span>
                <span id="timeStatus"></span>
            </div>
        </div>
    </div>

    <script src="echo_engine_real.js"></script>
</body>
</html>
